{"title": "Режимы", "done": "Готово", "modes": {"title": "Режимы", "createNewMode": "Создать новый режим", "importMode": "Импортировать режим", "noMatchFound": "Режимы не найдены", "editModesConfig": "Редактировать конфигурацию режимов", "editGlobalModes": "Редактировать глобальные режимы", "editProjectModes": "Редактировать режимы проекта (.kilocodemodes)", "createModeHelpText": "Режимы — это специализированные персоны, которые адаптируют поведение Kilo Code. <0>Узнайте об использовании режимов</0> или <1>настройке режимов.</1>", "selectMode": "Поиск режимов"}, "apiConfiguration": {"title": "Конфигурация API", "select": "Выберите, какую конфигурацию API использовать для этого режима"}, "tools": {"title": "Доступные инструменты", "builtInModesText": "Инструменты для встроенных режимов нельзя изменять", "editTools": "Редактировать инструменты", "doneEditing": "Завершить редактирование", "allowedFiles": "Разрешённые файлы:", "toolNames": {"read": "Чтение файлов", "edit": "Редактирование файлов", "browser": "Использовать браузер", "command": "Выполнять команды", "mcp": "Использовать MCP"}, "noTools": "Отсутствуют"}, "roleDefinition": {"title": "Определение роли", "resetToDefault": "Сбросить по умолчанию", "description": "Определите экспертность и личность Kilo Code для этого режима. Это описание формирует, как Kilo Code будет себя вести и выполнять задачи."}, "description": {"title": "Краткое описание (для людей)", "resetToDefault": "Сбросить до описания по умолчанию", "description": "Краткое описание, отображаемое в выпадающем списке выбора режима."}, "whenToUse": {"title": "Когда использовать (необязательно)", "description": "Опишите, когда следует использовать этот режим. Это помогает Orchestrator выбрать правильный режим для задачи.", "resetToDefault": "Сбросить описание 'Когда использовать' по умолчанию"}, "customInstructions": {"title": "Пользовательские инструкции для режима (необязательно)", "resetToDefault": "Сбросить по умолчанию", "description": "Добавьте рекомендации по поведению, специфичные для режима {{modeName}}.", "loadFromFile": "Пользовательские инструкции для режима {{mode}} также можно загрузить из папки <span>.kilocode/rules/</span> в вашем рабочем пространстве (.kilocoderules-{{slug}} устарели и скоро перестанут работать)."}, "exportMode": {"title": "Экспортировать режим", "description": "Экспортирует этот режим с правилами из папки .kilocode/rules-{{slug}}/, объединенными в общий YAML файл. Исходные файлы остаются без изменений.", "exporting": "Экспорт..."}, "globalCustomInstructions": {"title": "Пользовательские инструкции для всех режимов", "description": "Эти инструкции применяются ко всем режимам. Они задают базовое поведение, которое можно расширить с помощью инструкций ниже. <0>Узнать больше</0>", "loadFromFile": "Инструкции также можно загрузить из папки <span>.kilocode/rules/</span> в вашем рабочем пространстве (.kilocoderules устарели и скоро перестанут работать)."}, "importMode": {"selectLevel": "Выберите, куда импортировать этот режим:", "import": "Импорт", "importing": "Импортирование...", "global": {"label": "Глобальный уровень", "description": "Доступно во всех проектах. Если экспортированный режим содержал файлы правил, они будут воссозданы в глобальной папке .kilocode/rules-{slug}/."}, "project": {"label": "Уровень проекта", "description": "Доступно только в этом рабочем пространстве. Если экспортированный режим содержал файлы правил, они будут воссозданы в папке .kilocode/rules-{slug}/."}}, "systemPrompt": {"preview": "Предпросмотр системного промпта", "copy": "Скопировать системный промпт в буфер обмена", "title": "Системный промпт (режим {{modeName}})"}, "supportPrompts": {"title": "Вспомогательные промпты", "resetPrompt": "Сбросить промпт {{promptType}} по умолчанию", "prompt": "Промпт", "enhance": {"apiConfiguration": "Конфигурация API", "apiConfigDescription": "Вы можете выбрать конфигурацию API, которая всегда будет использоваться для улучшения промптов, или использовать текущую выбранную", "useCurrentConfig": "Использовать текущую конфигурацию API", "testPromptPlaceholder": "Введите промпт для тестирования улучшения", "previewButton": "Просмотреть улучшенный промпт", "testEnhancement": "Тестировать улучшение"}, "types": {"ENHANCE": {"label": "Улучшить промпт", "description": "Используйте улучшение промпта для получения индивидуальных предложений или улучшений ваших запросов. Это гарантирует, что Kilo Code правильно поймет ваш запрос и даст лучший ответ. Доступно через ✨ в чате."}, "EXPLAIN": {"label": "Объяснить код", "description": "Получите подробные объяснения фрагментов кода, функций или целых файлов. Полезно для понимания сложного кода или изучения новых паттернов. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "FIX": {"label": "Исправить ошибки", "description": "Получите помощь в выявлении и исправлении багов, ошибок или проблем с качеством кода. Пошаговое руководство по устранению проблем. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "IMPROVE": {"label": "Улучшить код", "description": "Получите предложения по оптимизации кода, лучшим практикам и архитектурным улучшениям при сохранении функциональности. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "ADD_TO_CONTEXT": {"label": "Добавить в контекст", "description": "Добавьте контекст к текущей задаче или беседе. Полезно для предоставления дополнительной информации или пояснений. Доступно в действиях с кодом (иконка лампочки в редакторе) и в контекстном меню редактора (ПКМ по выделенному коду)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Добавить содержимое терминала в контекст", "description": "Добавьте вывод терминала к текущей задаче или беседе. Полезно для предоставления результатов команд или логов. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "TERMINAL_FIX": {"label": "Исправить команду терминала", "description": "Получите помощь в исправлении команд терминала, которые не сработали или требуют улучшения. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "TERMINAL_EXPLAIN": {"label": "Объяснить команду терминала", "description": "Получите подробные объяснения команд терминала и их вывода. Доступно в контекстном меню терминала (ПКМ по выделенному содержимому терминала)."}, "NEW_TASK": {"label": "Начать новую задачу", "description": "Начать новую задачу с пользовательским вводом. Доступно в палитре команд."}, "COMMIT_MESSAGE": {"label": "Генерация Сообщений Коммита", "description": "Создавайте подробные сообщения коммитов на основе подготовленных изменений в git. Настройте запрос для генерации сообщений, соответствующих лучшим практикам вашего репозитория."}}}, "advancedSystemPrompt": {"title": "Дополнительно: переопределить системный промпт", "description": "<2>⚠️ Внимание:</2> Эта расширенная функция обходит средства защиты. <1>ПРОЧТИТЕ ЭТО ПЕРЕД ИСПОЛЬЗОВАНИЕМ!</1>Переопределите системный промпт по умолчанию, создав файл в <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Создать новый режим", "close": "Закрыть", "name": {"label": "Название", "placeholder": "Введите название режима"}, "slug": {"label": "Слаг", "description": "Слаг используется в URL и именах файлов. Он должен быть в нижнем регистре и содержать только буквы, цифры и дефисы."}, "saveLocation": {"label": "Место сохранения", "description": "Выберите, где сохранить этот режим. Режимы проекта имеют приоритет над глобальными.", "global": {"label": "Глобально", "description": "Доступно во всех рабочих пространствах"}, "project": {"label": "Для проекта (.kilocodemodes)", "description": "Доступно только в этом рабочем пространстве, имеет приоритет над глобальными"}}, "roleDefinition": {"label": "Определение роли", "description": "Определите экспертность и личность Kilo Code для этого режима."}, "whenToUse": {"label": "Когда использовать (необязательно)", "description": "Предоставьте четкое описание, когда этот режим наиболее эффективен и для каких типов задач он лучше всего подходит."}, "tools": {"label": "Доступные инструменты", "description": "Выберите, какие инструменты может использовать этот режим."}, "description": {"label": "Краткое описание (для людей)", "description": "Краткое описание, отображаемое в выпадающем списке выбора режима."}, "customInstructions": {"label": "Пользовательские инструкции (необязательно)", "description": "Добавьте рекомендации по поведению, специфичные для этого режима."}, "buttons": {"cancel": "Отмена", "create": "Создать режим"}, "deleteMode": "Удалить режим"}, "allFiles": "все файлы", "advanced": {"title": "Дополнительно"}, "deleteMode": {"title": "Удалить режим", "message": "Вы уверены, что хотите удалить режим \"{{modeName}}\"?", "rulesFolder": "У этого режима есть папка правил по адресу {{folderPath}}, которая также будет удалена.", "descriptionNoRules": "Вы уверены, что хотите удалить этот пользовательский режим?", "confirm": "Удалить", "cancel": "Отмена"}}