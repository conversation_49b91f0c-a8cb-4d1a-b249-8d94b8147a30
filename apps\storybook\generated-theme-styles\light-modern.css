/* Default Light Modern theme - Generated from VS Code */
--vscode-checkbox-border: #cecece;
--vscode-editor-background: #ffffff;
--vscode-editor-foreground: #3b3b3b;
--vscode-editor-inactiveSelectionBackground: #e5ebf1;
--vscode-editorIndentGuide-background1: #d3d3d3;
--vscode-editorIndentGuide-activeBackground1: #939393;
--vscode-editor-selectionHighlightBackground: #add6ff80;
--vscode-editorSuggestWidget-background: #f8f8f8;
--vscode-activityBarBadge-background: #005fb8;
--vscode-sideBarTitle-foreground: #3b3b3b;
--vscode-list-hoverBackground: #f2f2f2;
--vscode-menu-border: #cecece;
--vscode-input-placeholderForeground: #767676;
--vscode-searchEditor-textInputBorder: #cecece;
--vscode-settings-textInputBorder: #cecece;
--vscode-settings-numberInputBorder: #cecece;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-statusBarItem-remoteBackground: #005fb8;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-sideBarSectionHeader-background: #f8f8f8;
--vscode-sideBarSectionHeader-border: #e5e5e5;
--vscode-tab-selectedForeground: #333333b3;
--vscode-tab-selectedBackground: #ffffffa5;
--vscode-tab-lastPinnedBorder: #d4d4d4;
--vscode-notebook-cellBorderColor: #e5e5e5;
--vscode-notebook-selectedCellBackground: #c8ddf150;
--vscode-statusBarItem-errorBackground: #c72e0f;
--vscode-list-activeSelectionIconForeground: #000000;
--vscode-list-focusAndSelectionOutline: #005fb8;
--vscode-terminal-inactiveSelectionBackground: #e5ebf1;
--vscode-widget-border: #e5e5e5;
--vscode-actionBar-toggledBackground: #dddddd;
--vscode-diffEditor-unchangedRegionBackground: #f8f8f8;
--vscode-activityBar-activeBorder: #005fb8;
--vscode-activityBar-background: #f8f8f8;
--vscode-activityBar-border: #e5e5e5;
--vscode-activityBar-foreground: #1f1f1f;
--vscode-activityBar-inactiveForeground: #616161;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #cccccc;
--vscode-badge-foreground: #3b3b3b;
--vscode-button-background: #005fb8;
--vscode-button-border: #0000001a;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #0258a8;
--vscode-button-secondaryBackground: #e5e5e5;
--vscode-button-secondaryForeground: #3b3b3b;
--vscode-button-secondaryHoverBackground: #cccccc;
--vscode-chat-slashCommandBackground: #adceff7a;
--vscode-chat-slashCommandForeground: #26569e;
--vscode-chat-editedFileForeground: #895503;
--vscode-checkbox-background: #f8f8f8;
--vscode-descriptionForeground: #3b3b3b;
--vscode-dropdown-background: #ffffff;
--vscode-dropdown-border: #cecece;
--vscode-dropdown-foreground: #3b3b3b;
--vscode-dropdown-listBackground: #ffffff;
--vscode-editorGroup-border: #e5e5e5;
--vscode-editorGroupHeader-tabsBackground: #f8f8f8;
--vscode-editorGroupHeader-tabsBorder: #e5e5e5;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #005fb8;
--vscode-editorLineNumber-activeForeground: #171184;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #e5e5e5;
--vscode-editorWidget-background: #f8f8f8;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #005fb8;
--vscode-foreground: #3b3b3b;
--vscode-icon-foreground: #3b3b3b;
--vscode-input-background: #ffffff;
--vscode-input-border: #cecece;
--vscode-input-foreground: #3b3b3b;
--vscode-inputOption-activeBackground: #bed6ed;
--vscode-inputOption-activeBorder: #005fb8;
--vscode-inputOption-activeForeground: #000000;
--vscode-keybindingLabel-foreground: #3b3b3b;
--vscode-list-activeSelectionBackground: #e8e8e8;
--vscode-list-activeSelectionForeground: #000000;
--vscode-menu-selectionBackground: #005fb8;
--vscode-menu-selectionForeground: #ffffff;
--vscode-notificationCenterHeader-background: #ffffff;
--vscode-notificationCenterHeader-foreground: #3b3b3b;
--vscode-notifications-background: #ffffff;
--vscode-notifications-border: #e5e5e5;
--vscode-notifications-foreground: #3b3b3b;
--vscode-panel-background: #f8f8f8;
--vscode-panel-border: #e5e5e5;
--vscode-panelInput-border: #e5e5e5;
--vscode-panelTitle-activeBorder: #005fb8;
--vscode-panelTitle-activeForeground: #3b3b3b;
--vscode-panelTitle-inactiveForeground: #3b3b3b;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #ffffff;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #e5e5e5;
--vscode-pickerGroup-foreground: #8b949e;
--vscode-progressBar-background: #005fb8;
--vscode-quickInput-background: #f8f8f8;
--vscode-quickInput-foreground: #3b3b3b;
--vscode-settings-dropdownBackground: #ffffff;
--vscode-settings-dropdownBorder: #cecece;
--vscode-settings-headerForeground: #1f1f1f;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-sideBar-background: #f8f8f8;
--vscode-sideBar-border: #e5e5e5;
--vscode-sideBar-foreground: #3b3b3b;
--vscode-sideBarSectionHeader-foreground: #3b3b3b;
--vscode-statusBar-background: #f8f8f8;
--vscode-statusBar-foreground: #3b3b3b;
--vscode-statusBar-border: #e5e5e5;
--vscode-statusBarItem-hoverBackground: #b8b8b850;
--vscode-statusBarItem-compactHoverBackground: #cccccc;
--vscode-statusBar-debuggingBackground: #fd716c;
--vscode-statusBar-debuggingForeground: #000000;
--vscode-statusBar-focusBorder: #005fb8;
--vscode-statusBar-noFolderBackground: #f8f8f8;
--vscode-statusBarItem-focusBorder: #005fb8;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-tab-activeBackground: #ffffff;
--vscode-tab-activeBorder: #f8f8f8;
--vscode-tab-activeBorderTop: #005fb8;
--vscode-tab-activeForeground: #3b3b3b;
--vscode-tab-selectedBorderTop: #68a3da;
--vscode-tab-border: #e5e5e5;
--vscode-tab-hoverBackground: #ffffff;
--vscode-tab-inactiveBackground: #f8f8f8;
--vscode-tab-inactiveForeground: #868686;
--vscode-tab-unfocusedActiveBorder: #f8f8f8;
--vscode-tab-unfocusedActiveBorderTop: #e5e5e5;
--vscode-tab-unfocusedHoverBackground: #f8f8f8;
--vscode-terminalCursor-foreground: #005fb8;
--vscode-terminal-foreground: #3b3b3b;
--vscode-terminal-tab-activeBorder: #005fb8;
--vscode-textBlockQuote-background: #f8f8f8;
--vscode-textBlockQuote-border: #e5e5e5;
--vscode-textCodeBlock-background: #f8f8f8;
--vscode-textLink-activeForeground: #005fb8;
--vscode-textLink-foreground: #005fb8;
--vscode-textPreformat-foreground: #3b3b3b;
--vscode-textPreformat-background: #0000001f;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #f8f8f8;
--vscode-titleBar-activeForeground: #1e1e1e;
--vscode-titleBar-border: #e5e5e5;
--vscode-titleBar-inactiveBackground: #f8f8f8;
--vscode-titleBar-inactiveForeground: #8b949e;
--vscode-welcomePage-tileBackground: #f3f3f3;
