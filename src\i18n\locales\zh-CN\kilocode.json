{"info": {"settings_imported": "设置导入成功。"}, "userFeedback": {"message_update_failed": "更新消息失败", "no_checkpoint_found": "在此消息之前未找到检查点", "message_updated": "消息已成功更新"}, "lowCreditWarning": {"title": "余额不足警告！", "message": "请检查是否可以充值免费额度或购买更多额度！"}, "notLoggedInError": "无法完成请求，请确保您已连接并使用所选提供商登录。\n\n{{error}}", "rules": {"actions": {"delete": "删除", "confirmDelete": "确定要删除 {{filename}} 吗？", "deleted": "已删除 {{filename}}"}, "errors": {"noWorkspaceFound": "未找到工作区文件夹", "fileAlreadyExists": "文件 {{filename}} 已存在", "failedToCreateRuleFile": "创建规则文件失败。", "failedToDeleteRuleFile": "删除规则文件失败。"}, "templates": {"workflow": {"description": "工作流描述在此...", "stepsHeader": "## 步骤", "step1": "步骤 1", "step2": "步骤 2"}, "rule": {"description": "规则描述在此...", "guidelinesHeader": "## 指导原则", "guideline1": "指导原则 1", "guideline2": "指导原则 2"}}}, "commitMessage": {"activated": "Kilo Code: 提交信息生成器已启动", "gitNotFound": "⚠️ Git 仓库未找到或 git 不可用", "gitInitError": "⚠️ Git 初始化错误：{{error}}", "generating": "<PERSON>lo: 正在生成提交信息...", "noChanges": "Kilo: 未发现可分析的更改", "generated": "Kilo: 已生成提交信息！", "generationFailed": "<PERSON><PERSON>：生成提交信息失败：{{errorMessage}}", "generatingFromUnstaged": "Kilo：使用未暂存的更改生成消息", "providerRegistered": "Kilo：提交消息提供程序已注册", "activationFailed": "<PERSON><PERSON>：消息生成器激活失败：{{error}}"}, "autocomplete": {"statusBar": {"warning": "$(warning) Kilo自动补全", "enabled": "$(sparkle) Kilo自动补全", "disabled": "$(circle-slash) Kilo自动补全已禁用", "tooltip": {"disabled": "Kilo自动补全（已禁用）", "lastCompletion": "上一次完成：", "sessionTotal": "会话总费用：", "basic": "Kilo自动补全", "tokenError": "必须设置有效令牌才能使用自动完成功能", "model": "模型："}, "cost": {"lessThanCent": "<$0.01", "zero": "¥0.00"}}, "toggleMessage": "Kilo 自动补全 {{status}}"}, "ghost": {"progress": {"analyzing": "正在分析您的代码...", "generating": "正在生成建议修改...", "processing": "正在处理建议的编辑...", "showing": "显示建议的编辑...", "title": "Kilo Code"}, "input": {"title": "Kilo：快速任务", "placeholder": "例如，\"重构这个函数使其更高效\""}, "commands": {"displaySuggestions": "显示建议的编辑", "applyCurrentSuggestion": "应用当前建议的编辑", "generateSuggestions": "<PERSON><PERSON>：生成建议的编辑", "cancelSuggestions": "取消建议的编辑", "applyAllSuggestions": "应用所有建议的编辑", "promptCodeSuggestion": "快速任务提示", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code：建议编辑"}, "chatParticipant": {"fullName": "Kilo Code 代理", "description": "我可以帮助你完成快速任务和提供建议的编辑。", "name": "代理"}, "messages": {"provideCodeSuggestions": "正在提供代码建议..."}}}