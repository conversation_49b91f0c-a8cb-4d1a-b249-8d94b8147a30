{"answers": {"yes": "Ναι", "no": "Όχι", "cancel": "Ακύρωση", "remove": "Αφαίρεση", "keep": "Διατήρη<PERSON>η"}, "number_format": {"thousand_suffix": "χιλ", "million_suffix": "εκατ", "billion_suffix": "δισ"}, "feedback": {"title": "Σχόλια", "description": "Θα θέλαμε να ακούσουμε τα σχόλιά σου ή να σε βοηθήσουμε με οποιοδήποτε πρόβλημα αντιμετωπίζεις", "githubIssues": "Αναφορά προβλήματος στο <PERSON>", "githubDiscussions": "Συμμετο<PERSON><PERSON> στις συζητήσεις του GitHub", "discord": "Συμμετοχή στην κοινότητά μας στο Discord", "customerSupport": "Υποστήριξη πελατών"}, "ui": {"search_placeholder": "Αναζήτηση..."}, "mermaid": {"loading": "Δημιουργία διαγράμματος mermaid...", "render_error": "Αδυναμία απόδοσης διαγράμματος", "fixing_syntax": "Διόρθωση σύνταξης Mermaid...", "fix_syntax_button": "Διόρθωση σύνταξης με AI", "original_code": "Αρχι<PERSON><PERSON><PERSON> κώδικας:", "errors": {"unknown_syntax": "Άγνωστο συντακτικό σφάλμα", "fix_timeout": "Το αίτημα διόρθωσης LLM έληξε", "fix_failed": "Η διόρθωση LLM απέτυχε", "fix_attempts": "Αποτυχία διόρθωσης σύνταξης μετά από {{attempts}} προσπάθειες. Τελευταίο σφάλμα: {{error}}", "no_fix_provided": "Το LLM απέτυχε να παρέχει διόρθωση", "fix_request_failed": "Το αίτημα διόρθωσης απέτυχε"}, "buttons": {"zoom": "Ζουμ", "zoomIn": "Μεγέθυνση", "zoomOut": "Σμίκρυνση", "copy": "Αντιγραφή", "save": "Αποθήκευση εικόνας", "viewCode": "Προβολή κώδικα", "viewDiagram": "Προβολή διαγράμματος", "close": "Κλείσιμο"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mermaid"}, "tabs": {"diagram": "Διάγραμμα", "code": "Κώδικας"}, "feedback": {"imageCopied": "Η εικόνα αντιγράφηκε στο πρόχειρο", "copyError": "Σφάλμα κατά την αντιγραφή της εικόνας"}}, "file": {"errors": {"invalidDataUri": "Μη έγκυρη μορφή data URI", "copyingImage": "Σφάλ<PERSON>α κατά την αντιγραφή της εικόνας: {{error}}", "openingImage": "Σφάλμα κατά το άνοιγμα της εικόνας: {{error}}", "pathNotExists": "Η διαδρομή δεν υπάρχει: {{path}}", "couldNotOpen": "Αδυναμία ανοίγματος αρχείου: {{error}}", "couldNotOpenGeneric": "Αδυναμία ανοίγματος αρχείου!"}, "success": {"imageDataUriCopied": "Το image data URI αντιγράφηκε στο πρόχειρο"}}}