{"type-group": {"modes": "Lägen", "mcps": "MCP-servrar", "match": "tr<PERSON><PERSON>"}, "item-card": {"type-mode": "Läge", "type-mcp": "MCP-server", "type-other": "<PERSON><PERSON>", "by-author": "av {{author}}", "authors-profile": "Författarens profil", "remove-tag-filter": "Ta bort taggfilter: {{tag}}", "filter-by-tag": "Filtrera efter tagg: {{tag}}", "component-details": "Komponentdetaljer", "view": "Visa", "source": "<PERSON><PERSON><PERSON>"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> på marknadsplatsen..."}, "type": {"label": "<PERSON><PERSON>", "all": "Alla typer", "mode": "Läge", "mcpServer": "MCP-server"}, "sort": {"label": "Sortera efter", "name": "<PERSON><PERSON>", "lastUpdated": "Senast uppdaterad"}, "tags": {"label": "Taggar", "clear": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> tag<PERSON>...", "noResults": "Inga taggar hittades.", "selected": "Visar objekt med någon av de valda taggarna"}, "title": "Marknadsplats"}, "done": "<PERSON><PERSON>", "tabs": {"installed": "Installerade", "browse": "Bläddra", "settings": "Inställningar"}, "items": {"empty": {"noItems": "Inga marknadsplatsobjekt hittades.", "emptyHint": "Försök justera dina filter eller sö<PERSON>mer"}}, "installation": {"installing": "Installerar objekt: \"{{itemName}}\"", "installSuccess": "\"{{itemName}}\" installerades framgångsrikt", "installError": "<PERSON><PERSON><PERSON><PERSON> med att installera \"{{itemName}}\": {{errorMessage}}", "removing": "Tar bort objekt: \"{{itemName}}\"", "removeSuccess": "\"{{itemName}}\" togs bort framgångsrikt", "removeError": "<PERSON><PERSON><PERSON><PERSON> med att ta bort \"{{itemName}}\": {{errorMessage}}"}}