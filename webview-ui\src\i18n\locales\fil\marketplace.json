{"title": "Kilo Code Marketplace", "tabs": {"installed": "Naka-install", "settings": "Mga Setting", "browse": "Mag-browse"}, "done": "Tapos na", "refresh": "I-refresh", "filters": {"search": {"placeholder": "Maghanap ng mga item sa marketplace...", "placeholderMcp": "Maghanap ng mga MCP...", "placeholderMode": "Maghanap ng mga Mode..."}, "type": {"label": "I-filter ayon sa uri:", "all": "Lahat ng uri", "mode": "Mode", "mcpServer": "MCP Server"}, "sort": {"label": "Pagbukud-bukurin ayon sa:", "name": "<PERSON><PERSON><PERSON>", "author": "May-akda", "lastUpdated": "Huling Na-update"}, "tags": {"label": "I-filter ayon sa mga tag:", "clear": "Linisin ang mga tag", "placeholder": "Mag-type para maghanap at pumili ng mga tag...", "noResults": "Walang nahanap na tumugmang tag", "selected": "Ipinapakita ang mga item na may alinman sa mga napiling tag", "clickToFilter": "I-click ang mga tag para i-filter ang mga item"}, "none": "Wala"}, "type-group": {"modes": "Mga Mode", "mcps": "Mga MCP Server"}, "items": {"empty": {"noItems": "Walang nahanap na item sa marketplace", "withFilters": "<PERSON>ukang ayusin ang iyong mga filter", "noSources": "Subukang magdagdag ng source sa tab ng Sources", "adjustFilters": "<PERSON>ukang ayusin ang iyong mga filter o mga salitang hinahanap", "clearAllFilters": "<PERSON><PERSON><PERSON> lahat ng filter"}, "count": "{{count}} na item ang nahanap", "components": "{{count}} na component", "matched": "{{count}} na tumugma", "refresh": {"button": "I-refresh", "refreshing": "Nire-refresh...", "mayTakeMoment": "<PERSON><PERSON>ing tumagal ito ng ilang sandali."}, "card": {"by": "ni {{author}}", "from": "mula sa {{source}}", "install": "I-install", "installProject": "I-install", "installGlobal": "I-install (Global)", "remove": "Tanggalin", "removeProject": "Tanggalin", "removeGlobal": "<PERSON><PERSON><PERSON> (Global)", "viewSource": "Tingnan", "viewOnSource": "Tingnan sa {{source}}", "noWorkspaceTooltip": "Magbukas ng workspace para mag-install ng mga item sa marketplace", "installed": "Naka-install", "removeProjectTooltip": "Tang<PERSON>in mula sa kasalukuyang proyekto", "removeGlobalTooltip": "Tanggalin mula sa global configuration", "actionsMenuLabel": "Higit pang aksyon"}}, "install": {"title": "I-install ang {{name}}", "titleMode": "I-install ang {{name}} Mode", "titleMcp": "I-install ang {{name}} MCP", "scope": "Saklaw ng Pag-install", "project": "Proyekto (kasalukuyang workspace)", "global": "Global (lahat ng workspace)", "method": "Paraan ng Pag-install", "prerequisites": "<PERSON><PERSON>", "configuration": "Configuration", "configurationDescription": "I-configure ang mga parameter na kinakailangan para sa MCP server na ito", "button": "I-install", "successTitle": "Na-install ang {{name}}", "successDescription": "Matagumpay na natapos ang pag-install", "installed": "Matagumpay na na-install!", "whatNextMcp": "<PERSON><PERSON><PERSON> mo na ngayong i-configure at gamitin ang MCP server na ito. I-click ang MCP icon sa sidebar para lumipat ng tab.", "whatNextMode": "<PERSON><PERSON>i mo na ngayong gamitin ang mode na ito. I-click ang Modes icon sa sidebar para lumipat ng tab.", "done": "Tapos na", "goToMcp": "Pumunta sa MCP Tab", "goToModes": "Pumunta sa Modes Tab", "moreInfoMcp": "Tingnan ang dokumentasyon ng {{name}} MCP", "validationRequired": "Mangyaring magbigay ng halaga para sa {{paramName}}"}, "sources": {"title": "I-configure ang Marketplace Sources", "description": "Magdagdag ng mga Git repository na naglalaman ng mga marketplace item. Ang mga repository na ito ay kukunin kapag nagba-browse ng marketplace.", "add": {"title": "Magdagdag ng Bagong Source", "urlPlaceholder": "Git repository URL (hal., https://github.com/username/repo)", "urlFormats": "Mga suportadong format: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git), o Git protocol (git://github.com/username/repo.git)", "namePlaceholder": "Display name (max 20 chars)", "button": "Magdagdag ng Source"}, "current": {"title": "Kasalukuyang mga Source", "empty": "Walang naka-configure na source. Magdagdag ng source para magsimula.", "refresh": "I-refresh ang source na ito", "remove": "Tanggalin ang source"}, "errors": {"emptyUrl": "Hindi maaaring walang laman ang URL", "invalidUrl": "Hindi wastong format ng URL", "nonVisibleChars": "Ang URL ay naglalaman ng mga hindi nakikitang character maliban sa mga space", "invalidGitUrl": "Ang URL ay dapat na wastong Git repository URL (hal., https://github.com/username/repo)", "duplicateUrl": "Ang URL na ito ay nasa listahan na (case at whitespace insensitive match)", "nameTooLong": "<PERSON> pangalan ay dapat 20 character o mas kaunti", "nonVisibleCharsName": "<PERSON> pangalan ay naglalaman ng mga hindi nakikitang character maliban sa mga space", "duplicateName": "<PERSON> pangalang ito ay ginagamit na (case at whitespace insensitive match)", "emojiName": "Ang mga emoji character ay maaaring magdulot ng mga problema sa display", "maxSources": "Maximum na {{max}} source ang pin<PERSON>gan"}}, "footer": {"issueText": "Nakakita ng problema sa isang marketplace item o may mga suhestiyon para sa mga bago? <0>Magbukas ng GitHub issue</0> para ipaalam sa amin!"}}