{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Целая команда ИИ-разработчиков в вашем редакторе.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "views.activitybar.title": "Kilo Code", "command.newTask.title": "Новая задача", "command.mcpServers.title": "MCP серверы", "command.prompts.title": "Режимы", "command.history.title": "История", "command.marketplace.title": "Маркетплейс", "command.openInEditor.title": "Открыть в редакторе", "command.settings.title": "Настройки", "command.documentation.title": "Документация", "command.openInNewTab.title": "Открыть в новой вкладке", "command.explainCode.title": "Объяснить код", "command.fixCode.title": "Исправить код", "command.improveCode.title": "Улучшить код", "command.addToContext.title": "Добавить в контекст", "command.focusInput.title": "Фокус на поле ввода", "command.setCustomStoragePath.title": "Указать путь хранения", "command.importSettings.title": "Импортировать настройки", "command.terminal.addToContext.title": "Добавить содержимое терминала в контекст", "command.terminal.fixCommand.title": "Исправить эту команду", "command.terminal.explainCommand.title": "Объяснить эту команду", "command.acceptInput.title": "Принять ввод/предложение", "command.generateCommitMessage.title": "Сгенерировать сообщение коммита с Kilo", "command.profile.title": "Профиль", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Команды, которые могут быть автоматически выполнены, когда включена опция 'Всегда подтверждать операции выполнения'", "settings.vsCodeLmModelSelector.description": "Настройки для VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Поставщик языковой модели (например, copilot)", "settings.vsCodeLmModelSelector.family.description": "Семейство языковой модели (например, gpt-4)", "settings.customStoragePath.description": "Пользовательский путь хранения. Оставьте пустым для использования пути по умолчанию. Поддерживает абсолютные пути (например, 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Включить быстрые исправления Kilo Code.", "settings.autoImportSettingsPath.description": "Путь к файлу конфигурации Kilo Code для автоматического импорта при запуске расширения. Поддерживает абсолютные пути и пути относительно домашнего каталога (например, '~/Documents/kilo-code-settings.json'). Оставьте пустым, чтобы отключить автоматический импорт.", "ghost.input.title": "Нажмите 'Enter' для подтверждения или 'Escape' для отмены", "ghost.input.placeholder": "Опишите, что вы хотите сделать...", "ghost.commands.generateSuggestions": "Kilo Code: Генерировать Предлагаемые Правки", "ghost.commands.displaySuggestions": "Показать Предлагаемые Правки", "ghost.commands.cancelSuggestions": "Отменить Предлагаемые Правки", "ghost.commands.applyCurrentSuggestion": "Применить Текущую Предлагаемую Правку", "ghost.commands.applyAllSuggestions": "Применить Все Предлагаемые Правки", "ghost.commands.promptCodeSuggestion": "Быстрая Задача", "ghost.commands.goToNextSuggestion": "Перейти к Следующему Предложению", "ghost.commands.goToPreviousSuggestion": "Перейти к Предыдущему Предложению"}