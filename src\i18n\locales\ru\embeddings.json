{"unknownError": "Неизвестная ошибка", "authenticationFailed": "Не удалось создать вложения: <PERSON>ш<PERSON>бка аутентификации. Проверьте свой ключ API.", "failedWithStatus": "Не удалось создать вложения после {{attempts}} попыток: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Не удалось создать вложения после {{attempts}} попыток: {{errorMessage}}", "failedMaxAttempts": "Не удалось создать вложения после {{attempts}} попыток", "textExceedsTokenLimit": "Текст в индексе {{index}} превышает максимальный лимит токенов ({{itemTokens}} > {{maxTokens}}). Пропускается.", "rateLimitRetry": "Достигнут лимит скорости, повторная попытка через {{delayMs}} мс (попытка {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Не удалось прочитать тело ошибки", "requestFailed": "Запрос к API Ollama не удался со статусом {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Неверная структура ответа от API Ollama: массив \"embeddings\" не найден или не является массивом.", "embeddingFailed": "Вложение Ollama не удалось: {{message}}", "serviceNotRunning": "Сер<PERSON><PERSON><PERSON> не запущен по адресу {{baseUrl}}", "serviceUnavailable": "Серви<PERSON> Ollama недоступен (статус: {{status}})", "modelNotFound": "Мод<PERSON><PERSON>ь Ollama не найдена: {{modelId}}", "modelNotEmbeddingCapable": "Модель Ollama не способна к вложению: {{modelId}}", "hostNotFound": "<PERSON>о<PERSON><PERSON> Ollama не найден: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Неизвестная ошибка при обработке файла {{filePath}}", "unknownErrorDeletingPoints": "Неизвестная ошибка при удалении точек для {{filePath}}", "failedToProcessBatchWithError": "Не удалось обработать пакет после {{maxRetries}} попыток: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Не удалось подключиться к векторной базе данных Qdrant. Убедитесь, что Qdrant запущен и доступен по адресу {{qdrantUrl}}. Ошибка: {{errorMessage}}"}, "validation": {"authenticationFailed": "Ошибка аутентификации. Проверьте свой ключ API в настройках.", "connectionFailed": "Не удалось подключиться к службе эмбеддера. Проверьте настройки подключения и убедитесь, что служба запущена.", "modelNotAvailable": "Указанная модель недоступна. Проверьте конфигурацию модели.", "configurationError": "Неверная конфигурация эмбеддера. Проверьте свои настройки.", "serviceUnavailable": "Служба эмбеддера недоступна. Убедитесь, что она запущена и доступна.", "invalidEndpoint": "Неверная конечная точка API. Проверьте конфигурацию URL.", "invalidEmbedderConfig": "Неверная конфигурация эмбеддера. Проверьте свои настройки.", "invalidApiKey": "Неверный ключ API. Проверьте конфигурацию ключа API.", "invalidBaseUrl": "Неверный базовый URL. Проверьте конфигурацию URL.", "invalidModel": "Неверная модель. Проверьте конфигурацию модели.", "invalidResponse": "Неверный ответ от службы embedder. Проверьте вашу конфигурацию."}, "serviceFactory": {"openAiConfigMissing": "Отсутствует конфигурация OpenAI для создания эмбеддера", "ollamaConfigMissing": "Отсутствует конфигурация Ollama для создания эмбеддера", "openAiCompatibleConfigMissing": "Отсутствует конфигурация, совместимая с OpenAI, для создания эмбеддера", "geminiConfigMissing": "Отсутствует конфигурация Gemini для создания эмбеддера", "invalidEmbedderType": "Настроен недопустимый тип эмбеддера: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Не удалось определить размерность вектора для модели '{{modelId}}' с провайдером '{{provider}}'. Убедитесь, что 'Размерность эмбеддинга' правильно установлена в настройках провайдера, совместимого с OpenAI.", "vectorDimensionNotDetermined": "Не удалось определить размерность вектора для модели '{{modelId}}' с провайдером '{{provider}}'. Проверьте профили модели или конфигурацию.", "qdrantUrlMissing": "Отсутствует URL Qdrant для создания векторного хранилища", "codeIndexingNotConfigured": "Невозможно создать сервисы: Индексация кода не настроена должным образом"}}