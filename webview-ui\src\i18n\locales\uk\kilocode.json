{"welcome": {"greeting": "Ласкаво просимо до Kilo Code!", "introText1": "Kilo Code — це безкоштовний агент кодування AI з відкритим кодом.", "introText2": "Працює з найновішими моделями AI, такими як Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 та понад 450 іншими.", "introText3": "Створи безкоштовний акаунт і отримай токени на $20 для використання з будь-якою моделлю AI.", "ctaButton": "Створити безкоштовний акаунт", "manualModeButton": "Використовуй власний API key", "alreadySignedUp": "Вже зареєструвався?", "loginText": "Увійти тут"}, "lowCreditWarning": {"addCredit": "Додати кредит", "lowBalance": "Твій баланс Kilo Code низький"}, "notifications": {"toolRequest": "Запит інструменту очікує на схвалення", "browserAction": "Дія браузера очікує на схвалення", "command": "Команда очікує на схвалення"}, "settings": {"sections": {"mcp": "MCP сервери"}, "contextManagement": {"allowVeryLargeReads": {"label": "Дозволити дуже великі читання файлів", "description": "Коли увімкнено, Kilo Code виконуватиме дуже великі читання файлів або виводу MCP, навіть якщо існує висока ймовірність переповнення вікна контексту (розмір вмісту >80% вікна контексту)."}}, "provider": {"account": "Обліковий запис Kilo Code", "apiKey": "Kilo Code API Key", "login": "Увійти в Kilo Code", "logout": "Вийти з Kilo Code"}, "systemNotifications": {"label": "Увімкнути системні сповіщення", "description": "Коли увімкнено, Kilo Code надсилатиме системні сповіщення про важливі події, такі як завершення завдання або помилки.", "testButton": "Тестове сповіщення", "testTitle": "Kilo Code", "testMessage": "Це тестове сповіщення від Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code хоче стиснути твою розмову", "condenseConversation": "Стиснути розмову"}}, "newTaskPreview": {"task": "Завдання"}, "profile": {"title": "Профіль", "dashboard": "Панель керування", "logOut": "Вийти", "currentBalance": "ПОТОЧНИЙ БАЛАНС", "loading": "Завантаження..."}, "docs": "Документація", "rules": {"tooltip": "Керувати Kilo Code Rules та Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "Правила", "workflows": "Робочі процеси"}, "description": {"rules": "Правила дозволяють тобі надавати Kilo Code інструкції, яких він повинен дотримуватися в усіх режимах і для всіх промптів. Це постійний спосіб включити контекст і налаштування для всіх розмов у твоєму робочому просторі або глобально.", "workflows": "Робочі процеси - це підготовлений шаблон для розмови. Робочі процеси можуть дозволити тобі визначити промпти, які ти часто використовуєш, і можуть включати серію кроків для керування Kilo Code через повторювані завдання, такі як розгортання сервісу або надсилання PR. Щоб викликати робочий процес, введи", "workflowsInChat": "в чаті."}, "sections": {"globalRules": "Глобальні правила", "workspaceRules": "Правила робочого простору", "globalWorkflows": "Глобальні робочі процеси", "workspaceWorkflows": "Робочі процеси робочого простору"}, "validation": {"invalidFileExtension": "Дозволено лише .md, .txt або без розширення файлу"}, "placeholders": {"workflowName": "workflow-name (.md, .txt або без розширення)", "ruleName": "rule-name (.md, .txt або без розширення)"}, "newFile": {"newWorkflowFile": "Новий файл робочого процесу...", "newRuleFile": "Новий файл правила..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Переглянути {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "запуск браузера", "browser_action_result": "результат дії браузера", "browser_action": "дія браузера", "checkpoint_saved": "збережено контрольну точку", "command": "виконання команди", "command_output": "вивід команди", "completion_result": "результат завершення", "condense_context": "стиснути контекст", "error": "повідомлення про помилку", "followup": "додаткове питання", "mcp_server_response": "відповідь MCP сервера", "reasoning": "міркування AI", "text": "відповідь AI", "tool": "використання інструменту", "unknown": "невідомий тип повідомлення", "use_mcp_server": "використання MCP сервера", "user": "відгуки користувача"}}}, "userFeedback": {"editCancel": "Скасувати", "send": "Надіслати", "restoreAndSend": "Відновити та надіслати"}, "ideaSuggestionsBox": {"newHere": "Новачок тут?", "suggestionText": "<suggestionButton>Натисніть тут</suggestionButton> для крутої ідеї, потім торкніться <sendIcon /> і дивіться, як я творю магію!", "ideas": {"idea1": "Створіть обертову, світлу 3D-сферу, яка реагує на рухи миші. Зробіть так, щоб додаток працював у браузері.", "idea2": "Створіть портфоліо-веб-сайт для розробника Python", "idea3": "Створіть макет фінансового додатка у браузері. Потім перевірте, чи працює він", "idea4": "Створіть веб-сайт довідника з найкращими моделями генерації відео ШІ на даний момент", "idea5": "Створіть генератор CSS-градієнтів, який експортує користувацькі таблиці стилів і показує попередній перегляд у реальному часі", "idea6": "Створіть картки, які красиво розкривають динамічний контент при наведенні", "idea7": "Створіть заспокійливе інтерактивне зоряне поле, яке рухається з жестами миші"}}}