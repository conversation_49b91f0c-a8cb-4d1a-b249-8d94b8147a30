// @ts-nocheck

function* _getAddress(_person: Person): Address {
	// TODO
}

function* _getFirstAddress(_people: Person[]): Address {
	// TODO
}

function* _logPerson(_person: Person) {
	// TODO
}

function* _getHardcodedAddress(): Address {
	// TODO
}

function* _getAddresses(_people: Person[]): Address[] {
	// TODO
}

function* _logPersonWithAddress(_person: Person<Address>): Person<Address> {
	// TODO
}

function* _logPersonOrAddress(_person: Person | Address): Person | Address {
	// TODO
}

function* _logPersonAndAddress(_person: Person, _address: Address) {
	// TODO
}
