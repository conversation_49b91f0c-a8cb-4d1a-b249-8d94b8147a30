{"greeting": "Добро пожаловать в Kilo Code", "task": {"title": "Задача", "seeMore": "Показать больше", "seeLess": "Показать меньше", "tokens": "Токенов:", "cache": "Кэш:", "apiCost": "Стоимость API:", "contextWindow": "<PERSON><PERSON><PERSON><PERSON> контекста:", "closeAndStart": "Закрыть задачу и начать новую", "export": "Экспортировать историю задач", "delete": "Удалить задачу (Shift + клик для пропуска подтверждения)", "condenseContext": "Интеллектуально сжать контекст", "share": "Поделиться задачей", "shareWithOrganization": "Поделиться с организацией", "shareWithOrganizationDescription": "Только члены вашей организации могут получить доступ", "sharePublicly": "Поделиться публично", "sharePubliclyDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, у кого есть ссылка, может получить доступ", "connectToCloud": "Подключиться к облаку", "connectToCloudDescription": "Войди в Kilo Code Cloud, чтобы делиться задачами", "sharingDisabledByOrganization": "Об<PERSON>ен отключен организацией", "shareSuccessOrganization": "Ссылка организации скопирована в буфер обмена", "shareSuccessPublic": "Публичная ссылка скопирована в буфер обмена"}, "history": {"title": "История"}, "unpin": "Открепить", "pin": "Закрепить", "retry": {"title": "Повторить", "tooltip": "Попробовать выполнить операцию снова"}, "startNewTask": {"title": "Начать новую задачу", "tooltip": "Начать новую задачу"}, "reportBug": {"title": "Сообщить об ошибке"}, "proceedAnyways": {"title": "Все равно продолжить", "tooltip": "Продолжить выполнение команды"}, "save": {"title": "Сохранить", "tooltip": "Сохранить изменения в файле"}, "tokenProgress": {"availableSpace": "Доступно места: {{amount}} токенов", "tokensUsed": "Использовано токенов: {{used}} из {{total}}", "reservedForResponse": "Зарезервировано для ответа модели: {{amount}} токенов"}, "reject": {"title": "Отклонить", "tooltip": "Отклонить это действие"}, "completeSubtaskAndReturn": "Завершить подзадачу и вернуться", "approve": {"title": "Одобрить", "tooltip": "Одобрить это действие"}, "runCommand": {"title": "Выполнить команду", "tooltip": "Выполнить эту команду"}, "proceedWhileRunning": {"title": "Продолжить во время выполнения", "tooltip": "Продолжить несмотря на предупреждения"}, "resumeTask": {"title": "Возобновить задачу", "tooltip": "Продолжить текущую задачу"}, "killCommand": {"title": "Завер<PERSON>ить команду", "tooltip": "Завершить текущую команду"}, "terminate": {"title": "Завершить", "tooltip": "Завершить текущую задачу"}, "cancel": {"title": "Отмена", "tooltip": "Отменить текущую операцию"}, "scrollToBottom": "Прокрутить чат вниз", "about": "Создавайте, рефакторите и отлаживайте код с помощью ИИ. Подробнее см. в нашей <DocsLink>документации</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Оркестрация задач", "description": "Разделяйте задачи на более мелкие, управляемые части"}, "stickyModels": {"title": "Липкие режимы", "description": "Каждый режим запоминает вашу последнюю использованную модель"}, "tools": {"title": "Инструменты", "description": "Разрешите ИИ решать проблемы, просматривая веб-страницы, выполняя команды и т. д."}, "customizableModes": {"title": "Настраиваемые режимы", "description": "Специализированные персонажи с собственным поведением и назначенными моделями"}}, "onboarding": "<strong>Ваш список задач в этом рабочем пространстве пуст.</strong> Начните с ввода задачи ниже. Не знаете, с чего начать? Подробнее о возможностях Kilo Code читайте в <DocsLink>документации</DocsLink>.", "selectMode": "Выберите режим взаимодействия", "selectApiConfig": "Выберите конфигурацию API", "selectModelConfig": "Выберите модель", "enhancePrompt": "Улучшить запрос с дополнительным контекстом", "enhancePromptDescription": "Кнопка 'Улучшить запрос' помогает сделать ваш запрос лучше, предоставляя дополнительный контекст, уточнения или переформулировку. Попробуйте ввести запрос и снова нажать кнопку, чтобы увидеть, как это работает.", "modeSelector": {"title": "Режимы", "marketplace": "Маркетплейс режимов", "settings": "Настройки режимов", "description": "Специализированные персоны, которые настраивают поведение Kilo Code."}, "addImages": "Добавить изображения к сообщению", "sendMessage": "Отправить сообщение", "stopTts": "Остановить синтез речи", "typeMessage": "Введите сообщение...", "typeTask": "Введите вашу задачу здесь...", "addContext": "@ для добавления контекста, / для смены режима", "dragFiles": "удерживайте shift для перетаскивания файлов", "dragFilesImages": "удерживайте shift для перетаскивания файлов/изображений", "errorReadingFile": "Ошибка чтения файла:", "noValidImages": "Не удалось обработать ни одно изображение", "separator": "Разделитель", "edit": "Редактировать...", "forNextMode": "для следующего режима", "apiRequest": {"title": "API-запрос", "failed": "API-запрос не выполнен", "streaming": "API-запрос...", "cancelled": "API-запрос отменен", "streamingFailed": "Ошибка потокового API-запроса"}, "checkpoint": {"initial": "Начальная точка сохранения", "regular": "Точка сохранения", "initializingWarning": "Точка сохранения еще инициализируется... Если это занимает слишком много времени, вы можете отключить точки сохранения в <settingsLink>настройках</settingsLink> и перезапустить задачу.", "menu": {"viewDiff": "Просмотреть различия", "restore": "Восстановить точку сохранения", "restoreFiles": "Восстановить файлы", "restoreFilesDescription": "Восстанавливает файлы вашего проекта до состояния на момент этой точки.", "restoreFilesAndTask": "Восстановить файлы и задачу", "confirm": "Подтвердить", "cancel": "Отмена", "cannotUndo": "Это действие нельзя отменить.", "restoreFilesAndTaskDescription": "Восстанавливает файлы проекта до состояния на момент этой точки и удаляет все сообщения после нее."}, "current": "Текущая"}, "instructions": {"wantsToFetch": "Kilo Code хочет получить подробные инструкции для помощи с текущей задачей"}, "fileOperations": {"wantsToRead": "Kilo Code хочет прочитать этот файл:", "wantsToReadOutsideWorkspace": "Kilo Code хочет прочитать этот файл вне рабочей области:", "didRead": "Kilo Code прочитал этот файл:", "wantsToEdit": "Kilo Code хочет отредактировать этот файл:", "wantsToEditOutsideWorkspace": "Kilo Code хочет отредактировать этот файл вне рабочей области:", "wantsToEditProtected": "Kilo Code хочет отредактировать защищённый файл конфигурации:", "wantsToCreate": "Kilo Code хочет создать новый файл:", "wantsToSearchReplace": "Kilo Code хочет выполнить поиск и замену в этом файле:", "didSearchReplace": "Kilo Code выполнил поиск и замену в этом файле:", "wantsToInsert": "Kilo Code хочет вставить содержимое в этот файл:", "wantsToInsertWithLineNumber": "Kilo Code хочет вставить содержимое в этот файл на строку {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code хочет добавить содержимое в конец этого файла:", "wantsToReadAndXMore": "Kilo Code хочет прочитать этот файл и еще {{count}}:", "wantsToReadMultiple": "Kilo Code хочет прочитать несколько файлов:", "wantsToApplyBatchChanges": "Kilo Code хочет применить изменения к нескольким файлам:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code хочет просмотреть файлы верхнего уровня в этой директории:", "didViewTopLevel": "Kilo Code просмотрел файлы верхнего уровня в этой директории:", "wantsToViewRecursive": "Kilo Code хочет рекурсивно просмотреть все файлы в этой директории:", "didViewRecursive": "Kilo Code рекурсивно просмотрел все файлы в этой директории:", "wantsToViewDefinitions": "Kilo Code хочет просмотреть имена определений исходного кода в этой директории:", "didViewDefinitions": "Kilo Code просмотрел имена определений исходного кода в этой директории:", "wantsToSearch": "Kilo Code хочет выполнить поиск в этой директории по <code>{{regex}}</code>:", "didSearch": "Kilo Code выполнил поиск в этой директории по <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code хочет выполнить поиск в этой директории (вне рабочего пространства) по <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code выполнил поиск в этой директории (вне рабочего пространства) по <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code хочет просмотреть файлы верхнего уровня в этой директории (вне рабочего пространства):", "didViewTopLevelOutsideWorkspace": "Kilo Code просмотрел файлы верхнего уровня в этой директории (вне рабочего пространства):", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code хочет рекурсивно просмотреть все файлы в этой директории (вне рабочего пространства):", "didViewRecursiveOutsideWorkspace": "Kilo Code рекурсивно просмотрел все файлы в этой директории (вне рабочего пространства):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code хочет просмотреть имена определений исходного кода в этой директории (вне рабочего пространства):", "didViewDefinitionsOutsideWorkspace": "Kilo Code просмотрел имена определений исходного кода в этой директории (вне рабочего пространства):"}, "commandOutput": "Вывод команды", "response": "Ответ", "arguments": "Аргументы", "mcp": {"wantsToUseTool": "Kilo Code хочет использовать инструмент на сервере MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code хочет получить доступ к ресурсу на сервере MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code хочет переключиться в режим {{mode}}", "wantsToSwitchWithReason": "Kilo Code хочет переключиться в режим {{mode}}, потому что: {{reason}}", "didSwitch": "Kilo Code переключился в режим {{mode}}", "didSwitchWithReason": "<PERSON><PERSON> Code переключился в режим {{mode}}, потому что: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code хочет создать новую подзадачу в режиме {{mode}}:", "wantsToFinish": "Kilo Code хочет завершить эту подзадачу", "newTaskContent": "Инструкции по подзадаче", "completionContent": "Подзадача завершена", "resultContent": "Результаты подзадачи", "defaultResult": "Пожалуйста, переходите к следующей задаче.", "completionInstructions": "Подзадача завершена! Вы можете просмотреть результаты и предложить исправления или следующие шаги. Если всё в порядке, подтвердите для возврата результата в родительскую задачу."}, "questions": {"hasQuestion": "У Kilo Code есть вопрос:"}, "taskCompleted": "Задача завершена", "error": "Ошибка", "diffError": {"title": "Не удалось выполнить редактирование"}, "troubleMessage": "У Kilo Code возникли проблемы...", "powershell": {"issues": "Похоже, у вас проблемы с Windows PowerShell, пожалуйста, ознакомьтесь с этим"}, "autoApprove": {"title": "Автоодобрение:", "none": "Нет", "description": "Автоодобрение позволяет Kilo Code выполнять действия без запроса разрешения. Включайте только для полностью доверенных действий. Более подробная настройка доступна в <settingsLink>Настройках</settingsLink>."}, "announcement": {"title": "🎉 Выпущен Roo Code {{version}}", "description": "Roo Code {{version}} приносит мощные новые функции и значительные улучшения для совершенствования вашего рабочего процесса разработки.", "whatsNew": "Что нового", "feature1": "<bold>Индексация кодовой базы выпущена из экспериментального статуса</bold>: Полная индексация кодовой базы теперь стабильна и готова к использованию в продакшене с улучшенным поиском и пониманием контекста.", "feature2": "<bold>Новая функция списка задач</bold>: Держите свои задачи на правильном пути с интегрированным управлением задачами, которое помогает оставаться организованным и сосредоточенным на целях разработки.", "feature3": "<bold>Улучшенные переходы от Архитектора к Коду</bold>: Плавные переходы от планирования в режиме Архитектора к реализации в режиме Кода.", "hideButton": "Скрыть объявление", "detailsDiscussLinks": "Подробнее и обсуждение в <discordLink>Discord</discordLink> и <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Обдумывание", "seconds": "{{count}}с"}, "contextCondense": {"title": "Контекст сжат", "condensing": "Сжатие контекста...", "errorHeader": "Не удалось сжать контекст", "tokens": "токены"}, "followUpSuggest": {"copyToInput": "Скопировать во ввод (то же, что shift + клик)", "autoSelectCountdown": "Автовыбор через {{count}}с", "countdownDisplay": "{{count}}с"}, "browser": {"rooWantsToUse": "Kilo Code хочет использовать браузер:", "consoleLogs": "Логи консоли", "noNewLogs": "(Новых логов нет)", "screenshot": "Скриншот браузера", "cursor": "курсор", "navigation": {"step": "Шаг {{current}} из {{total}}", "previous": "Предыдущий", "next": "Следующий"}, "sessionStarted": "Сессия браузера запущена", "actions": {"title": "Действие в браузере: ", "launch": "Открыть браузер по адресу {{url}}", "click": "Клик ({{coordinate}})", "type": "Ввести \"{{text}}\"", "scrollDown": "Прокрутить вниз", "scrollUp": "Прокрутить вверх", "close": "Закрыть браузер"}}, "codeblock": {"tooltips": {"expand": "Развернуть блок кода", "collapse": "Свернуть блок кода", "enable_wrap": "Включить перенос строк", "disable_wrap": "Отключить перенос строк", "copy_code": "Копировать код"}}, "systemPromptWarning": "ПРЕДУПРЕЖДЕНИЕ: Активна пользовательская системная подсказка. Это может серьезно нарушить работу и вызвать непредсказуемое поведение.", "profileViolationWarning": "Текущий профиль нарушает настройки вашей организации", "shellIntegration": {"title": "Предупреждение о выполнении команды", "description": "Ваша команда выполняется без интеграции оболочки терминала VSCode. Чтобы скрыть это предупреждение, вы можете отключить интеграцию оболочки в разделе <strong>Terminal</strong> в <settingsLink>настройках Kilo Code</settingsLink> или устранить проблемы с интеграцией терминала VSCode, используя ссылку ниже.", "troubleshooting": "Нажмите здесь для просмотра документации по интеграции оболочки."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Достигнут лимит автоматически одобренных запросов", "description": "Kilo Code достиг автоматически одобренного лимита в {{count}} API-запрос(ов). Хотите сбросить счетчик и продолжить задачу?", "button": "Сбросить и продолжить"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code хочет выполнить поиск в кодовой базе по <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code хочет выполнить поиск в кодовой базе по <code>{{query}}</code> в <code>{{path}}</code>:", "didSearch": "Найдено {{count}} результат(ов) для <code>{{query}}</code>:", "resultTooltip": "Оценка схожести: {{score}} (нажмите, чтобы открыть файл)"}, "read-batch": {"approve": {"title": "Одобрить все"}, "deny": {"title": "Отклонить все"}}, "indexingStatus": {"ready": "Индекс готов", "indexing": "Индексация {{percentage}}%", "indexed": "Проиндексировано", "error": "Ошибка индекса", "status": "Статус индекса"}, "versionIndicator": {"ariaLabel": "Версия {{version}} - Нажмите, чтобы просмотреть примечания к выпуску"}}