// @ts-nocheck

class _Group {
	_getPersonAddress(_person: Person): Address {
		// TODO
	}

	_getHardcodedAddress(): Address {
		// TODO
	}

	_addPerson(_person: Person) {
		// TODO
	}

	_addPeople(_people: Person[]) {
		// TODO
	}

	_getAddresses(_people: Person[]): Address[] {
		// TODO
	}

	_logPersonWithAddress(_person: Person<Address>): Person<Address> {
		// TODO
	}

	_logPersonOrAddress(_person: Person | Address): Person | Address {
		// TODO
	}

	_logPersonAndAddress(_person: Person, _address: Address) {
		// TODO
	}
}
