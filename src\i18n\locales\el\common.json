{"extension": {"name": "Kilo Code", "description": "Β<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικοποίησης AI ανοιχτού κώδικα για σχεδιασμό, δημιουργία και διόρθωση κώδικα."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Σχόλια", "description": "Θα θέλαμε να ακούσουμε τα σχόλιά σου ή να βοηθήσουμε με οποιαδήποτε προβλήματα αντιμετωπίζεις.", "githubIssues": "Αναφορά προβλήματος στο <PERSON>", "githubDiscussions": "Συμμετο<PERSON><PERSON> στις συζητήσεις του GitHub", "discord": "Γίν<PERSON> μέλος της κοινότητάς μας στο Discord", "customerSupport": "Υποστήριξη Πελατών"}, "welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθες, {{name}}! Έχεις {{count}} ειδοποιήσεις.", "items": {"zero": "Κανέ<PERSON><PERSON> στοιχείο", "one": "Ένα στοιχείο", "other": "{{count}} στοιχεία"}, "confirmation": {"reset_state": "Είσαι σίγουρος ότι θέλεις να επαναφέρεις όλη την κατάσταση και την μυστική αποθήκευση στην επέκταση; Αυτό δεν μπορεί να αναιρεθεί.", "delete_config_profile": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτό το προφίλ διαμόρφωσης;", "delete_custom_mode": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτή την προσαρμοσμένη λειτουργία;", "delete_custom_mode_with_rules": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτή τη λειτουργία {scope};\n\nΑυτό θα διαγράψει επίσης τον σχετικό φάκελο κανόνων στη διαδρομή:\n{rulesFolderPath}", "delete_message": "Τι θα ήθελες να διαγράψεις;", "edit_warning": "Η επεξεργασία αυτού του μηνύματος θα διαγράψει όλα τα επόμενα μηνύματα στη συνομιλία. Θέλεις να συνεχίσεις;", "delete_just_this_message": "Μόνο αυτό το μήνυμα", "delete_this_and_subsequent": "Αυτό και όλα τα επόμενα μηνύματα", "proceed": "Συνέχεια"}, "errors": {"invalid_data_uri": "Μη έγκυρη μορφή data URI", "error_copying_image": "Σφάλ<PERSON>α κατά την αντιγραφή εικόνας: {{errorMessage}}", "error_opening_image": "Σφάλμα κατά το άνοιγμα εικόνας: {{error}}", "error_saving_image": "Σφάλμα κατά την αποθήκευση εικόνας: {{errorMessage}}", "could_not_open_file": "Δεν ήταν δυνατό το άνοιγμα του αρχείου: {{errorMessage}}", "could_not_open_file_generic": "Δεν ήταν δυνατό το άνοιγμα του αρχείου!", "checkpoint_timeout": "Λήξη χρονικού ορίου κατά την προσπάθεια επαναφοράς του checkpoint.", "checkpoint_failed": "Αποτυχία επαναφοράς του checkpoint.", "no_workspace": "Παρα<PERSON><PERSON><PERSON><PERSON> άνοιξε πρώτα έναν φάκελο έργου", "update_support_prompt": "Αποτυχία ενημέρωσης του support prompt", "reset_support_prompt": "Αποτυχία επαναφοράς του support prompt", "enhance_prompt": "Αποτυχία βελτίωσης του prompt", "get_system_prompt": "Αποτυχία λήψης του system prompt", "search_commits": "Αποτυχ<PERSON><PERSON> αναζ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commits", "save_api_config": "Αποτυχία αποθήκευσης της διαμόρφωσης api", "create_api_config": "Αποτυχία δημιουργίας της διαμόρφωσης api", "rename_api_config": "Αποτυχ<PERSON>α μετονομασίας της διαμόρφωσης api", "load_api_config": "Αποτυχ<PERSON><PERSON> φόρτωσης της διαμόρφωσης api", "delete_api_config": "Αποτυχία διαγραφής της διαμόρφωσης api", "list_api_config": "Αποτυχ<PERSON>α λήψης λίστας διαμόρφωσης api", "update_server_timeout": "Αποτυχ<PERSON>α ενημέρωσης του server timeout", "hmr_not_running": "Ο τοπικός διακομιστής ανάπτυξης δεν εκτελείται, το HMR δεν θα λειτουργήσει. Παρακ<PERSON><PERSON><PERSON> εκτέλεσε 'npm run dev' πριν ξεκινήσεις την επέκταση για να ενεργοποιήσεις το HMR.", "retrieve_current_mode": "Σφάλμα: αποτυ<PERSON><PERSON><PERSON> ανάκτησης της τρέχουσας λειτουργίας από την κατάσταση.", "failed_delete_repo": "Αποτυχία διαγραφής του σχετικού shadow repository ή branch: {{error}}", "failed_remove_directory": "Αποτυχία αφαίρεσης του καταλόγου εργασίας: {{error}}", "custom_storage_path_unusable": "Η προσαρμοσμένη διαδρομή αποθήκευσης \"{{path}}\" δεν μπορεί να χρησιμοποιηθεί, θα χρησιμοποιηθεί η προεπιλεγμένη διαδρομή", "cannot_access_path": "Δεν είναι δυνατή η πρόσβαση στη διαδρομή {{path}}: {{error}}", "settings_import_failed": "Η εισαγωγή ρυθμίσεων απέτυχε: {{error}}.", "mistake_limit_guidance": "Αυτό μπορεί να υποδεικνύει αποτυχία στη διαδικασία σκέψης του μοντέλου ή αδυναμία χρήσης ενός εργαλείου σωστά, που μπορεί να μετριαστεί με κάποια καθοδήγηση από τον χρήστη (π.χ. \"Δοκίμασε να διασπάσεις την εργασία σε μικρότερα βήματα\").", "violated_organization_allowlist": "Αποτυχ<PERSON>α εκτέλεσης εργασίας: το τρέχον προφίλ παραβιάζει τις ρυθμίσεις του οργανισμού σου", "condense_failed": "Αποτυχία συμπύκνωσης του πλαισίου", "condense_not_enough_messages": "Δεν υπάρχουν αρκετά μηνύματα για συμπύκνωση πλαισίου {{prevContextTokens}} tokens. Απαιτούνται τουλάχιστον {{minimumMessageCount}} μηνύματα, αλλά διατίθενται μόνο {{messageCount}}.", "condensed_recently": "Το πλαίσιο συμπυκνώθηκε πρόσφατα· παράλειψη αυτής της προσπάθειας", "condense_handler_invalid": "Ο χειριστής API για τη συμπύκνωση του πλαισίου είναι μη έγκυρος", "condense_context_grew": "Το μέγεθος του πλαισίου αυξήθηκε από {{prevContextTokens}} σε {{newContextTokens}} κατά τη συμπύκνωση· παράλειψη αυτής της προσπάθειας", "url_timeout": "Ο ιστότοπος χρειάστηκε πολύ χρόνο για να φορτώσει (timeout). Αυτό μπορεί να οφείλεται σε αργή σύνδεση, β<PERSON><PERSON><PERSON> ιστότοπο ή ο ιστότοπος να είναι προσωρινά μη διαθέσιμος. Μπορείς να δοκιμάσεις ξανά αργότερα ή να ελέγξεις αν το URL είναι σωστό.", "url_not_found": "Η διεύθυνση του ιστότοπου δεν βρέθηκε. Παρα<PERSON><PERSON><PERSON><PERSON> έλεγξε αν το URL είναι σωστό και δοκίμασε ξανά.", "no_internet": "Δεν υπάρχει σύνδεση στο διαδίκτυο. Παρα<PERSON><PERSON><PERSON><PERSON> έλεγξε τη σύνδεση δικτύου σου και δοκίμασε ξανά.", "url_forbidden": "Η πρόσβαση σε αυτόν τον ιστότοπο απαγορεύεται. Ο ιστότοπος μπορεί να μπλοκάρει την αυτοματοποιημένη πρόσβαση ή να απαιτεί έλεγχο ταυτότητας.", "url_page_not_found": "Η σελίδα δεν βρέθηκε. Παρα<PERSON><PERSON><PERSON><PERSON> έλεγξε αν το URL είναι σωστό.", "url_fetch_failed": "Αποτυχία λήψης περιεχομένου URL: {{error}}", "url_fetch_error_with_url": "Σφάλμα λήψης περιεχομένου για {{url}}: {{error}}", "share_task_failed": "Αποτυχ<PERSON><PERSON> κοινοποίησης εργασίας. Παρακ<PERSON><PERSON><PERSON> δοκίμασε ξανά.", "share_no_active_task": "Δεν υπάρχει ενεργή εργασία για κοινοποίηση", "share_auth_required": "Απαιτ<PERSON><PERSON><PERSON><PERSON><PERSON> έλεγχος ταυτότητας. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συνδέσου για να κοινοποιήσεις εργασίες.", "share_not_enabled": "Η κοινοποίηση εργασιών δεν είναι ενεργοποιημένη για αυτόν τον οργανισμό.", "share_task_not_found": "Η εργασία δεν βρέθηκε ή η πρόσβαση απορρίφθηκε.", "mode_import_failed": "Αποτυχία εισαγωγής λειτουργίας: {{error}}", "delete_rules_folder_failed": "Αποτυχία διαγραφής φακέλου κανόνων: {{rulesFolderPath}}. Σφάλμα: {{error}}", "claudeCode": {"processExited": "Η διεργασία Claude Code τερματίστηκε με κωδικό {{exitCode}}.", "errorOutput": "Έξοδος σφάλματος: {{output}}", "processExitedWithError": "Η διεργασία Claude Code τερματίστηκε με κωδικό {{exitCode}}. Έξοδος σφάλματος: {{output}}", "stoppedWithReason": "Το <PERSON> Code σταμάτησε με αιτία: {{reason}}", "apiKeyModelPlanMismatch": "Τα κλειδιά API και τα προγράμματα συνδρομής επιτρέπουν διαφορετικά μοντέλα. Βεβαιώσου ότι το επιλεγμένο μοντέλο περιλαμβάνεται στο πρόγραμμά σου."}, "geminiCli": {"oauthLoadFailed": "Αποτυχί<PERSON> φόρτωσης OAuth διαπιστευτηρίων. Παρα<PERSON><PERSON><PERSON><PERSON> ελέγξτε την ταυτότητά σας πρώτα: {{error}}", "tokenRefreshFailed": "Αποτυχία ανανέωσης OAuth token: {{error}}", "onboardingTimeout": "Η λειτουργία onboarding έλειξε μετά από 60 δευτερόλεπτα. Παρακαλώ δοκιμάστε ξανά αργότερα.", "projectDiscoveryFailed": "Δεν ήταν δυνατή η εύρεση του ID έργου. Βεβαιωθείτε ότι έχετε ελέγξει την ταυτότητά σας με 'gemini auth'.", "rateLimitExceeded": "Υπέρβαση ορίου ρυθμού. Τα όρια του δωρεάν επιπέδου έχουν επιτευχθεί.", "badRequest": "Κακή αίτηση: {{details}}", "apiError": "Σφάλμα Gemini CLI API: {{error}}", "completionError": "Σφάλμα ολοκλήρωσης Gemini CLI: {{error}}"}}, "warnings": {"no_terminal_content": "Δεν έχει επιλεγεί περιεχόμενο τερματικού", "missing_task_files": "Τα αρχεία αυτής της εργασίας λείπουν. Θα ήθελες να την αφαιρέσεις από τη λίστα εργασιών;", "auto_import_failed": "Αποτυχία αυτόματης εισαγωγής ρυθμίσεων Kilo Code: {{error}}"}, "info": {"no_changes": "Δεν βρέθηκαν αλλαγές.", "clipboard_copy": "Το system prompt αντιγρά<PERSON>ηκ<PERSON> επιτυχώς στο πρόχειρο", "history_cleanup": "Καθαρίστηκαν {{count}} εργα<PERSON>ί<PERSON>(ες) με αρχεία που λείπουν από το ιστορικό.", "custom_storage_path_set": "Ορίστηκε προσαρμοσμένη διαδρομή αποθήκευσης: {{path}}", "default_storage_path": "Επιστροφή στη χρήση της προεπιλεγμένης διαδρομής αποθήκευσης", "settings_imported": "Οι ρυθμίσεις εισήχθησαν επιτυχώς.", "share_link_copied": "Ο σύνδεσμος κοινοποίη<PERSON>ης αντιγράφηκε στο πρόχειρο", "organization_share_link_copied": "Ο σύνδεσμος κοινοποίησης οργανισμού αντιγράφηκε στο πρόχειρο!", "public_share_link_copied": "Ο δημόσιος σύνδεσμος κοινοποίησης αντιγράφηκε στο πρόχειρο!", "image_copied_to_clipboard": "Το image data URI αντιγράφηκε στο πρόχειρο", "image_saved": "Η εικόνα αποθηκεύτηκε στο {{path}}", "auto_import_success": "Οι ρυθμίσεις <PERSON>lo Code εισήχθησαν αυτόματα από το {{filename}}", "mode_exported": "Η λειτουργία '{{mode}}' εξήχθη επιτυχώς", "mode_imported": "Η λειτουργία εισήχθη επιτυχώς"}, "answers": {"yes": "Ναι", "no": "Όχι", "cancel": "Ακύρωση", "remove": "Αφαίρεση", "keep": "Διατήρη<PERSON>η"}, "buttons": {"save": "Αποθήκευση", "edit": "Επεξεργασία"}, "tasks": {"canceled": "Σφάλμα εργασίας: Διακόπηκε και ακυρώθηκε από τον χρήστη.", "deleted": "Αποτυχία εργασίας: Διακ<PERSON><PERSON>ηκε και διαγράφηκε από τον χρήστη.", "incomplete": "Εργασία #{{taskNumber}} (Ατελή<PERSON>)", "no_messages": "Εργασία #{{taskNumber}} (<PERSON><PERSON><PERSON><PERSON><PERSON> μηνύματα)"}, "storage": {"prompt_custom_path": "Εισήγαγε προσαρμοσμένη διαδρομή αποθήκευσης ιστορικού συνομιλίας, άφησε κενό για να χρησιμοποιήσεις την προεπιλεγμένη τοποθεσία", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "Παρα<PERSON><PERSON><PERSON><PERSON> εισήγαγε μια απόλυτη διαδρομή (π.χ. D:\\KiloCodeStorage ή /home/<USER>/storage)", "enter_valid_path": "Παρα<PERSON><PERSON><PERSON><PERSON> εισήγαγε μια έγκυρη διαδρομή"}, "input": {"task_prompt": "Τι πρέπει να κάνει το Kilo <PERSON>;", "task_placeholder": "Δημιούργη<PERSON><PERSON>, βρες, ρώτα κάτι"}, "customModes": {"errors": {"yamlParseError": "Μη έγκυρο YAML στο αρχείο .k<PERSON><PERSON><PERSON><PERSON> στη γραμμή {{line}}. Παρακ<PERSON>λώ ελέγξτε για:\n• Σωστή εσοχή (χρησιμοποιήστε κενά, όχι tabs)\n• Αντίστοιχα εισαγωγικά και αγκύλες\n• Έγκυρη YAML σύνταξη", "schemaValidationError": "Μη έγκυρος μορφότυπος προσαρμοσμένων λειτουργιών στο .kilocodemodes:\n{{issues}}", "invalidFormat": "Μη έγκυρος μορφότυπος προσαρμοσμένων λειτουργιών. Παρακαλώ βεβαιωθείτε ότι οι ρυθμίσεις σας ακολουθούν τη σωστή μορφή YAML.", "updateFailed": "Αποτυχία ενημέρωσης προσαρμοσμένης λειτουργίας: {{error}}", "deleteFailed": "Αποτυχία διαγραφής προσαρμοσμένης λειτουργίας: {{error}}", "resetFailed": "Αποτυχία επαναφ<PERSON><PERSON><PERSON>ς προσαρμοσμένων λειτουργιών: {{error}}", "modeNotFound": "Σφάλμα εγγραφής: Η λειτουργία δεν βρέθηκε", "noWorkspaceForProject": "Δεν βρέθηκ<PERSON> φάκελος χώρου εργασίας για λειτουργία συγκεκριμένου έργου"}, "scope": {"project": "έργου", "global": "καθολική"}}, "mdm": {"errors": {"cloud_auth_required": "Ο οργανισμός σου απαιτε<PERSON> έλεγχο ταυτότητας <PERSON>. Παρακαλώ συνδέσου για να συνεχίσεις.", "organization_mismatch": "Πρέπει να πιστοποιηθείς με τον λογαριασμ<PERSON> Kilo <PERSON> Cloud του οργανισμού σου.", "verification_failed": "Αδυναμία επαλήθευσης του ελέγχου ταυτότητας οργανισμού."}}, "prompts": {"deleteMode": {"title": "Διαγραφή Προσαρμοσμένης Λειτουργίας", "description": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτή τη λειτουργία {{scope}}; Αυτό θα διαγράψει επίσης τον σχετικό φάκελο κανόνων στη διαδρομή: {{rulesFolderPath}}", "descriptionNoRules": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτή την προσαρμοσμένη λειτουργία;", "confirm": "Διαγραφή"}}}