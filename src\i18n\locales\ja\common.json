{"extension": {"name": "Kilo Code", "description": "コードの計画、構築、修正のためのオープンソース AI コーディング アシスタント。"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "フィードバック", "description": "あなたのフィードバックをお聞かせいただくか、あなたが経験している問題についてお手伝いします。", "githubIssues": "GitHubで問題を報告する", "githubDiscussions": "GitHubのディスカッションに参加する", "discord": "Discordコミュニティに参加する", "customerSupport": "カスタマーサポート"}, "welcome": "ようこそ、{{name}}さん！{{count}}件の通知があります。", "items": {"zero": "アイテムなし", "one": "1つのアイテム", "other": "{{count}}個のアイテム"}, "confirmation": {"reset_state": "拡張機能のすべての状態とシークレットストレージをリセットしてもよろしいですか？この操作は元に戻せません。", "delete_config_profile": "この設定プロファイルを削除してもよろしいですか？", "delete_custom_mode_with_rules": "この{scope}モードを削除してもよろしいですか？\n\nこれにより、関連するルールフォルダも次の場所で削除されます:\n{rulesFolderPath}", "delete_message": "何を削除しますか？", "edit_warning": "このメッセージを編集すると、会話内のすべての後続メッセージが削除されます。続行しますか？", "delete_just_this_message": "このメッセージのみ", "delete_this_and_subsequent": "これ以降のすべてのメッセージ", "proceed": "続行"}, "errors": {"invalid_data_uri": "データURIフォーマットが無効です", "error_copying_image": "画像のコピー中にエラーが発生しました：{{errorMessage}}", "error_saving_image": "画像の保存中にエラーが発生しました：{{errorMessage}}", "error_opening_image": "画像を開く際にエラーが発生しました：{{error}}", "could_not_open_file": "ファイルを開けませんでした：{{errorMessage}}", "could_not_open_file_generic": "ファイルを開けませんでした！", "checkpoint_timeout": "チェックポイントの復元を試みる際にタイムアウトしました。", "checkpoint_failed": "チェックポイントの復元に失敗しました。", "no_workspace": "まずプロジェクトフォルダを開いてください", "update_support_prompt": "サポートメッセージの更新に失敗しました", "reset_support_prompt": "サポートメッセージのリセットに失敗しました", "enhance_prompt": "メッセージの強化に失敗しました", "get_system_prompt": "システムメッセージの取得に失敗しました", "search_commits": "コミットの検索に失敗しました", "save_api_config": "API設定の保存に失敗しました", "create_api_config": "API設定の作成に失敗しました", "rename_api_config": "API設定の名前変更に失敗しました", "load_api_config": "API設定の読み込みに失敗しました", "delete_api_config": "API設定の削除に失敗しました", "list_api_config": "API設定リストの取得に失敗しました", "update_server_timeout": "サーバータイムアウトの更新に失敗しました", "hmr_not_running": "ローカル開発サーバーが実行されていないため、HMRは機能しません。HMRを有効にするには、拡張機能を起動する前に'npm run dev'を実行してください。", "retrieve_current_mode": "現在のモードを状態から取得する際にエラーが発生しました。", "failed_delete_repo": "関連するシャドウリポジトリまたはブランチの削除に失敗しました：{{error}}", "failed_remove_directory": "タスクディレクトリの削除に失敗しました：{{error}}", "custom_storage_path_unusable": "カスタムストレージパス \"{{path}}\" が使用できないため、デフォルトパスを使用します", "cannot_access_path": "パス {{path}} にアクセスできません：{{error}}", "settings_import_failed": "設定のインポートに失敗しました：{{error}}", "mistake_limit_guidance": "これは、モデルの思考プロセスの失敗やツールを適切に使用できないことを示している可能性があり、ユーザーのガイダンスによって軽減できます（例：「タスクをより小さなステップに分割してみてください」）。", "violated_organization_allowlist": "タスクの実行に失敗しました: 現在のプロファイルは組織の設定に違反しています", "condense_failed": "コンテキストの圧縮に失敗しました", "condense_not_enough_messages": "{{prevContextTokens}}トークンのコンテキストを圧縮するのに十分なメッセージがありません。最低{{minimumMessageCount}}件のメッセージが必要ですが、{{messageCount}}件しか利用できません。", "condensed_recently": "コンテキストは最近圧縮されました；この試行をスキップします", "condense_handler_invalid": "コンテキストを圧縮するためのAPIハンドラーが無効です", "condense_context_grew": "圧縮中にコンテキストサイズが{{prevContextTokens}}から{{newContextTokens}}に増加しました；この試行をスキップします", "url_timeout": "ウェブサイトの読み込みがタイムアウトしました。接続が遅い、ウェブサイトが重い、または一時的に利用できない可能性があります。後でもう一度試すか、URLが正しいか確認してください。", "url_not_found": "ウェブサイトのアドレスが見つかりませんでした。URLが正しいか確認してもう一度試してください。", "no_internet": "インターネット接続がありません。ネットワーク接続を確認してもう一度試してください。", "url_forbidden": "このウェブサイトへのアクセスが禁止されています。サイトが自動アクセスをブロックしているか、認証が必要な可能性があります。", "url_page_not_found": "ページが見つかりませんでした。URLが正しいか確認してください。", "url_fetch_failed": "URLコンテンツの取得に失敗しました：{{error}}", "url_fetch_error_with_url": "{{url}} のコンテンツ取得エラー：{{error}}", "share_task_failed": "タスクの共有に失敗しました", "share_no_active_task": "共有するアクティブなタスクがありません", "share_auth_required": "認証が必要です。タスクを共有するにはサインインしてください。", "share_not_enabled": "この組織ではタスク共有が有効になっていません。", "share_task_not_found": "タスクが見つからないか、アクセスが拒否されました。", "mode_import_failed": "モードのインポートに失敗しました：{{error}}", "delete_rules_folder_failed": "ルールフォルダの削除に失敗しました：{{rulesFolderPath}}。エラー：{{error}}", "claudeCode": {"processExited": "Claude Code プロセスがコード {{exitCode}} で終了しました。", "errorOutput": "エラー出力：{{output}}", "processExitedWithError": "Claude Code プロセスがコード {{exitCode}} で終了しました。エラー出力：{{output}}", "stoppedWithReason": "Claude Code が理由により停止しました：{{reason}}", "apiKeyModelPlanMismatch": "API キーとサブスクリプションプランでは異なるモデルが利用可能です。選択したモデルがプランに含まれていることを確認してください。"}, "geminiCli": {"oauthLoadFailed": "OAuth認証情報の読み込みに失敗しました。まず認証してください: {{error}}", "tokenRefreshFailed": "OAuthトークンの更新に失敗しました: {{error}}", "onboardingTimeout": "オンボーディング操作が60秒でタイムアウトしました。後でもう一度お試しください。", "projectDiscoveryFailed": "プロジェクトIDを発見できませんでした。'gemini auth'で認証されていることを確認してください。", "rateLimitExceeded": "レート制限を超過しました。無料プランの制限に達しています。", "badRequest": "不正なリクエスト: {{details}}", "apiError": "Gemini CLI APIエラー: {{error}}", "completionError": "Gemini CLI補完エラー: {{error}}"}}, "warnings": {"no_terminal_content": "選択されたターミナルコンテンツがありません", "missing_task_files": "このタスクのファイルが見つかりません。タスクリストから削除しますか？", "auto_import_failed": "Kilo Code設定の自動インポートに失敗しました：{{error}}"}, "info": {"no_changes": "変更は見つかりませんでした。", "clipboard_copy": "システムメッセージがクリップボードに正常にコピーされました", "history_cleanup": "履歴から不足ファイルのある{{count}}個のタスクをクリーンアップしました。", "custom_storage_path_set": "カスタムストレージパスが設定されました：{{path}}", "default_storage_path": "デフォルトのストレージパスに戻りました", "settings_imported": "設定が正常にインポートされました。", "auto_import_success": "Kilo Code設定が{{filename}}から自動インポートされました", "share_link_copied": "共有リンクがクリップボードにコピーされました", "image_copied_to_clipboard": "画像データURIがクリップボードにコピーされました", "image_saved": "画像を{{path}}に保存しました", "organization_share_link_copied": "組織共有リンクがクリップボードにコピーされました！", "public_share_link_copied": "公開共有リンクがクリップボードにコピーされました！", "mode_exported": "モード「{{mode}}」が正常にエクスポートされました", "mode_imported": "モードが正常にインポートされました"}, "answers": {"yes": "はい", "no": "いいえ", "remove": "削除", "keep": "保持"}, "buttons": {"save": "保存", "edit": "編集"}, "tasks": {"canceled": "タスクエラー：ユーザーによって停止およびキャンセルされました。", "deleted": "タスク失敗：ユーザーによって停止および削除されました。", "incomplete": "タスク #{{taskNumber}} (未完了)", "no_messages": "タスク #{{taskNumber}} (メッセージなし)"}, "storage": {"prompt_custom_path": "会話履歴のカスタムストレージパスを入力してください。デフォルトの場所を使用する場合は空のままにしてください", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "絶対パスを入力してください（例：D:\\KiloCodeStorage または /home/<USER>/storage）", "enter_valid_path": "有効なパスを入力してください"}, "input": {"task_prompt": "Kilo Codeにどんなことをさせますか？", "task_placeholder": "タスクをここに入力してください"}, "settings": {"providers": {"groqApiKey": "Groq APIキー", "getGroqApiKey": "Groq APIキーを取得", "claudeCode": {"pathLabel": "<PERSON>", "description": "Claude Code CLI へのオプションのパス。設定されていない場合は、デフォルトで「claude」になります。", "placeholder": "デフォルト: claude"}}}, "customModes": {"errors": {"yamlParseError": ".kilocodemodes ファイルの {{line}} 行目で無効な YAML です。以下を確認してください：\n• 正しいインデント（タブではなくスペースを使用）\n• 引用符と括弧の対応\n• 有効な YAML 構文", "schemaValidationError": ".kilocodemodes のカスタムモード形式が無効です：\n{{issues}}", "invalidFormat": "カスタムモード形式が無効です。設定が正しい YAML 形式に従っていることを確認してください。", "updateFailed": "カスタムモードの更新に失敗しました：{{error}}", "deleteFailed": "カスタムモードの削除に失敗しました：{{error}}", "resetFailed": "カスタムモードのリセットに失敗しました：{{error}}", "modeNotFound": "書き込みエラー：モードが見つかりません", "noWorkspaceForProject": "プロジェクト固有モード用のワークスペースフォルダーが見つかりません"}, "scope": {"project": "プロジェクト", "global": "グローバル"}}, "mdm": {"errors": {"cloud_auth_required": "あなたの組織では Kilo Code Cloud 認証が必要です。続行するにはサインインしてください。", "organization_mismatch": "組織の Kilo Code Cloud アカウントで認証する必要があります。", "verification_failed": "組織認証の確認ができませんでした。"}}, "prompts": {"deleteMode": {"title": "カスタムモードの削除", "description": "この{{scope}}モードを削除してもよろしいですか？これにより、関連するルールフォルダーも{{rulesFolderPath}}で削除されます", "descriptionNoRules": "このカスタムモードを削除してもよろしいですか？", "confirm": "削除"}}}