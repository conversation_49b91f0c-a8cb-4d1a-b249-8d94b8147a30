{"welcome": {"greeting": "Добро пожаловать в Kilo Code!", "introText1": "Kilo Code — это бесплатный агент кодирования ИИ с открытым исходным кодом.", "introText2": "Работает с новейшими моделями ИИ, такими как Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 и более чем 450 другими.", "introText3": "Создайте бесплатную учетную запись и получите токены на $20 для использования с любой моделью ИИ.", "ctaButton": "Создать бесплатную учетную запись", "manualModeButton": "Использовать свой API-ключ", "alreadySignedUp": "Уже зарегистрированы?", "loginText": "Войти здесь"}, "lowCreditWarning": {"addCredit": "Добавить кредит", "lowBalance": "Ваш баланс Kilo Code низкий"}, "notifications": {"toolRequest": "Запрос инструмента ожидает утверждения", "browserAction": "Действие браузера ожидает утверждения", "command": "Команда ожидает утверждения"}, "settings": {"sections": {"mcp": "MCP Серверы"}, "provider": {"account": "Аккаунт Kilo Code", "apiKey": "API-ключ Kilo Code", "login": "Войти в Kilo Code", "logout": "Выйти из Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Разрешить чтение очень больших файлов", "description": "При включении Kilo Code будет выполнять чтение очень больших файлов или выводов MCP, даже если существует высокая вероятность переполнения окна контекста (размер содержимого >80% окна контекста)."}}, "systemNotifications": {"label": "Включить системные уведомления", "description": "При включении Kilo Code будет отправлять системные уведомления о важных событиях, таких как завершение задач или ошибки.", "testButton": "Проверить уведомление", "testTitle": "Kilo Code", "testMessage": "Это тестовое уведомление от Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code хочет сократить твой разговор", "condenseConversation": "Сократить разговор"}}, "newTaskPreview": {"task": "Задача"}, "profile": {"title": "Профиль", "dashboard": "Панель управления", "logOut": "Выйти", "currentBalance": "ТЕКУЩИЙ БАЛАНС", "loading": "Загрузка..."}, "docs": "Документация", "rules": {"tooltip": "Управление правилами и рабочими процессами Kilo Code", "ariaLabel": "Правила Kilo Code", "tabs": {"rules": "Правила", "workflows": "Рабочие процессы"}, "description": {"rules": "Правила позволяют предоставить Kilo Code инструкции, которым он должен следовать во всех режимах и для всех промптов. Это постоянный способ включения контекста и предпочтений для всех разговоров в вашем рабочем пространстве или глобально.", "workflows": "Рабочие процессы - это подготовленный шаблон для разговора. Рабочие процессы позволяют определить промпты, которые вы часто используете, и могут включать серию шагов для руководства Kilo Code через повторяющиеся задачи, такие как развертывание сервиса или отправка PR. Чтобы вызвать рабочий процесс, введите", "workflowsInChat": "в чате."}, "sections": {"globalRules": "Глобальные правила", "workspaceRules": "Правила рабочего пространства", "globalWorkflows": "Глобальные рабочие процессы", "workspaceWorkflows": "Рабочие процессы рабочего пространства"}, "validation": {"invalidFileExtension": "Разрешены только расширения .md, .txt или без расширения"}, "placeholders": {"workflowName": "имя-рабочего-процесса (.md, .txt или без расширения)", "ruleName": "имя-правила (.md, .txt или без расширения)"}, "newFile": {"newWorkflowFile": "Новый файл рабочего процесса...", "newRuleFile": "Новый файл правила..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "запуск браузера", "browser_action_result": "результат действия в браузере", "browser_action": "действие в браузере", "command": "выполнение команды", "command_output": "вывод команды", "checkpoint_saved": "контрольная точка сохранена", "error": "сообщение об ошибке", "completion_result": "результат завершения", "condense_context": "сжать контекст", "followup": "дополнительный вопрос", "mcp_server_response": "Ответ сервера MCP", "tool": "использование инструмента", "reasoning": "ИИ рассуждение", "text": "Ответ ИИ", "use_mcp_server": "Использование сервера MCP", "unknown": "неизвестный тип сообщения", "user": "отзыв пользователя"}, "clickToScroll": "Посмотреть {{messageType}} (#{{messageNumber}})"}}, "userFeedback": {"editCancel": "Отмена", "send": "Отправить", "restoreAndSend": "Восстановить и отправить"}, "ideaSuggestionsBox": {"newHere": "Новичок?", "suggestionText": "<suggestionButton>Нажмите здесь</suggestionButton> для крутой идеи, затем коснитесь <sendIcon /> и посмотрите, как я творю магию!", "ideas": {"idea1": "Создайте вращающуюся, светящуюся 3D-сферу, которая реагирует на движения мыши. Сделайте приложение работающим в браузере.", "idea2": "Создайте сайт-портфолио для разработчика Python", "idea3": "Создайте макет финансового приложения в браузере. Затем проверьте, работает ли он", "idea4": "Создайте сайт-справочник, содержащий лучшие модели генерации видео ИИ на данный момент", "idea5": "Создайте генератор CSS-градиентов, который экспортирует пользовательские таблицы стилей и показывает предварительный просмотр в реальном времени", "idea6": "Создайте карточки, которые красиво раскрывают динамичный контент при наведении", "idea7": "Создайте успокаивающее интерактивное звездное поле, которое движется с жестами мыши"}}}