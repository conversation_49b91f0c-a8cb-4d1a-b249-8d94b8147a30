{"common": {"save": "Зберегти", "done": "Готово", "cancel": "Скасувати", "reset": "Скинути", "select": "Вибрати", "add": "Додати заголовок", "remove": "Видалити"}, "header": {"title": "Налаштування", "saveButtonTooltip": "Зберегти зміни", "nothingChangedTooltip": "Нічого не змінилося", "doneButtonTooltip": "Відхилити незбережені зміни та закрити панель налаштувань"}, "unsavedChangesDialog": {"title": "Незбережені зміни", "description": "Ви хочете відхилити зміни та продовжити?", "cancelButton": "Скасувати", "discardButton": "Відхилити зміни"}, "sections": {"providers": "Провайдери", "autoApprove": "Автоматичне затвердження", "browser": "Браузер", "checkpoints": "Контрольні точки", "display": "Дис<PERSON><PERSON>ей", "notifications": "Сповіщення", "contextManagement": "Контекст", "terminal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prompts": "Prompts", "experimental": "Експериментальне", "language": "Мова", "about": "Про Kilo Code"}, "prompts": {"description": "Налаштуйте допоміжні підказки, які використовуються для швидких дій, таких як покращення підказок, пояснення коду та виправлення проблем. Ці підказки допомагають Kilo Code надавати кращу допомогу для типових завдань розробки."}, "codeIndex": {"title": "Індексація кодової бази", "description": "Налаштуйте параметри індексації кодової бази для увімкнення семантичного пошуку вашого проекту. <0>Дізнатися більше</0>", "statusTitle": "Статус", "enableLabel": "Увімкнути індексацію кодової бази", "enableDescription": "Увімкнути індексацію коду для покращеного пошуку та розуміння контексту", "settingsTitle": "Налаштування індексації", "disabledMessage": "Індексація кодової бази наразі вимкнена. Увімкніть її в глобальних налаштуваннях для конфігурації параметрів індексації.", "providerLabel": "Постачальник вбудовувань", "embedderProviderLabel": "Постачальник вбудовувань", "selectProviderPlaceholder": "Виберіть постачальника", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Ключ API:", "geminiApiKeyPlaceholder": "Введіть ваш ключ API Gemini", "openaiCompatibleProvider": "Сумісний з OpenAI", "openAiKeyLabel": "Ключ API OpenAI", "openAiKeyPlaceholder": "Введіть ваш ключ API OpenAI", "openaiKeyLabel": "Ключ OpenAI:", "openAiCompatibleBaseUrlLabel": "Базовий URL", "openaiCompatibleBaseUrlLabel": "Базовий URL:", "openAiCompatibleApiKeyLabel": "Ключ API", "openaiCompatibleApiKeyLabel": "Ключ API:", "openAiCompatibleApiKeyPlaceholder": "Введіть ваш ключ API", "openAiCompatibleModelDimensionLabel": "Розмірність вбудовування:", "openAiCompatibleModelDimensionPlaceholder": "наприклад, 1536", "modelDimensionLabel": "Розмірність моделі", "openaiCompatibleModelDimensionLabel": "Розмірність вбудовування:", "openaiCompatibleModelDimensionPlaceholder": "наприклад, 1536", "openAiCompatibleModelDimensionDescription": "Розмірність вбудовування (розмір виводу) для вашої моделі. Перевірте документацію вашого постачальника для цього значення. Загальні значення: 384, 768, 1536, 3072.", "openaiCompatibleModelDimensionDescription": "Розмірність вбудовування (розмір виводу) для вашої моделі. Перевірте документацію вашого постачальника для цього значення. Загальні значення: 384, 768, 1536, 3072.", "modelLabel": "Модель", "modelPlaceholder": "Введіть назву моделі", "selectModel": "Виберіть модель", "selectModelPlaceholder": "Виберіть модель", "ollamaUrlLabel": "URL Ollama:", "ollamaBaseUrlLabel": "Базовий URL Ollama", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON><PERSON><PERSON> Qdrant:", "qdrantApiKeyLabel": "Ключ API Qdrant", "qdrantApiKeyPlaceholder": "Введіть ваш ключ API Qdrant (необов'язково)", "setupConfigLabel": "Налаштування", "advancedConfigLabel": "Розши<PERSON><PERSON>на конфігурація", "searchMinScoreLabel": "Поріг оцінки пошуку", "searchMinScoreDescription": "Мінімальна оцінка схожості (0.0-1.0), необхідна для результатів пошуку. Нижчі значення повертають більше результатів, але можуть бути менш релевантними. Вищі значення повертають менше, але більш релевантних результатів.", "searchMinScoreResetTooltip": "Скинути до значення за замовчуванням (0.4)", "searchMaxResultsLabel": "Максима<PERSON>ьна кількість результатів пошуку", "searchMaxResultsDescription": "Максимальна кількість результатів пошуку для повернення при запиті до індексу кодової бази. Вищі значення надають більше контексту, але можуть включати менш релевантні результати.", "resetToDefault": "Скинути до стандартних", "startIndexingButton": "Почати індексацію", "clearIndexDataButton": "Очистити дані індексу", "unsavedSettingsMessage": "Будь ласка, збережіть налаштування перед початком процесу індексації.", "clearDataDialog": {"title": "Ви впевнені?", "description": "Цю дію неможливо скасувати. Це назавжди видалить дані індексу вашої кодової бази.", "cancelButton": "Скасувати", "confirmButton": "Очистити дані"}, "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Не вдалося зберегти налаштування", "modelDimensions": "({{dimension}} розмірностей)", "saveSuccess": "Налаштування успішно збережено", "saving": "Збереження...", "saveSettings": "Зберегти", "indexingStatuses": {"standby": "Очікування", "indexing": "Індексація", "indexed": "Проіндексовано", "error": "Помилка"}, "close": "Закрити", "validation": {"qdrantUrlRequired": "URL Qdrant є обов'язковим", "invalidQdrantUrl": "Недійсний URL Qdrant", "invalidOllamaUrl": "Недійсний URL Ollama", "invalidBaseUrl": "Недійсний базовий URL", "openaiApiKeyRequired": "Ключ API OpenAI є обов'язковим", "modelSelectionRequired": "Вибір моделі є обов'язковим", "apiKeyRequired": "Ключ API є обов'язковим", "modelIdRequired": "ID моделі є обов'язковим", "modelDimensionRequired": "Розмірність моделі є обов'язковою", "geminiApiKeyRequired": "Ключ API Gemini є обов'язковим", "ollamaBaseUrlRequired": "Базовий URL Ollama є обов'язковим", "baseUrlRequired": "Базовий URL є обов'язковим", "modelDimensionMinValue": "Розмірність моделі повинна бути більше 0"}}, "autoApprove": {"description": "Дозволити Kilo Code автоматично виконувати операції без необхідності затвердження. Вмикайте ці налаштування, лише якщо ви повністю довіряєте ШІ та розумієте пов’язані з цим ризики безпеки.", "readOnly": {"label": "Читати", "description": "Якщо ввімкнено, Kilo Code автоматично переглядатиме вміст каталогу та читатиме файли, не вимагаючи від вас натискання кнопки «Схвалити».", "outsideWorkspace": {"label": "Включати файли поза робочим простором", "description": "Дозволити Kilo Code читати файли поза поточним робочим простором без необхідності затвердження."}}, "write": {"label": "Писати", "description": "Автоматично створювати та редагувати файли без необхідності затвердження", "delayLabel": "Затримка після запису, щоб діагностика могла виявити потенційні проблеми", "outsideWorkspace": {"label": "Включати файли поза робочим простором", "description": "Дозволити Kilo Code створювати та редагувати файли поза поточним робочим простором без необхідності затвердження."}, "protected": {"label": "Включати захищені файли", "description": "Дозволити Kilo Code створювати та редагувати захищені файли (наприклад, .kilocodeignore та файли конфігурації .kilocode/) без необхідності затвердження."}}, "browser": {"label": "Браузер", "description": "Автоматично виконувати дії браузера без необхідності затвердження. Примітка: застосовується лише тоді, коли модель підтримує використання комп’ютера"}, "retry": {"label": "Повторити", "description": "Автоматично повторювати невдалі запити API, коли сервер повертає відповідь про помилку", "delayLabel": "Затримка перед повторним запитом"}, "mcp": {"label": "MCP", "description": "Увімкнути автоматичне затвердження окремих інструментів MCP у поданні MCP Servers (потрібне як це налаштування, так і індивідуальний прапорець «Завжди дозволяти» інструмента)"}, "modeSwitch": {"label": "Режим", "description": "Автоматично перемикатися між різними режимами без необхідності затвердження"}, "subtasks": {"label": "Підзавдання", "description": "Дозволити створення та виконання підзавдань без необхідності затвердження"}, "followupQuestions": {"label": "Питання", "description": "Автоматично вибирати першу запропоновану відповідь на додаткові питання після налаштованого тайм-ауту", "timeoutLabel": "<PERSON>ас очікування перед автоматичним вибором першої відповіді"}, "execute": {"label": "Виконати", "description": "Автоматично виконувати дозволені команди терміналу без необхідності затвердження", "allowedCommands": "Дозволені команди автоматичного виконання", "allowedCommandsDescription": "Префікси команд, які можна автоматично виконати, коли ввімкнено «Завжди затверджувати операції виконання». Додайте * щоб дозволити всі команди (використовуйте з обережністю).", "commandPlaceholder": "Введіть префікс команди (наприклад, 'git ')", "addButton": "Додати"}, "showMenu": {"label": "Показувати меню автоматичного затвердження в поданні чату", "description": "Якщо ввімкнено, меню автоматичного затвердження відображатиметься внизу подання чату, що дозволить швидко отримати доступ до налаштувань автоматичного затвердження"}, "updateTodoList": {"label": "Завдання", "description": "Автоматично оновлювати список завдань без необхідності затвердження"}, "apiRequestLimit": {"title": "Макси<PERSON>а<PERSON><PERSON>на кількість запитів", "description": "Автоматично робити таку кількість запитів API, перш ніж запитувати дозвіл на продовження завдання.", "unlimited": "Необмежено"}}, "providers": {"providerDocumentation": "Документація {{provider}}", "configProfile": "Профіль конфігурації", "description": "Збережіть різні конфігурації API для швидкого перемикання між провайдерами та налаштуваннями.", "apiProvider": "Провайдер API", "model": "Модель", "nameEmpty": "Ім'я не може бути порожнім", "nameExists": "Профіль із таким іменем уже існує", "deleteProfile": "Видалити профіль", "invalidArnFormat": "Недійсний формат ARN. Перевірте приклади вище.", "enterNewName": "Введіть нове ім'я", "addProfile": "Додати профіль", "renameProfile": "Перейменувати профіль", "newProfile": "Новий профіль конфігурації", "enterProfileName": "Введіть ім'я профілю", "createProfile": "Створити профіль", "cannotDeleteOnlyProfile": "Неможливо видалити єдиний профіль", "searchPlaceholder": "Шукати профілі", "searchProviderPlaceholder": "Шукати провайдерів", "noProviderMatchFound": "Провайдерів не знайдено", "noMatchFound": "Збігів не знайдено", "vscodeLmDescription": "API мовної моделі VS Code дозволяє запускати моделі, надані іншими розширеннями VS Code (включаючи, але не обмежуючись, GitHub Copilot). Найпростіший спосіб почати роботу — встановити розширення Copilot і Copilot Chat з VS Code Marketplace.", "awsCustomArnUse": "Введіть дійсний ARN Amazon Bedrock для моделі, яку ви хочете використовувати. Приклади формату:", "awsCustomArnDesc": "Переконайтеся, що регіон у ARN відповідає вибраному вами регіону AWS вище.", "openRouterApiKey": "Ключ API OpenRouter", "getOpenRouterApiKey": "Отримати ключ API OpenRouter", "apiKeyStorageNotice": "Ключі API надійно зберігаються в Secret Storage VSCode", "glamaApiKey": "Ключ API Glama", "getGlamaApiKey": "Отримати ключ API Glama", "useCustomBaseUrl": "Використовувати власний базовий URL", "useReasoning": "Увімкнути міркування", "useHostHeader": "Використовувати власний заголовок Host", "useLegacyFormat": "Використовувати застарілий формат API OpenAI", "customHeaders": "Власні заголовки", "headerName": "Ім'я заголовка", "headerValue": "Значення заголовка", "noCustomHeaders": "Не визначено жодних власних заголовків. Натисніть кнопку + щоб додати.", "requestyApiKey": "Ключ API Requesty", "refreshModels": {"label": "Оновити моделі", "hint": "Будь ласка, знову відкрийте налаштування, щоб побачити останні моделі.", "loading": "Оновлення списку моделей...", "success": "Список моделей успішно оновлено!", "error": "Не вдалося оновити список моделей. Спробуйте ще раз."}, "getRequestyApiKey": "Отримати ключ API Requesty", "openRouterTransformsText": "Стискати підказки та ланцюжки повідомлень до розміру контексту (<a>Перетворення OpenRouter</a>)", "anthropicApiKey": "Ключ API Anthropic", "getAnthropicApiKey": "Отримати ключ API Anthropic", "anthropicUseAuthToken": "Передавати ключ API Anthropic як заголовок Authorization замість X-Api-Key", "chutesApiKey": "Ключ API Chutes", "getChutesApiKey": "Отримати ключ API Chutes", "deepSeekApiKey": "Ключ API DeepSeek", "getDeepSeekApiKey": "Отримати ключ API DeepSeek", "geminiApiKey": "Ключ API Gemini", "getGroqApiKey": "Отримати ключ API Groq", "groqApiKey": "Ключ API Groq", "getGeminiApiKey": "Отримати ключ API Gemini", "openAiApiKey": "Ключ API OpenAI", "apiKey": "Ключ API", "openAiBaseUrl": "Базовий URL", "getOpenAiApiKey": "Отримати ключ API OpenAI", "mistralApiKey": "Ключ API Mistral", "getMistralApiKey": "Отримати ключ API Mistral / Codestral", "codestralBaseUrl": "Базовий URL Codestral (необов'язково)", "codestralBaseUrlDesc": "Встановити альтернативний URL для моделі Codestral.", "xaiApiKey": "Ключ API xAI", "getXaiApiKey": "Отримати ключ API xAI", "litellmApiKey": "Ключ API LiteLLM", "litellmBaseUrl": "Базовий URL LiteLLM", "awsCredentials": "Облікові дані AWS", "awsProfile": "Профіль AWS", "awsProfileName": "Ім'я профілю AWS", "awsAccessKey": "Ключ доступу AWS", "awsSecretKey": "Секретний ключ AWS", "awsSessionToken": "Токен сесії AWS", "awsRegion": "Регіон AWS", "awsCrossRegion": "Використовувати міжрегіональний висновок", "awsBedrockVpc": {"useCustomVpcEndpoint": "Використовувати власний кінцевий пункт VPC", "vpcEndpointUrlPlaceholder": "Введіть URL кінцевого пункту VPC (необов'язково)", "examples": "Приклади:"}, "enablePromptCaching": "Увімкнути кешування підказок", "enablePromptCachingTitle": "Увімкніть кешування підказок, щоб покращити продуктивність і зменшити витрати для підтримуваних моделей.", "cacheUsageNote": "Примітка: якщо ви не бачите використання кешу, спробуйте вибрати іншу модель, а потім знову вибрати потрібну модель.", "vscodeLmModel": "Мовна модель", "vscodeLmWarning": "Примітка: це дуже експериментальна інтеграція, і підтримка провайдера може відрізнятися. Якщо ви отримуєте помилку про те, що модель не підтримується, це проблема на боці провайдера.", "googleCloudSetup": {"title": "Щоб використовувати Google Cloud Vertex AI, вам потрібно:", "step1": "1. Створити обліковий запис Google Cloud, увімкнути Vertex AI API та увімкнути потрібні моделі Claude.", "step2": "2. Встановити Google Cloud CLI та налаштувати облікові дані за замовчуванням для програми.", "step3": "3. Або створити обліковий запис служби з обліковими даними."}, "googleCloudCredentials": "Облікові дані Google Cloud", "googleCloudKeyFile": "Шлях до файлу ключа Google Cloud", "googleCloudProjectId": "Ідентифікатор проекту Google Cloud", "googleCloudRegion": "Регіон Google Cloud", "lmStudio": {"baseUrl": "Базовий URL (необов'язково)", "modelId": "Ідентифікатор моделі", "speculativeDecoding": "Увімкнути спекулятивне декодування", "draftModelId": "Ідентифікатор чернетки моделі", "draftModelDesc": "Чернетка моделі має бути з тієї ж родини моделей, щоб спекулятивне декодування працювало правильно.", "selectDraftModel": "Вибрати чернетку моделі", "noModelsFound": "Чернетки моделей не знайдено. Переконайтеся, що LM Studio запущено з увімкненим режимом сервера.", "description": "LM Studio дозволяє запускати моделі локально на вашому комп’ютері. Інструкції щодо початку роботи див. у їхньому <a>посібнику зі швидкого старту</a>. Вам також потрібно буде запустити функцію <b>локального сервера</b> LM Studio, щоб використовувати її з цим розширенням. <span>Примітка:</span> Kilo Code використовує складні підказки і найкраще працює з моделями Claude. Менш здатні моделі можуть працювати не так, як очікувалося."}, "ollama": {"baseUrl": "Базовий URL (необов'язково)", "modelId": "Ідентифікатор моделі", "description": "Ollama дозволяє запускати моделі локально на вашому комп’ютері. Інструкції щодо початку роботи див. у їхньому посібнику зі швидкого старту.", "warning": "Примітка: Kilo Code використовує складні підказки і найкраще працює з моделями Claude. Менш здатні моделі можуть працювати не так, як очікувалося."}, "unboundApiKey": "Ключ API Unbound", "getUnboundApiKey": "Отримати ключ API Unbound", "unboundRefreshModelsSuccess": "Список моделей оновлено! Тепер ви можете вибирати з останніх моделей.", "unboundInvalidApiKey": "Недійсний ключ API. Перевірте свій ключ API і спробуйте ще раз.", "humanRelay": {"description": "Ключ API не потрібен, але користувачеві потрібно допомогти скопіювати та вставити інформацію до веб-чату ШІ.", "instructions": "Під час використання з’явиться діалогове вікно, і поточне повідомлення буде автоматично скопійовано до буфера обміну. Вам потрібно вставити це до веб-версій ШІ (наприклад, ChatGPT або Claude), потім скопіювати відповідь ШІ назад у діалогове вікно та натиснути кнопку підтвердження."}, "openRouter": {"providerRouting": {"title": "Маршрутизація провайдера OpenRouter", "description": "OpenRouter направляє запити до найкращих доступних провайдерів для вашої моделі. За замовчуванням запити розподіляються між провідними провайдерами для максимального часу безвідмовної роботи. Однак ви можете вибрати конкретного провайдера для використання цієї моделі.", "learnMore": "Дізнайтеся більше про маршрутизацію провайдерів"}}, "cerebras": {"apiKey": "Ключ API Cerebras", "getApiKey": "Отримати ключ API Cerebras"}, "customModel": {"capabilities": "Налаштуйте можливості та ціни для вашої власної моделі, сумісної з OpenAI. Будьте обережні при вказуванні можливостей моделі, оскільки вони можуть вплинути на продуктивність Kilo Code.", "maxTokens": {"label": "Макс. вихідних токенів", "description": "Максимальна кількість токенів, які може генерувати модель у відповіді. (Вкажіть -1, щоб дозволити серверу встановити макс. кількість токенів.)"}, "contextWindow": {"label": "Розмір контекстного вікна", "description": "Загальна кількість токенів (вхід + вихід), які може обробити модель."}, "imageSupport": {"label": "Підтримка зображень", "description": "Чи здатна ця модель обробляти та розуміти зображення?"}, "computerUse": {"label": "Використання комп'ютера", "description": "Чи здатна ця модель взаємодіяти з браузером? (наприклад, Claude 3.7 Sonnet)."}, "promptCache": {"label": "Кешування підказок", "description": "Чи здатна ця модель кешувати підказки?"}, "pricing": {"input": {"label": "<PERSON><PERSON>на входу", "description": "Вартість за мільйон токенів у вхідному/підказковому тексті. Це впливає на вартість надсилання контексту та інструкцій до моделі."}, "output": {"label": "Ціна виходу", "description": "Вартість за мільйон токенів у відповіді моделі. Це впливає на вартість генерованого контенту та завершень."}, "cacheReads": {"label": "Ціна читання з кешу", "description": "Вартість за мільйон токенів для читання з кешу. Це ціна, що стягується при отриманні кешованої відповіді."}, "cacheWrites": {"label": "Ціна запису в кеш", "description": "Вартість за мільйон токенів для запису в кеш. Це ціна, що стягується, коли підказка кешується вперше."}}, "resetDefaults": "Скинути до стандартних"}, "rateLimitSeconds": {"label": "Обмеження швидкості", "description": "Мінімальний час між запитами API."}, "reasoningEffort": {"label": "Зусилля з міркування моделі", "high": "Високий", "medium": "Середній", "low": "Низький"}, "setReasoningLevel": "Увімкнути зусилля з міркування", "claudeCode": {"pathLabel": "Шлях до Claude Code", "description": "Необов'язковий шлях до вашого Claude Code CLI. За замовчуванням 'claude', якщо не встановлено.", "placeholder": "За замовчуванням: claude"}, "geminiCli": {"description": "Цей провайдер використовує OAuth-аутентифікацію інструменту Gemini CLI і не потребує API-ключів.", "oauthPath": "Шлях до облікових даних OAuth (необов'язково)", "oauthPathDescription": "Шлях до файлу облікових даних OAuth. Залиште порожнім для використання місця за замовчуванням (~/.gemini/oauth_creds.json).", "instructions": "Якщо ти ще не пройшов аутентифікацію, будь ласка, виконай", "instructionsContinued": "у своєму терміналі спочатку.", "setupLink": "Інструкції налаштування Gemini CLI", "requirementsTitle": "Важливі вимоги", "requirement1": "Спочатку тобі потрібно встановити інструмент Gemini CLI", "requirement2": "Потім запусти gemini у своєму терміналі і переконайся, що увійшов через Google", "requirement3": "Працює лише з особистими обліковими записами Google (не обліковими записами Google Workspace)", "requirement4": "Не використовує API-ключі - аутентифікація здійснюється через OAuth", "requirement5": "Потребує, щоб інструмент Gemini CLI був спочатку встановлений і аутентифікований", "freeAccess": "Безкоштовний доступ через OAuth-аутентифікацію"}}, "browser": {"enable": {"label": "Увімкнути інструмент браузера", "description": "Якщо ввімкнено, Kilo Code може використовувати браузер для взаємодії з веб-сайтами при використанні моделей, що підтримують використання комп’ютера. <0>Дізнатися більше</0>"}, "viewport": {"label": "Розмір оглядового вікна", "description": "Виберіть розмір оглядового вікна для взаємодії з браузером. Це впливає на те, як відображаються та взаємодіють веб-сайти.", "options": {"largeDesktop": "Великий робочий стіл (1280x800)", "smallDesktop": "Малий робочий стіл (900x600)", "tablet": "Планшет (768x1024)", "mobile": "Мобільний (360x640)"}}, "screenshotQuality": {"label": "Якість знімка екрана", "description": "Налаштуйте якість WebP знімків екрана браузера. Вищі значення забезпечують чіткіші знімки екрана, але збільшують використання токенів."}, "remote": {"label": "Використовувати віддалене підключення до браузера", "description": "Підключитися до браузера Chrome, що працює з увімкненим віддаленим налагодженням (--remote-debugging-port=9222).", "urlPlaceholder": "Власний URL (наприклад, http://localhost:9222)", "testButton": "Перевірити з'єднання", "testingButton": "Перевірка...", "instructions": "Введіть адресу хоста протоколу DevTools або залиште порожнім для автоматичного виявлення локальних екземплярів Chrome. Кнопка «Перевірити з’єднання» спробує власний URL, якщо він наданий, або автоматично виявить, якщо поле порожнє."}}, "checkpoints": {"enable": {"label": "Увімкнути автоматичні контрольні точки", "description": "Якщо ввімкнено, Kilo Code автоматично створюватиме контрольні точки під час виконання завдання, що полегшить перегляд змін або повернення до попередніх станів. <0>Дізнатися більше</0>"}}, "display": {"taskTimeline": {"label": "Показувати хронологію завдань", "description": "Відображати візуальну хронологію повідомлень завдання, розфарбовану за типом, що дозволяє швидко переглядати хід виконання завдання та повертатися до певних моментів у історії завдання."}}, "notifications": {"sound": {"label": "Увімкнути звукові ефекти", "description": "Якщо ввімкнено, Kilo Code відтворюватиме звукові ефекти для сповіщень та подій.", "volumeLabel": "Гу<PERSON>ність"}, "tts": {"label": "Увімкнути перетворення тексту на мову", "description": "Якщо ввімкнено, Kilo Code читатиме вголос свої відповіді за допомогою перетворення тексту на мову.", "speedLabel": "Швидкість"}}, "contextManagement": {"description": "Контролюйте, яка інформація включається у вікно контексту ШІ, що впливає на використання токенів та якість відповіді", "autoCondenseContextPercent": {"label": "Поріг для запуску інтелектуального стиснення контексту", "description": "Коли вікно контексту досягне цього порогу, Kilo Code автоматично його стисне."}, "condensingApiConfiguration": {"label": "Конфігурація API для стиснення контексту", "description": "Вибер<PERSON>ть, яку конфігурацію API використовувати для операцій стиснення контексту. Залиште невибраним, щоб використовувати поточну активну конфігурацію.", "useCurrentConfig": "За замовчуванням"}, "customCondensingPrompt": {"label": "Власна підказка для стиснення контексту", "description": "Налаштуйте системну підказку, що використовується для стиснення контексту. Залиште порожнім, щоб використовувати підказку за замовчуванням.", "placeholder": "Введіть тут свою власну підказку для стиснення...\n\nВи можете використовувати ту ж структуру, що й у підказці за замовчуванням:\n- Попередня розмова\n- Поточна робота\n- Ключові технічні концепції\n- Відповідні файли та код\n- Вирішення проблем\n- Незавершені завдання та наступні кроки", "reset": "Скинути до стандартних", "hint": "Порожнє = використовувати підказку за замовчуванням"}, "autoCondenseContext": {"name": "Автоматично запускати інтелектуальне стиснення контексту", "description": "Якщо ввімкнено, Kilo Code автоматично стискатиме контекст, коли буде досягнуто поріг. Якщо вимкнено, ви все одно можете вручну запустити стиснення контексту."}, "openTabs": {"label": "Обмеження контексту відкритих вкладок", "description": "Максимальна кількість відкритих вкладок VSCode, які включаються до контексту. Вищі значення надають більше контексту, але збільшують використання токенів."}, "workspaceFiles": {"label": "Обмеження контексту файлів робочого простору", "description": "Максимальна кількість файлів, які включаються до деталей поточного робочого каталогу. Вищі значення надають більше контексту, але збільшують використання токенів."}, "rooignore": {"label": "Показувати файли, що ігноруються .kilocodeignore, у списках та пошуках", "description": "Якщо ввімкнено, файли, що відповідають шаблонам у .kilocodeignore, будуть показані у списках із символом замка. Якщо вимкнено, ці файли будуть повністю приховані зі списків файлів та пошуків."}, "maxConcurrentFileReads": {"label": "Обмеження одночасного читання файлів", "description": "Максимальна кількість файлів, які інструмент 'read_file' може обробляти одночасно. Вищі значення можуть прискорити читання кількох невеликих файлів, але збільшують використання пам'яті."}, "maxReadFile": {"label": "Поріг автоматичного скорочення читання файлу", "description": "Kilo Code читає таку кількість рядків, коли модель опускає значення початку/кінця. Якщо це число менше за загальну кількість рядків у файлі, Kilo Code генерує індекс номерів рядків визначень коду. Особливі випадки: -1 наказує Kilo Code прочитати весь файл (без індексації), а 0 наказує не читати жодних рядків і надає лише індекси рядків для мінімального контексту. Нижчі значення мінімізують початкове використання контексту, дозволяючи точні послідовні читання діапазонів рядків. Явні запити на початок/кінець не обмежуються цим налаштуванням.", "lines": "ряд<PERSON><PERSON>в", "always_full_read": "Завжди читати весь файл"}, "condensingThreshold": {"label": "Поріг запуску стиснення", "selectProfile": "Налаштувати поріг для профілю", "defaultProfile": "Глобальний за замовчуванням (усі профілі)", "defaultDescription": "Коли контекст досягне цього відсотка, він буде автоматично стиснутий для всіх профілів, якщо вони не мають власних налаштувань", "profileDescription": "Власний поріг лише для цього профілю (перевизначає глобальний за замовчуванням)", "inheritDescription": "Цей профіль успадковує глобальний поріг за замовчуванням ({{threshold}}%)", "usesGlobal": "(використовує глобальний {{threshold}}%)"}}, "terminal": {"basic": {"label": "Налаштування терміналу: Базові", "description": "Базові налаштування терміналу"}, "advanced": {"label": "Налаштування терміналу: Розширені", "description": "Наступні параметри можуть вимагати перезавантаження терміналу для застосування налаштувань."}, "outputLineLimit": {"label": "Обмеження виводу терміналу", "description": "Максимальна кількість рядків, які включаються до виводу терміналу при виконанні команд. При перевищенні рядки будуть видалені з середини, заощаджуючи токени. <0>Дізнатися більше</0>"}, "shellIntegrationTimeout": {"label": "Тайм-аут інтеграції оболонки терміналу", "description": "Максимальний час очікування ініціалізації інтеграції оболонки перед виконанням команд. Для користувачів із тривалим часом запуску оболонки це значення може знадобитися збільшити, якщо ви бачите помилки «Інтеграція оболонки недоступна» в терміналі. <0>Дізнатися більше</0>"}, "shellIntegrationDisabled": {"label": "Вимкнути інтеграцію оболонки терміналу", "description": "Увімкніть це, якщо команди терміналу не працюють належним чином або ви бачите помилки «Інтеграція оболонки недоступна». Це використовує простіший метод для виконання команд, оминаючи деякі розширені функції терміналу. <0>Дізнатися більше</0>"}, "commandDelay": {"label": "Затримка команди терміналу", "description": "Затримка в мілісекундах, що додається після виконання команди. Налаштування за замовчуванням 0 повністю вимикає затримку. Це може допомогти забезпечити повний запис виводу команди в терміналах із проблемами синхронізації. У більшості терміналів це реалізовано шляхом встановлення `PROMPT_COMMAND='sleep N'`, а Powershell додає `start-sleep` у кінець кожної команди. Спочатку це було обхідним шляхом для помилки VSCode#237208 і може більше не знадобитися. <0>Дізнатися більше</0>"}, "compressProgressBar": {"label": "Стискати вивід індикатора виконання", "description": "Якщо ввімкнено, обробляє вивід терміналу з поверненням каретки (\\r) для імітації того, як реальний термінал відображатиме вміст. Це видаляє проміжні стани індикатора виконання, зберігаючи лише кінцевий стан, що економить простір контексту для більш релевантної інформації. <0>Дізнатися більше</0>"}, "powershellCounter": {"label": "Увімкнути обхідний шлях для лічильника PowerShell", "description": "Якщо ввімкнено, додає лічильник до команд PowerShell для забезпечення належного виконання команд. Це допомагає з терміналами PowerShell, у яких можуть виникати проблеми із захопленням виводу команд. <0>Дізнатися більше</0>"}, "zshClearEolMark": {"label": "Очистити позначку EOL ZSH", "description": "Якщо ввімкнено, очищає позначку кінця рядка ZSH шляхом встановлення PROMPT_EOL_MARK=''. Це запобігає проблемам з інтерпретацією виводу команд, коли вивід закінчується спеціальними символами, такими як '%'. <0>Дізнатися більше</0>"}, "zshOhMy": {"label": "Увімкнути інтеграцію Oh My Zsh", "description": "Якщо ввімкнено, встановлює ITERM_SHELL_INTEGRATION_INSTALLED=Yes для ввімкнення функцій інтеграції оболонки Oh My Zsh. Застосування цього налаштування може вимагати перезавантаження IDE. <0>Дізнатися більше</0>"}, "zshP10k": {"label": "Увімкнути інтеграцію Powerlevel10k", "description": "Якщо ввімкнено, встановлює POWERLEVEL9K_TERM_SHELL_INTEGRATION=true для ввімкнення функцій інтеграції оболонки Powerlevel10k. <0>Дізнатися більше</0>"}, "zdotdir": {"label": "Увімкнути обробку ZDOTDIR", "description": "Якщо ввімкнено, створює тимчасовий каталог для ZDOTDIR для належної обробки інтеграції оболонки zsh. Це гарантує правильну роботу інтеграції оболонки VSCode з zsh зі збереженням вашої конфігурації zsh. <0>Дізнатися більше</0>"}, "inheritEnv": {"label": "Успадковувати змінні середовища", "description": "Якщо ввімкнено, термінал успадковуватиме змінні середовища від батьківського процесу VSCode, наприклад, налаштування інтеграції оболонки, визначені профілем користувача. Це безпосередньо перемикає глобальне налаштування VSCode `terminal.integrated.inheritEnv`. <0>Дізнатися більше</0>"}}, "advanced": {"diff": {"label": "Увімкнути редагування через diffs", "description": "Якщо ввімкнено, Kilo Code зможе швидше редагувати файли та автоматично відхилятиме скорочені записи цілих файлів. Найкраще працює з останньою моделлю Claude 4 Sonnet.", "strategy": {"label": "Стратегія diff", "options": {"standard": "Ста<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (один блок)", "multiBlock": "Експериментальний: багатоблоковий diff", "unified": "Експериментальний: уніфікований diff"}, "descriptions": {"standard": "Стандартна стратегія diff застосовує зміни до одного блоку коду за раз.", "unified": "Уніфікована стратегія diff використовує кілька підходів до застосування diffs і вибирає найкращий.", "multiBlock": "Багатоблокова стратегія diff дозволяє оновлювати кілька блоків коду у файлі за один запит."}}, "matchPrecision": {"label": "Точність збігу", "description": "Цей повзунок контролює, наскільки точно секції коду повинні збігатися при застосуванні diff. Нижчі значення дозволяють більш гнучке зіставлення, але збільшують ризик неправильних замін. Використовуйте значення нижче 100% з надзвичайною обережністю."}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Використовувати експериментальну уніфіковану стратегію diff", "description": "Увімкнути експериментальну уніфіковану стратегію diff. Ця стратегія може зменшити кількість повторних спроб, спричинених помилками моделі, але може викликати несподівану поведінку або неправильні редагування. Вмикайте лише якщо ти розумієш ризики та готовий ретельно переглядати всі зміни."}, "SEARCH_AND_REPLACE": {"name": "Використовувати експериментальний інструмент пошуку та заміни", "description": "Увімкнути експериментальний інструмент пошуку та заміни, що дозволяє Kilo Code замінювати кілька екземплярів пошукового терміна за один запит."}, "INSERT_BLOCK": {"name": "Використовувати експериментальний інструмент вставки вмісту", "description": "Увімкнути експериментальний інструмент вставки вмісту, що дозволяє Kilo Code вставляти вміст у певні номери рядків без необхідності створювати diff."}, "POWER_STEERING": {"name": "Використовувати експериментальний режим \"силового керування\"", "description": "Якщо ввімкнено, Kilo Code частіше нагадуватиме моделі про деталі її поточного визначення режиму. Це призведе до сильнішого дотримання визначень ролей та власних інструкцій, але використовуватиме більше токенів на повідомлення."}, "AUTOCOMPLETE": {"name": "Використовувати експериментальну функцію \"автодоповнення\"", "description": "Якщо ввімкнено, Kilo Code надаватиме вбудовані пропозиції коду під час введення. Потрібен Kilo Code API Provider."}, "CONCURRENT_FILE_READS": {"name": "Увімкнути одночасне читання файлів", "description": "Якщо ввімкнено, Kilo Code може читати кілька файлів за один запит. Якщо вимкнено, Kilo Code повинен читати файли по одному. Вимкнення цього може допомогти при роботі з менш здатними моделями або коли ти хочеш більше контролю над доступом до файлів."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Використовувати експериментальний інструмент багатоблокового diff", "description": "Якщо ввімкнено, Kilo Code використовуватиме інструмент багатоблокового diff. Це спробує оновити кілька блоків коду у файлі за один запит."}, "MARKETPLACE": {"name": "Увімкнути Marketplace", "description": "Якщо ввімкнено, ти можеш встановлювати MCP та власні режими з Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Увімкнути одночасне редагування файлів", "description": "Якщо ввімкнено, Kilo Code може редагувати кілька файлів за один запит. Якщо вимкнено, Kilo Code повинен редагувати файли по одному. Вимкнення цього може допомогти при роботі з менш здатними моделями або коли ти хочеш більше контролю над модифікаціями файлів."}}, "promptCaching": {"label": "Вимкнути кешування підказок", "description": "Якщо відмічено, Kilo Code не використовуватиме кешування підказок для цієї моделі."}, "temperature": {"useCustom": "Використовувати власну температуру", "description": "Контролює випадковість у відповідях моделі.", "rangeDescription": "Вищі значення роблять вивід більш випадковим, нижчі значення роблять його більш детермінованим."}, "modelInfo": {"supportsImages": "Підтримує зображення", "noImages": "Не підтримує зображення", "supportsComputerUse": "Підтримує використання комп'ютера", "noComputerUse": "Не підтримує використання комп'ютера", "supportsPromptCache": "Підтримує кешування підказок", "noPromptCache": "Не підтримує кешування підказок", "maxOutput": "Макс. вихід", "inputPrice": "<PERSON><PERSON>на входу", "outputPrice": "Ціна виходу", "cacheReadsPrice": "Ціна читання кешу", "cacheWritesPrice": "Ціна запису кешу", "enableStreaming": "Увімкнути потокову передачу", "enableR1Format": "Увімкнути параметри моделі R1", "enableR1FormatTips": "Має бути ввімкнено при використанні моделей R1, таких як QWQ, щоб запобігти помилкам 400", "useAzure": "Використовувати Azure", "azureApiVersion": "Встановити версію Azure API", "gemini": {"freeRequests": "* Безкоштовно до {{count}} запитів на хвилину. Після цього оплата залежить від розміру підказки.", "pricingDetails": "Для отримання додаткової інформації див. деталі ціноутворення.", "billingEstimate": "* Оплата є приблизною - точна вартість залежить від розміру підказки."}}, "modelPicker": {"automaticFetch": "Розширення автоматично отримує останній список моделей, доступних на <serviceLink>{{serviceName}}</serviceLink>. Якщо ти не впевнений, яку модель вибрати, Kilo Code найкраще працює з <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Ти також можеш спробувати пошукати \"free\" для безкоштовних варіантів, які зараз доступні.", "label": "Модель", "searchPlaceholder": "По<PERSON><PERSON>к", "noMatchFound": "Збігів не знайдено", "useCustomModel": "Використовувати власну: {{modelId}}"}, "footer": {"feedback": "Якщо у тебе є питання або відгуки, не соромся відкрити issue на <githubLink>github.com/Kilo-Org/kilocode</githubLink> або приєднатися до <redditLink>reddit.com/r/kilocode</redditLink> або <discordLink>kilocode.ai/discord</discordLink>.", "support": "З фінансових питань звертайся до служби підтримки клієнтів за адресою <supportLink>https://kilocode.ai/support</supportLink>", "telemetry": {"label": "Дозволити звітування про помилки та використання", "description": "Допоможи покращити Kilo Code, надсилаючи дані про використання та звіти про помилки. Жоден код, підказки або особиста інформація ніколи не надсилаються. Дивись нашу політику конфіденційності для отримання додаткової інформації."}, "settings": {"import": "Імпорт", "export": "Експорт", "reset": "Скинути"}}, "thinkingBudget": {"maxTokens": "Макс. токенів", "maxThinkingTokens": "Макс. токенів роздумів"}, "validation": {"apiKey": "Ти повинен надати дійсний ключ API.", "awsRegion": "Ти повинен вибрати регіон для використання з Amazon Bedrock.", "googleCloud": "Ти повинен надати дійсний ідентифікатор проекту Google Cloud та регіон.", "modelId": "Ти повинен надати дійсний ідентифікатор моделі.", "modelSelector": "Ти повинен надати дійсний селектор моделі.", "openAi": "Ти повинен надати дійсний базовий URL, ключ API та ідентифікатор моделі.", "arn": {"invalidFormat": "Недійсний формат ARN. Будь ласка, перевір вимоги до формату.", "regionMismatch": "Попередження: регіон у твоєму ARN ({{arnRegion}}) не відповідає вибраному регіону ({{region}}). Це може спричинити проблеми з доступом. Провайдер використовуватиме регіон з ARN."}, "modelAvailability": "Ідентифіка<PERSON>ор моделі ({{modelId}}), який ти надав, недоступний. Будь ласка, вибери іншу модель.", "providerNotAllowed": "Провайдер '{{provider}}' не дозволений твоєю організацією", "modelNotAllowed": "Модель '{{model}}' не дозволена для провайдера '{{provider}}' твоєю організацією", "profileInvalid": "Цей профіль містить провайдера або модель, які не дозволені твоєю організацією"}, "placeholders": {"apiKey": "Введи ключ API...", "profileName": "Введи назву профілю", "accessKey": "Введи ключ доступу...", "secretKey": "Введи секретний ключ...", "sessionToken": "Введи токен сесії...", "credentialsJson": "Введи JSON облікових даних...", "keyFilePath": "Введи шлях до файлу ключа...", "projectId": "Введи ідентифікатор проекту...", "customArn": "Введи ARN (наприклад, arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Введи базовий URL...", "modelId": {"lmStudio": "наприклад, meta-llama-3.1-8b-instruct", "lmStudioDraft": "наприклад, lmstudio-community/llama-3.2-1b-instruct", "ollama": "наприклад, llama3.1"}, "numbers": {"maxTokens": "наприклад, 4096", "contextWindow": "наприклад, 128000", "inputPrice": "наприклад, 0.0001", "outputPrice": "наприклад, 0.0002", "cacheWritePrice": "наприклад, 0.00005"}}, "defaults": {"ollamaUrl": "За замовчуванням: http://localhost:11434", "lmStudioUrl": "За замовчуванням: http://localhost:1234", "geminiUrl": "За замовчуванням: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Власний ARN", "useCustomArn": "Використовувати власний ARN..."}, "includeMaxOutputTokens": "Включити макс. вихідні токени", "includeMaxOutputTokensDescription": "Надсилати параметр макс. вихідних токенів у запитах API. Деякі провайдери можуть не підтримувати це."}