{"greeting": "Hai, saya <PERSON>!", "introduction": "<strong>Kilo Code adalah agen coding otonom terdepan.</strong> Bersiaplah untuk merancang, coding, debug, dan meningkatkan produktivitas seperti yang belum pernah Anda lihat sebelumnya. Untuk melanjutkan, Kilo Code memerlukan API key.", "notice": "Untuk memulai, ekstensi ini memerlukan provider API.", "start": "Ayo mulai!", "routers": {"requesty": {"description": "Router LLM yang di<PERSON>", "incentive": "Kredit gratis $1"}, "openrouter": {"description": "Interface terpadu untuk LLM"}}, "chooseProvider": "Untuk melakukan k<PERSON>, Kilo Code membutuhkan API key.", "startRouter": "<PERSON><PERSON> menggunakan Router LLM:", "startCustom": "Atau Anda dapat menggunakan API key Anda sendiri:", "telemetry": {"title": "Bantu Tingkatkan Kilo Code", "anonymousTelemetry": "Kirim data error dan penggunaan untuk membantu kami memperbaiki bug dan meningkatkan ekstensi. Tidak ada kode, prompts, atau informasi pribadi yang pernah dikirim.", "changeSettings": "<PERSON>a selalu dapat mengubah ini di bagian bawah <settingsLink>pengaturan</settingsLink>", "settings": "pengat<PERSON><PERSON>", "allow": "Izinkan", "deny": "<PERSON><PERSON>"}, "importSettings": "<PERSON><PERSON><PERSON>"}