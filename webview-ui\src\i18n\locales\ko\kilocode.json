{"welcome": {"greeting": "Kilo Code에 오신 것을 환영합니다!", "introText1": "Kilo Code는 무료 오픈소스 AI 코딩 에이전트입니다.", "introText2": "Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 및 450개 이상의 최신 AI 모델과 함께 작동합니다.", "introText3": "무료 계정을 만들고 모든 AI 모델에서 사용할 수 있는 $20 상당의 토큰을 받으세요.", "ctaButton": "무료 계정 만들기", "manualModeButton": "내 API 키 사용하기", "alreadySignedUp": "이미 가입하셨나요?", "loginText": "여기에서 로그인"}, "lowCreditWarning": {"addCredit": "크레딧 추가", "lowBalance": "Kilo Code 잔액이 부족합니다"}, "notifications": {"toolRequest": "도구 요청 승인 대기 중", "browserAction": "브라우저 작업 승인 대기 중", "command": "명령 승인 대기 중"}, "settings": {"sections": {"mcp": "MCP 서버"}, "provider": {"account": "Kilo Code 계정", "apiKey": "Kilo Code API 키", "login": "Kilo Code에 로그인", "logout": "Kilo Code에서 로그아웃"}, "contextManagement": {"allowVeryLargeReads": {"label": "매우 큰 파일 읽기 허용", "description": "활성화되면 Kilo Code는 컨텍스트 창을 오버플로할 가능성이 높더라도 매우 큰 파일이나 MCP 출력 읽기를 수행합니다 (콘텐츠 크기가 컨텍스트 창의 80% 이상)."}}, "systemNotifications": {"label": "시스템 알림 활성화", "description": "활성화되면 Kilo Code는 작업 완료나 오류와 같은 중요한 이벤트에 대한 시스템 알림을 보냅니다.", "testButton": "알림 테스트", "testTitle": "Kilo Code", "testMessage": "이것은 Kilo Code의 테스트 알림입니다."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code가 대화를 요약하고자 합니다", "condenseConversation": "대화 요약하기"}}, "newTaskPreview": {"task": "작업"}, "profile": {"title": "프로필", "dashboard": "대시보드", "logOut": "로그아웃", "currentBalance": "현재 잔액", "loading": "로딩 중..."}, "docs": "문서", "rules": {"tooltip": "Kilo Code 규칙 및 워크플로우 관리", "ariaLabel": "Kilo Code 규칙", "tabs": {"rules": "규칙", "workflows": "워크플로우"}, "description": {"rules": "규칙을 사용하면 Kilo Code가 모든 모드와 모든 프롬프트에서 따라야 할 지침을 제공할 수 있습니다. 이는 워크스페이스나 전역적으로 모든 대화에 컨텍스트와 기본 설정을 포함하는 지속적인 방법입니다.", "workflows": "워크플로우는 대화를 위한 준비된 템플릿입니다. 워크플로우를 사용하면 자주 사용하는 프롬프트를 정의할 수 있으며, 서비스 배포나 PR 제출과 같은 반복적인 작업을 통해 Kilo Code를 안내하는 일련의 단계를 포함할 수 있습니다. 워크플로우를 호출하려면 다음과 같이 입력하세요", "workflowsInChat": "채팅에서."}, "sections": {"globalRules": "전역 규칙", "workspaceRules": "워크스페이스 규칙", "globalWorkflows": "전역 워크플로우", "workspaceWorkflows": "워크스페이스 워크플로우"}, "validation": {"invalidFileExtension": ".md, .txt 또는 확장자 없음만 허용됩니다"}, "placeholders": {"workflowName": "워크플로우-이름 (.md, .txt 또는 확장자 없음)", "ruleName": "규칙-이름 (.md, .txt 또는 확장자 없음)"}, "newFile": {"newWorkflowFile": "새 워크플로우 파일...", "newRuleFile": "새 규칙 파일..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "{{messageType}} (#{{messageNumber}}) 보기", "messageTypes": {"browser_action": "브라우저 작업", "checkpoint_saved": "체크포인트 저장됨", "browser_action_launch": "브라우저 실행", "browser_action_result": "브라우저 작업 결과", "command_output": "명령어 출력", "command": "명령어 실행", "condense_context": "컨텍스트 요약", "followup": "후속 질문", "error": "오류 메시지", "completion_result": "완성 결과", "mcp_server_response": "MCP 서버 응답", "text": "AI 응답", "tool": "도구 사용", "unknown": "알 수 없는 메시지 유형", "reasoning": "AI 추론", "use_mcp_server": "MCP 서버 사용량", "user": "사용자 피드백"}}}, "userFeedback": {"editCancel": "취소", "send": "보내기", "restoreAndSend": "복원 및 보내기"}, "ideaSuggestionsBox": {"newHere": "처음이신가요?", "suggestionText": "멋진 아이디어를 위해 <suggestionButton>여기를 클릭</suggestionButton>하세요. 그다음 <sendIcon />를 탭하고 제가 마법을 부리는 것을 보세요!", "ideas": {"idea1": "마우스 움직임에 반응하는 회전하고 빛나는 3D 구체를 만드세요. 앱이 브라우저에서 작동하도록 하세요.", "idea2": "Python 소프트웨어 개발자를 위한 포트폴리오 웹사이트 만들기", "idea3": "브라우저에서 금융 앱 모형을 만드세요. 그다음 작동하는지 테스트하세요", "idea4": "현재 최고의 AI 비디오 생성 모델들을 포함한 디렉터리 웹사이트 만들기", "idea5": "사용자 정의 스타일시트를 내보내고 실시간 미리보기를 보여주는 CSS 그라디언트 생성기 만들기", "idea6": "마우스를 올렸을 때 동적 콘텐츠를 아름답게 보여주는 카드 생성하기", "idea7": "마우스 제스처에 따라 움직이는 차분한 인터랙티브 별빛 만들기"}}}