{"title": "Kilo Code Marketplace", "tabs": {"installed": "Εγκατεστημένα", "settings": "Ρυθμίσεις", "browse": "Περιήγηση"}, "done": "Ολοκληρώθηκε", "refresh": "Ανανέωση", "filters": {"search": {"placeholder": "Αναζήτηση στοιχείων marketplace...", "placeholderMcp": "Αναζήτηση MCP...", "placeholderMode": "Αναζήτηση Modes..."}, "type": {"label": "Φιλτράρισμα κατά τύπο:", "all": "Όλοι οι τύποι", "mode": "Mode", "mcpServer": "MCP Server"}, "sort": {"label": "Ταξινόμηση κατά:", "name": "Όνομα", "author": "Δημιου<PERSON><PERSON><PERSON>ς", "lastUpdated": "Τελευταία Ενημέρωση"}, "tags": {"label": "Φιλτράρισμα κατά ετικέτες:", "clear": "Καθα<PERSON>ισ<PERSON><PERSON><PERSON> ετικετών", "placeholder": "Πληκτρολόγησε για αναζήτηση και επιλογή ετικετών...", "noResults": "Δεν βρέθηκαν ετικέτες που ταιριάζουν", "selected": "Εμφάνιση στοιχείων με οποιαδήποτε από τις επιλεγμένες ετικέτες", "clickToFilter": "Κάνε κλικ στις ετικέτες για φιλτράρισμα στοιχείων"}, "none": "Κανένα"}, "type-group": {"modes": "Modes", "mcps": "MCP Servers"}, "items": {"empty": {"noItems": "Δεν βρέθηκαν στοιχεία marketplace", "withFilters": "Δοκίμασε να προσαρμόσεις τα φίλτρα σου", "noSources": "Δοκίμασε να προσθέσεις μια πηγή στην καρτέλα Sources", "adjustFilters": "Δοκίμασε να προσαρμόσεις τα φίλτρα ή τους όρους αναζήτησής σου", "clearAllFilters": "Καθα<PERSON>ισ<PERSON><PERSON>ς όλων των φίλτρων"}, "count": "Βρέθηκαν {{count}} στοιχεία", "components": "{{count}} στοιχεία", "matched": "{{count}} ταιριάζουν", "refresh": {"button": "Ανανέωση", "refreshing": "Ανανέωση...", "mayTakeMoment": "Αυτό μπορεί να πάρει λίγο χρόνο."}, "card": {"by": "από {{author}}", "from": "από {{source}}", "install": "Εγκατάσταση", "installProject": "Εγκατάσταση", "installGlobal": "Εγκατ<PERSON><PERSON>τα<PERSON>η (Καθολική)", "remove": "Αφαίρεση", "removeProject": "Αφαίρεση", "removeGlobal": "Αφαίρεση (Καθολική)", "viewSource": "Προβολή", "viewOnSource": "Προβολή στο {{source}}", "noWorkspaceTooltip": "Άνοιξε ένα workspace για να εγκαταστήσεις στοιχεία marketplace", "installed": "Εγκατεστημένο", "removeProjectTooltip": "Αφαίρεση από το τρέχον έργο", "removeGlobalTooltip": "Αφαίρεση από την καθολική διαμόρφωση", "actionsMenuLabel": "Περισσότερες ενέργειες"}}, "install": {"title": "Εγκατάσταση {{name}}", "titleMode": "Εγκατάσταση {{name}} Mode", "titleMcp": "Εγκατάσταση {{name}} MCP", "scope": "<PERSON><PERSON><PERSON><PERSON>ης", "project": "Έργο (τρέχον workspace)", "global": "Καθολική (όλα τα workspaces)", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τασης", "prerequisites": "Προαπαιτούμενα", "configuration": "Διαμόρφωση", "configurationDescription": "Διαμόρφωσε τις παραμέτρους που απαιτούνται για αυτόν τον MCP server", "button": "Εγκατάσταση", "successTitle": "Το {{name}} Εγκαταστάθηκε", "successDescription": "Η εγκατάσταση ολοκληρώθηκε επιτυχώς", "installed": "Εγκαταστάθηκε επιτυχώς!", "whatNextMcp": "Μπορείς τώρα να διαμορφώσεις και να χρησιμοποιήσεις αυτόν τον MCP server. <PERSON>άν<PERSON> κλικ στο εικονίδιο MCP στην πλευρική μπάρα για να αλλάξεις καρτέλα.", "whatNextMode": "Μπορείς τώρα να χρησιμοποιήσεις αυτό το mode. Κάνε κλικ στο εικονίδιο Modes στην πλευρική μπάρα για να αλλάξεις καρτέλα.", "done": "Ολοκληρώθηκε", "goToMcp": "Μετάβαση στην Καρτέλα MCP", "goToModes": "Μετάβαση στην Καρτέλα Modes", "moreInfoMcp": "Προβολή τεκμηρίωσης {{name}} MCP", "validationRequired": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δώσε τιμή για {{paramName}}"}, "sources": {"title": "Διαμόρφωση Πηγών Marketplace", "description": "Πρόσθεσε Git repositories που περιέχουν στοιχεία marketplace. Αυτά τα repositories θα ανακτηθούν κατά την περιήγηση στο marketplace.", "add": {"title": "Προσθήκη Νέας Πηγής", "urlPlaceholder": "Git repository URL (π.χ., https://github.com/username/repo)", "urlFormats": "Υποστηριζόμενες μορφές: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git), ή Git protocol (git://github.com/username/repo.git)", "namePlaceholder": "Όνομα εμφάνισης (μέγ. 20 χαρακτήρες)", "button": "Προσθήκη Πηγής"}, "current": {"title": "Τρέχουσες <PERSON>η<PERSON>ς", "empty": "Δεν έχουν διαμορφωθεί πηγές. Πρόσθεσε μια πηγή για να ξεκινήσεις.", "refresh": "Ανανέωση αυτής της πηγής", "remove": "Αφαίρεση πηγής"}, "errors": {"emptyUrl": "Το URL δεν μπορεί να είναι κενό", "invalidUrl": "Μη έγκυρη μορφή URL", "nonVisibleChars": "Το URL περιέχει μη ορατούς χαρακτήρες εκτός από κενά", "invalidGitUrl": "Το URL πρέπει να είναι έγκυρο Git repository URL (π.χ., https://github.com/username/repo)", "duplicateUrl": "Αυτό το URL υπάρχει ήδη στη λίστα (χωρίς διάκριση πεζών-κεφαλα<PERSON>ων και κενών)", "nameTooLong": "Το όνομα πρέπει να είναι 20 χαρακτήρες ή λιγότερο", "nonVisibleCharsName": "Το όνομα περιέχει μη ορατούς χαρακτήρες εκτός από κενά", "duplicateName": "Αυτό το όνομα χρησιμοποιείται ήδη (χωρίς διάκριση πεζών-κεφα<PERSON><PERSON><PERSON>ων και κενών)", "emojiName": "Οι χαρακτήρες emoji μπορεί να προκαλέσουν προβλήματα εμφάνισης", "maxSources": "Επιτρέπονται μέγιστο {{max}} πηγές"}}, "footer": {"issueText": "Βρήκες πρόβλημα με κάποιο στοιχείο marketplace ή έχεις προτάσεις για νέα; <0>Άνοιξε ένα GitHub issue</0> για να μας ενημερώσεις!"}}