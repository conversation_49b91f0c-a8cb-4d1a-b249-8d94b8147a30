{"number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "Gostaríamos de ouvir seu feedback ou ajudar com quaisquer problemas que você esteja enfrentando.", "githubIssues": "Reportar um problema no GitHub", "githubDiscussions": "Participar das discussões no GitHub", "discord": "Junte-se à nossa comunidade no Discord", "customerSupport": "Suporte ao Cliente"}, "answers": {"yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Remover", "keep": "<PERSON><PERSON>"}, "ui": {"search_placeholder": "Pesquisar..."}, "mermaid": {"loading": "Gerando diagrama mermaid...", "render_error": "Não foi possível render<PERSON>r o diagrama", "fixing_syntax": "Corrigindo sintaxe do Mermaid...", "fix_syntax_button": "Corrigir sintaxe com IA", "original_code": "Código original:", "errors": {"unknown_syntax": "Erro de sintaxe desconhecido", "fix_timeout": "Tempo limite da solicitação de correção LLM esgotado", "fix_failed": "Falha na correção LLM", "fix_attempts": "Falha ao corrigir sintaxe após {{attempts}} tentativas. Último erro: {{error}}", "no_fix_provided": "LLM falhou em fornecer uma correção", "fix_request_failed": "Falha na solicitação de correção"}, "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "Reduzir", "copy": "Copiar", "save": "<PERSON><PERSON> imagem", "viewCode": "<PERSON>er código", "viewDiagram": "Ver diagrama", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Código"}, "feedback": {"imageCopied": "Imagem copiada para a área de transferência", "copyError": "Erro ao copiar imagem"}}, "file": {"errors": {"invalidDataUri": "Formato de URI de dados inválido", "copyingImage": "Erro ao copiar imagem: {{error}}", "openingImage": "Erro ao abrir imagem: {{error}}", "pathNotExists": "Caminho não existe: {{path}}", "couldNotOpen": "Não foi possível abrir o arquivo: {{error}}", "couldNotOpenGeneric": "Não foi possível abrir o arquivo!"}, "success": {"imageDataUriCopied": "URI de dados da imagem copiada para a área de transferência"}}}