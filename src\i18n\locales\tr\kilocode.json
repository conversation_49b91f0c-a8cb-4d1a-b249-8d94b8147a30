{"info": {"settings_imported": "Ayarlar başarıyla içe aktarıldı."}, "userFeedback": {"message_update_failed": "<PERSON><PERSON>", "no_checkpoint_found": "Bu mesajdan önce kontrol noktası bulunamadı", "message_updated": "<PERSON><PERSON>"}, "lowCreditWarning": {"title": "Düşük Kredi Uyarısı!", "message": "Ücretsiz kredilerle yükleme yapabilir veya daha fazla satın alabilir misiniz kontrol edin!"}, "notLoggedInError": "İstek tamamlanamıyor, se<PERSON><PERSON>layıcıya bağlı olduğundan ve oturum açtığından emin ol.\n\n{{error}}", "rules": {"actions": {"delete": "Sil", "confirmDelete": "{{filename}} dosyasını silmek istediğinizden emin misiniz?", "deleted": "{{filename}} silindi"}, "errors": {"noWorkspaceFound": "Çalışma alanı klasörü bulunamadı", "fileAlreadyExists": "{{filename}} dos<PERSON>ı zaten mevcut", "failedToCreateRuleFile": "Kural dosyası oluşturulamadı.", "failedToDeleteRuleFile": "Kural dosyası silinemedi."}, "templates": {"workflow": {"description": "İş akışı açıklaması buraya...", "stepsHeader": "## <PERSON><PERSON><PERSON><PERSON>", "step1": "Adım 1", "step2": "Adım 2"}, "rule": {"description": "Kural açıklaması buraya...", "guidelinesHeader": "## <PERSON><PERSON><PERSON><PERSON><PERSON>", "guideline1": "Yönerge 1", "guideline2": "Yönerge 2"}}}, "commitMessage": {"activated": "Kilo Code taahhüt mesajı oluşturucu etkinleştirildi", "gitNotFound": "⚠️ Git deposu bulunamadı veya git kullanılabilir değil", "gitInitError": "⚠️ Git ba<PERSON><PERSON>ma hatası: {{error}}", "generating": "Kilo: İşlem mesajı oluşturuluyor...", "noChanges": "Kilo: <PERSON><PERSON><PERSON> değişiklik bulunamadı", "generated": "Kilo: İşleme mesajı oluşturuldu!", "generationFailed": "Kilo: <PERSON><PERSON><PERSON><PERSON>t mesajı oluşturulamadı: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> değişiklikleri kullanarak mesaj oluşturuluyor", "providerRegistered": "Kilo: <PERSON><PERSON><PERSON>e mesaj <PERSON>ıcısı kaydedildi", "activationFailed": "Kilo: <PERSON><PERSON>ştirilemedi: {{error}}"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Otomatik <PERSON>lama", "disabled": "$(circle-slash) <PERSON><PERSON>atik <PERSON>a", "tooltip": {"basic": "Kilo Code Otomatik Tamamlama", "disabled": "Kilo Code Otomatik Tamamlama (devre dışı)", "tokenError": "Otomatik tamamlama özelliğini kullanmak için geçerli bir belirteç ayarlanmalıdır", "sessionTotal": "<PERSON><PERSON><PERSON> toplam maliyeti:", "lastCompletion": "Son tamamlama:", "model": "Model:"}, "warning": "$(warning) <PERSON>lo Otomatik <PERSON>lama", "cost": {"zero": "0,00 ₺", "lessThanCent": "<0,01$"}}, "toggleMessage": "<PERSON><PERSON> {{status}}"}, "ghost": {"progress": {"analyzing": "Kodunuz analiz ediliyor...", "generating": "Düzenleme önerileri oluşturuluyor...", "processing": "Önerilen düzeltmeler işleniyor...", "showing": "Önerilen düzenlemeler görüntüleniyor...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Hızlı Görev", "placeholder": "<PERSON><PERSON><PERSON><PERSON>, 'bu fonksi<PERSON>u daha verimli olacak şekilde ye<PERSON> düzenley<PERSON>'"}, "commands": {"generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON><PERSON>lemeleri <PERSON>", "displaySuggestions": "Önerilen <PERSON>lemele<PERSON>", "cancelSuggestions": "Önerilen Düzenlemeleri İptal Et", "applyCurrentSuggestion": "Mevcut Önerilen Düzenlemeyi Uygula", "category": "Kilo Code", "applyAllSuggestions": "Önerilen Düzenlemelerin Tümünü Uygula", "promptCodeSuggestion": "Hızlı Görev İsteği"}, "chatParticipant": {"fullName": "Kilo <PERSON> Ajan<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Hızlı görevler ve önerilen düzenlemeler konusunda size yardımcı olabilirim."}, "codeAction": {"title": "Kilo Code: <PERSON><PERSON><PERSON><PERSON>"}, "messages": {"provideCodeSuggestions": "<PERSON>d ö<PERSON>ileri sunuluyor..."}}}