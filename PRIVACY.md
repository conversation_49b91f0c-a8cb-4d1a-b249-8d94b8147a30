# Kilo Code Privacy Policy

**Last Updated: March 7th, 2025**

Kilo Code respects your privacy and is committed to transparency about how we handle your data. Below is a simple breakdown of where key pieces of data go—and, importantly, where they don’t.

### **Where Your Data Goes (And Where It Doesn’t)**

- **Code & Files**: Kilo Code accesses files on your local machine when needed for AI-assisted features. When you send commands to Kilo Code, relevant files may be transmitted to your chosen AI model provider (e.g., OpenAI, Anthropic, OpenRouter) to generate responses. We do not have access to this data, but AI providers may store it per their privacy policies.
- **Commands**: Any commands executed through Kilo Code happen on your local environment. However, when you use AI-powered features, the relevant code and context from your commands may be transmitted to your chosen AI model provider (e.g., OpenAI, Anthropic, OpenRouter) to generate responses. We do not have access to or store this data, but AI providers may process it per their privacy policies.
- **Prompts & AI Requests**: When you use AI-powered features, your prompts and relevant project context are sent to your chosen AI model provider (e.g., OpenAI, Anthropic, OpenRouter) to generate responses. We do not store or process this data. These AI providers have their own privacy policies and may store data per their terms of service.
- **API Keys & Credentials**: If you enter an API key (e.g., to connect an AI model), it is stored locally on your device and never sent to us or any third party, except the provider you have chosen.

### **Your Choices & Control**

- You can run models locally to prevent data being sent to third-parties.

### **Security & Updates**

We take reasonable measures to secure your data, but no system is 100% secure. If our privacy policy changes, we will notify you within the extension.

### **Contact Us**

For any privacy-related questions, soon you can reach out to us at <EMAIL>.

---

By using Kilo Code, you agree to this Privacy Policy.
