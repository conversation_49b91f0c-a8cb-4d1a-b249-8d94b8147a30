{"info": {"settings_imported": "Configuración importada correctamente."}, "userFeedback": {"message_update_failed": "Error al actualizar el mensaje", "no_checkpoint_found": "No se encontró ningún punto de control antes de este mensaje", "message_updated": "Mensaje actualizado con éxito"}, "lowCreditWarning": {"title": "¡Advertencia de Crédito Bajo!", "message": "¡Comprueba si puedes recargar con créditos gratuitos o comprar más!"}, "notLoggedInError": "No se puede completar la solicitud, asegúrate de que estás conectado y has iniciado sesión con el proveedor seleccionado.\n\n{{error}}", "rules": {"actions": {"delete": "Eliminar", "confirmDelete": "¿Estás seguro de que quieres eliminar {{filename}}?", "deleted": "{{filename}} eliminado"}, "errors": {"noWorkspaceFound": "No se encontró carpeta de espacio de trabajo", "fileAlreadyExists": "El archivo {{filename}} ya existe", "failedToCreateRuleFile": "Error al crear el archivo de regla.", "failedToDeleteRuleFile": "Error al eliminar el archivo de regla."}, "templates": {"workflow": {"description": "Descripción del flujo de trabajo aquí...", "stepsHeader": "## Pasos", "step1": "Paso 1", "step2": "Paso 2"}, "rule": {"description": "Descripción de la regla aquí...", "guidelinesHeader": "## Directrices", "guideline1": "Directriz 1", "guideline2": "Directriz 2"}}}, "commitMessage": {"activated": "Generador de mensajes de commit de Kilo Code activado", "gitNotFound": "⚠️ Repositorio Git no encontrado o git no disponible", "gitInitError": "⚠️ Error de inicialización de Git: {{error}}", "generating": "Kilo: Generando mensaje de confirmación...", "noChanges": "Kilo: No se encontraron cambios para analizar", "generated": "<PERSON><PERSON>: ¡Mensaje de commit generado!", "generationFailed": "Kilo: Error al generar mensaje de confirmación: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON>rando mensaje usando cambios no preparados", "providerRegistered": "Kilo: Proveedor de mensajes de confirmación registrado", "activationFailed": "Kilo: Error al activar el generador de mensajes: {{error}}"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Autocompletado", "disabled": "$(circle-slash) Kilo Autocompletado", "warning": "$(warning) Kilo Autocompletado", "tooltip": {"disabled": "Kilo Code Autocompletado (desactivado)", "basic": "Kilo Code Autocompletado", "sessionTotal": "Costo total de la sesión:", "lastCompletion": "Última finalización:", "tokenError": "Se debe establecer un token válido para usar el autocompletado", "model": "Modelo:"}, "cost": {"zero": "$0,00", "lessThanCent": "<$0.01"}}, "toggleMessage": "<PERSON><PERSON> Autocompletado {{status}}"}, "ghost": {"progress": {"analyzing": "Analizando su código...", "processing": "Procesando ediciones sugeridas...", "generating": "Generando sugerencias de edición...", "showing": "Mostrando ediciones sugeridas...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON>", "placeholder": "p. ej., 'refactorizar esta función para que sea más eficiente'"}, "commands": {"displaySuggestions": "Mostrar Ediciones Sugeridas", "cancelSuggestions": "Cancelar Ediciones Sugeridas", "generateSuggestions": "Kilo Code: Generar Ediciones Sugeridas", "applyCurrentSuggestion": "Aplicar edición sugerida actual", "promptCodeSuggestion": "<PERSON><PERSON>", "category": "Kilo Code", "applyAllSuggestions": "Aplicar todas las ediciones sugeridas"}, "chatParticipant": {"fullName": "<PERSON><PERSON>e", "description": "Puedo ayudarte con tareas rápidas y ediciones sugeridas.", "name": "<PERSON><PERSON>"}, "codeAction": {"title": "Kilo Code: Ediciones sugeridas"}, "messages": {"provideCodeSuggestions": "Proporcionando sugerencias de código..."}}}