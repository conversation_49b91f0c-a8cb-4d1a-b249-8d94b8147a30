{"welcome": {"greeting": "Maligayang pagdating sa Kilo Code!", "introText1": "Ang Kilo Code ay isang libreng, open source AI coding agent.", "introText2": "Gumagana ito sa pinakabagong AI models tulad ng Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, at higit 450 pang iba.", "introText3": "Gumawa ng libreng account at makakuha ng $20 na tokens para gamitin sa kahit anong AI model.", "ctaButton": "Gumawa ng libreng account", "manualModeButton": "Gamitin ang sarili mong API key", "alreadySignedUp": "Nag-sign up ka na ba?", "loginText": "Mag-log in dito"}, "lowCreditWarning": {"addCredit": "Magdagdag ng Credit", "lowBalance": "Mababa na ang iyong Kilo Code balance"}, "notifications": {"toolRequest": "Naghihintay ng pag-apruba ang tool request", "browserAction": "Naghihintay ng pag-apruba ang browser action", "command": "Naghihintay ng pag-apruba ang command"}, "settings": {"sections": {"mcp": "MCP Servers"}, "provider": {"account": "Account sa <PERSON><PERSON>", "apiKey": "Kilo Code API Key", "login": "Mag-log in sa Kilo Code", "logout": "Mag-log out mula sa Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Payagan ang pagbasa ng napakalaking files", "description": "Kapag naka-enable, magsasagawa ang Kilo Code ng pagbasa ng napakalaking files o MCP outputs kahit na may mataas na posibilidad na mag-overflow ang context window (laki ng content >80% ng context window)."}}, "systemNotifications": {"label": "Paganahin ang mga System Notification", "description": "<PERSON><PERSON><PERSON> naka-enable, magpapadala ang Kilo Code ng mga system notification para sa mahahalagang pangyayari tulad ng pagkumpleto ng gawain o mga error.", "testButton": "Subukan ang Notification", "testTitle": "Kilo Code", "testMessage": "Ito ay isang test notification mula sa Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Gusto ng Kilo Code na paikliin ang iyong pag-uusap", "condenseConversation": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>"}}, "newTaskPreview": {"task": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Profile", "dashboard": "Dashboard", "logOut": "Mag-log out", "currentBalance": "KASALUKUYANG BALANSE", "loading": "Nag-lo-load..."}, "docs": "Docs", "rules": {"tooltip": "Pamahalaan ang Kilo Code Rules at Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "Rules", "workflows": "Workflows"}, "description": {"rules": "Ang Rules ay nagbibigay-daan sa iyo na magbigay sa Kilo Code ng mga tagubilin na dapat nitong sundin sa lahat ng modes at para sa lahat ng prompts. Ito ay isang patuloy na paraan upang isama ang konteksto at mga kagustuhan para sa lahat ng pag-uusap sa iyong workspace o globally.", "workflows": "Ang Workflows ay isang nakahandang template para sa isang pag-uusap. Ang Workflows ay maaaring magbigay-daan sa iyo na tukuyin ang mga prompts na madalas mong ginagamit, at maaaring magsama ng serye ng mga hakbang upang gabayan ang Kilo Code sa mga paulit-ulit na gawain, tulad ng pag-deploy ng service o pag-submit ng PR. Para gamitin ang workflow, i-type", "workflowsInChat": "sa chat."}, "sections": {"globalRules": "Global Rules", "workspaceRules": "Workspace Rules", "globalWorkflows": "Global Workflows", "workspaceWorkflows": "Workspace Workflows"}, "validation": {"invalidFileExtension": "Tanging .md, .txt, o walang file extension ang pinapayagan"}, "placeholders": {"workflowName": "workflow-name (.md, .txt, o walang extension)", "ruleName": "rule-name (.md, .txt, o walang extension)"}, "newFile": {"newWorkflowFile": "Bagong workflow file...", "newRuleFile": "Bagong rule file..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Tingnan ang {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "paglunsad ng browser", "browser_action_result": "resulta ng browser action", "browser_action": "browser action", "checkpoint_saved": "na-save na checkpoint", "command": "pagpapatupad ng command", "command_output": "output ng command", "completion_result": "resulta ng pagkumpleto", "condense_context": "paik<PERSON><PERSON> ang konteksto", "error": "mensahe ng error", "followup": "follow-up na tanong", "mcp_server_response": "tugon ng MCP server", "reasoning": "pag-iisip ng AI", "text": "tugon ng AI", "tool": "paggamit ng tool", "unknown": "hindi kilalang uri ng mensahe", "use_mcp_server": "paggamit ng MCP server", "user": "feedback ng user"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON><PERSON>", "send": "Ipadala", "restoreAndSend": "Ibalik at Ipadala"}, "ideaSuggestionsBox": {"newHere": "Bago dito?", "suggestionText": "<suggestionButton>I-click dito</suggestionButton> para sa isang magandang ideya, pagkatapos i-tap ang <sendIcon /> at panoorin mo akong gumawa ng mahika!", "ideas": {"idea1": "Gumawa ng umiikot, kumikinang na 3D sphere na tumutuon sa mga galaw ng mouse. Gawing gumagana ang app sa browser.", "idea2": "Lumikha ng portfolio website para sa isang Python software developer", "idea3": "Lumikha ng financial app mockup sa browser. Pagkatapos subukan kung gumagana", "idea4": "Lumikha ng directory website na naglalaman ng mga pinakamahusay na AI video generation models ngayon", "idea5": "Lumikha ng CSS gradient generator na nag-export ng custom stylesheets at nagpapakita ng live preview", "idea6": "Mag-generate ng mga cards na nagbubunyag ng dynamic content nang maganda kapag nag-hover", "idea7": "Lumikha ng mapayapang interactive starfield na gumagalaw sa mga galaw ng mouse"}}}