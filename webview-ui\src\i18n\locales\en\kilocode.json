{"welcome": {"greeting": "Welcome to Kilo Code!", "introText1": "Kilo Code is a free, open source AI coding agent.", "introText2": "It works with the latest AI models like Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, and 450+ more.", "introText3": "Create a free account and get $20 worth of tokens to use across any AI model.", "ctaButton": "Create a free account", "manualModeButton": "Use your own API key", "alreadySignedUp": "Already signed up?", "loginText": "Log in here"}, "lowCreditWarning": {"addCredit": "Add Credit", "lowBalance": "Your Kilo Code balance is low"}, "notifications": {"toolRequest": "Tool request waiting for approval", "browserAction": "Browser action waiting for approval", "command": "Command waiting for approval"}, "settings": {"sections": {"mcp": "MCP Servers"}, "provider": {"account": "<PERSON><PERSON> Code Account", "apiKey": "Kilo Code API Key", "login": "Log in at Kilo Code", "logout": "Log out from Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Allow very large file reads", "description": "When enabled, Kilo Code will perform very large file or MCP output reads even if there is a high chance of overflowing the context window (content size >80% of context window)."}}, "systemNotifications": {"label": "Enable System Notifications", "description": "When enabled, Kilo Code will send system notifications for important events like task completion or errors.", "testButton": "Test Notification", "testTitle": "Kilo Code", "testMessage": "This is a test notification from Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "<PERSON><PERSON> wants to condense your conversation", "condenseConversation": "Condense Conversation"}}, "newTaskPreview": {"task": "Task"}, "profile": {"title": "Profile", "dashboard": "Dashboard", "logOut": "Log out", "currentBalance": "CURRENT BALANCE", "loading": "Loading..."}, "docs": "Docs", "rules": {"tooltip": "Manage Kilo Code Rules & Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "Rules", "workflows": "Workflows"}, "description": {"rules": "Rules allow you to provide Kilo Code with instructions it should follow in all modes and for all prompts. They are a persistent way to include context and preferences for all conversations in your workspace or globally.", "workflows": "Workflows are a prepared template for a conversation. Workflows can allow you to define prompts you use frequently, and can include a series of steps to guide Kilo Code through repetitive tasks, such as deploying a service or submitting a PR. To invoke a workflow, type", "workflowsInChat": "in the chat."}, "sections": {"globalRules": "Global Rules", "workspaceRules": "Workspace Rules", "globalWorkflows": "Global Workflows", "workspaceWorkflows": "Workspace Workflows"}, "validation": {"invalidFileExtension": "Only .md, .txt, or no file extension allowed"}, "placeholders": {"workflowName": "workflow-name (.md, .txt, or no extension)", "ruleName": "rule-name (.md, .txt, or no extension)"}, "newFile": {"newWorkflowFile": "New workflow file...", "newRuleFile": "New rule file..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "View {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "browser launch", "browser_action_result": "browser action result", "browser_action": "browser action", "checkpoint_saved": "checkpoint saved", "command": "command execution", "command_output": "command output", "completion_result": "completion result", "condense_context": "condense context", "error": "error message", "followup": "followup question", "mcp_server_response": "MCP server response", "reasoning": "AI reasoning", "text": "AI response", "tool": "tool usage", "user": "user feedback", "unknown": "unknown message type", "use_mcp_server": "MCP server usage"}}}, "userFeedback": {"editCancel": "Cancel", "send": "Send", "restoreAndSend": "Restore & Send"}, "ideaSuggestionsBox": {"newHere": "New here?", "suggestionText": "<suggestionButton>Click here</suggestionButton> for a cool idea, then tap on <sendIcon /> and watch me do magic!", "ideas": {"idea1": "Make a spinning, glowing 3D sphere that responds to mouse movements. Make the app work in the browser.", "idea2": "Create a portfolio website for a Python software developer", "idea3": "Create a financial app mockup in the browser. Then test if it works", "idea4": "Create a directory website containing the best AI video generation models currently", "idea5": "Create a CSS gradient generator that exports custom stylesheets and shows a live preview", "idea6": "Generate cards that reveal dynamic content beautifully when hovered over", "idea7": "Create a calming interactive starfield that moves with mouse gestures"}}}