{"greeting": "Co Kilo Code może dla Ciebie zrobić?", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Zobacz więcej", "seeLess": "Zobacz mniej", "tokens": "Tokeny:", "cache": "<PERSON><PERSON><PERSON>ć podręczna:", "apiCost": "Koszt API:", "contextWindow": "Okno kontekstu:", "closeAndStart": "Zamknij zadanie i rozpocznij nowe", "export": "Eksportuj historię zadań", "delete": "<PERSON><PERSON><PERSON> zadanie (Shi<PERSON> + <PERSON><PERSON><PERSON>, aby pomin<PERSON> potwierdzenie)", "condenseContext": "Inteligentnie skondensuj kontekst", "share": "Udostępnij zadanie", "shareWithOrganization": "Udostępnij organizacji", "shareWithOrganizationDescription": "Tylko członkowie twojej organizacji mogą uzyskać dostęp", "sharePublicly": "Udostępnij publicznie", "sharePubliclyDescription": "Każ<PERSON> z linkiem może uzyskać dostęp", "connectToCloud": "Połącz z chmurą", "connectToCloudDescription": "Zaloguj się do Kilo Code Cloud, aby udostę<PERSON>niać zadania", "sharingDisabledByOrganization": "Udostępnianie wyłączone przez organizację", "shareSuccessOrganization": "Link organizacji skopiowany do schowka", "shareSuccessPublic": "Link publiczny skopiowany do schowka"}, "history": {"title": "Historia"}, "unpin": "Odepnij", "pin": "Przypnij", "tokenProgress": {"availableSpace": "Dostę<PERSON><PERSON> miej<PERSON>ce: {{amount}} tokenów", "tokensUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tokeny: {{used}} z {{total}}", "reservedForResponse": "Zarezerwowane dla odpowiedzi modelu: {{amount}} tokenów"}, "retry": {"title": "Ponów", "tooltip": "Spróbuj ponownie wykonać operację"}, "startNewTask": {"title": "Rozpocznij nowe zadanie", "tooltip": "Rozpocznij nowe zadanie"}, "reportBug": {"title": "Zgł<PERSON>ś błąd"}, "proceedAnyways": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mimo to", "tooltip": "Kontynuuj podczas wykonywania polecenia"}, "save": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Zapisz zmiany w pliku"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tę akcję"}, "completeSubtaskAndReturn": "Zakończ podzadanie i wróć", "approve": {"title": "Zatwierdź", "tooltip": "Zatwierdź tę akcję"}, "runCommand": {"title": "Uru<PERSON><PERSON> polecenie", "tooltip": "<PERSON><PERSON><PERSON><PERSON> to polecenie"}, "proceedWhileRunning": {"title": "Kontynuuj podczas wykonywania", "tooltip": "Kontynuuj pomimo ostrzeżeń"}, "killCommand": {"title": "Zatrzymaj <PERSON>nie", "tooltip": "Zatrzymaj bieżące polecenie"}, "resumeTask": {"title": "Wznów zadanie", "tooltip": "Kontynuuj bieżące zadanie"}, "terminate": {"title": "Zakończ", "tooltip": "Zakończ bieżące zadanie"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Anuluj bieżącą operację"}, "scrollToBottom": "Przewiń do dołu czatu", "about": "Gene<PERSON>j, refaktoryzuj i debuguj kod z pomocą sztucznej inteligencji. Sprawdź naszą <DocsLink>dokumentację</DocsLink>, aby dowiedzieć się więcej.", "onboarding": "<strong>Twoja lista zadań w tym obszarze roboczym jest pusta.</strong> Zacznij od wpisania zadania poniżej. <PERSON>e wiesz, jak zaczą<PERSON>? Przeczytaj więcej o tym, co Kilo Code może dla Ciebie zrobić w <DocsLink>dokumentacji</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Orkiestracja Zadań", "description": "Podziel zadania na mniejsze, łatwiejsze do zarządzania części."}, "stickyModels": {"title": "Tryby trwałe", "description": "<PERSON><PERSON><PERSON> tryb zapamiętuje ostatnio używany model"}, "tools": {"title": "Narzędzia", "description": "Pozwól sztucznej inteligencji rozwiązywać problemy, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sie<PERSON>, uruchamiając polecenia i nie tylko."}, "customizableModes": {"title": "Konfigurow<PERSON>ne tryby", "description": "Wyspecjalizowane persona z własnymi zachowaniami i przypisanymi modelami"}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON> tryb interakcji", "selectApiConfig": "<PERSON><PERSON><PERSON><PERSON> konfigurację <PERSON>", "selectModelConfig": "Wybierz model", "enhancePrompt": "Ulepsz podpowiedź dodatkowym kontekstem", "addImages": "Dodaj obrazy do wiadomości", "sendMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stopTts": "Zatrzymaj syntezę mowy", "typeMessage": "<PERSON><PERSON><PERSON> wiadom<PERSON>ść...", "typeTask": "<PERSON><PERSON><PERSON>, znajdź, zapytaj o coś", "addContext": "@ aby do<PERSON><PERSON> konte<PERSON>t, / aby z<PERSON><PERSON> tryb", "dragFiles": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przecią<PERSON><PERSON>ć pliki", "dragFilesImages": "prz<PERSON><PERSON><PERSON><PERSON> shift, aby przec<PERSON><PERSON><PERSON><PERSON><PERSON> pliki/obrazy", "enhancePromptDescription": "Przycisk 'Uleps<PERSON> podpowiedź' pomaga ul<PERSON>, dostar<PERSON><PERSON><PERSON><PERSON> dodatkowy kontekst, wyjaśnienia lub przeformułowania. Spróbuj wpisać prośbę tutaj i kliknij przycisk ponownie, aby <PERSON><PERSON><PERSON>, jak to dzia<PERSON>.", "modeSelector": {"title": "Tryby", "marketplace": "Marketplace Trybów", "settings": "Ustawienia Trybów", "description": "Wyspecjalizowan<PERSON> persony, które dostosowują zachowanie Kilo Code."}, "errorReadingFile": "Błąd odczytu pliku:", "noValidImages": "Nie przetworzono żadnych prawidłowych obrazów", "separator": "Separator", "edit": "Edytuj...", "forNextMode": "dla następnego trybu", "error": "Błąd", "diffError": {"title": "<PERSON><PERSON><PERSON><PERSON>a"}, "troubleMessage": "Kilo Code ma problemy...", "apiRequest": {"title": "Zapytanie API", "failed": "Zapytanie API nie powiodło się", "streaming": "Zapytanie API...", "cancelled": "Zapytanie API anulowane", "streamingFailed": "Strumieniowanie API nie powiodło się"}, "checkpoint": {"initial": "Początkowy punkt kontrolny", "regular": "Punkt kontrolny", "initializingWarning": "Trwa inicjalizacja punktu kontrolnego... <PERSON><PERSON><PERSON> to trwa zbyt długo, moż<PERSON>z wyłączyć punkty kontrolne w <settingsLink>ustawieniach</settingsLink> i uruchomić zadanie ponownie.", "menu": {"viewDiff": "Zobacz różnice", "restore": "Przywróć punkt kontrolny", "restoreFiles": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pliki", "restoreFilesDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie.", "restoreFilesAndTask": "Przywróć pliki i zadanie", "confirm": "Potwierdź", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON> akcji nie można <PERSON>.", "restoreFilesAndTaskDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie i usuwa wszystkie wiadomości po tym punkcie."}, "current": "Bieżący"}, "instructions": {"wantsToFetch": "Kilo Code chce pobrać szczegółowe instrukcje, aby pomóc w bieżącym zadaniu"}, "fileOperations": {"wantsToRead": "Kilo Code chce przeczytać ten plik:", "wantsToReadOutsideWorkspace": "Kilo Code chce przeczytać ten plik poza obszarem roboczym:", "didRead": "Kilo Code przeczytał ten plik:", "wantsToEdit": "Kilo Code chce edytować ten plik:", "wantsToEditOutsideWorkspace": "Kilo Code chce edytować ten plik poza obszarem roboczym:", "wantsToEditProtected": "Kilo Code chce edytować chroniony plik konfiguracyjny:", "wantsToCreate": "Kilo Code chce utworzyć nowy plik:", "wantsToSearchReplace": "Kilo Code chce wykonać wyszukiwanie i zamianę w tym pliku:", "didSearchReplace": "Kilo Code wykonał wyszukiwanie i zamianę w tym pliku:", "wantsToInsert": "Kilo Code chce wstawić zawartość do tego pliku:", "wantsToInsertWithLineNumber": "Kilo Code chce w<PERSON>wić zawartość do tego pliku w linii {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code chce dodać zawartość na końcu tego pliku:", "wantsToReadAndXMore": "Kilo Code chce przeczytać ten plik i {{count}} więcej:", "wantsToReadMultiple": "Kilo Code chce odczytać wiele plików:", "wantsToApplyBatchChanges": "Kilo Code chce zastosować zmiany do wielu plików:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code chce zobaczyć pliki najwyższego poziomu w tym katalogu:", "didViewTopLevel": "Kilo Code zobaczył pliki najwyższego poziomu w tym katalogu:", "wantsToViewRecursive": "Kilo Code chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu:", "didViewRecursive": "Kilo Code rekurencyjnie zobaczył wszystkie pliki w tym katalogu:", "wantsToViewDefinitions": "Kilo Code chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu:", "didViewDefinitions": "Kilo Code zobaczył nazwy definicji kodu źródłowego używane w tym katalogu:", "wantsToSearch": "Kilo Code chce przeszukać ten katalog w poszukiwaniu <code>{{regex}}</code>:", "didSearch": "Kilo Code przeszukał ten katalog w poszukiwaniu <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code chce przeszukać ten katalog (poza obszarem roboczym) w poszukiwaniu <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code przeszukał ten katalog (poza obszarem roboczym) w poszukiwaniu <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code chce zobaczyć pliki najwyższego poziomu w tym katalogu (poza obszarem roboczym):", "didViewTopLevelOutsideWorkspace": "Kilo Code zobaczył pliki najwyższego poziomu w tym katalogu (poza obszarem roboczym):", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu (poza obszarem roboczym):", "didViewRecursiveOutsideWorkspace": "Kilo Code rekurencyjnie zobaczył wszystkie pliki w tym katalogu (poza obszarem roboczym):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu (poza obszarem roboczym):", "didViewDefinitionsOutsideWorkspace": "Kilo Code zobaczył nazwy definicji kodu źródłowego używane w tym katalogu (poza obszarem roboczym):"}, "commandOutput": "Wyjście polecenia", "response": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "arguments": "Argumenty", "mcp": {"wantsToUseTool": "Kilo Code chce użyć narzędzia na serwerze MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code chce uzyskać dostęp do zasobu na serwerze MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code chce przełączyć się na tryb <code>{{mode}}</code>", "wantsToSwitchWithReason": "Kilo Code chce przełączyć się na tryb <code>{{mode}}</code> ponieważ: {{reason}}", "didSwitch": "Kilo Code przełączył się na tryb <code>{{mode}}</code>", "didSwitchWithReason": "Kilo Code przełączył się na tryb <code>{{mode}}</code> ponieważ: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code chce utworzyć nowe podzadanie w trybie <code>{{mode}}</code>:", "wantsToFinish": "Kilo Code chce zakończyć to podzadanie", "newTaskContent": "Instrukcje podzadania", "completionContent": "Podzadanie zakończone", "resultContent": "Wyniki podzadania", "defaultResult": "Proszę kontynuować następne zadanie.", "completionInstructions": "Podzadanie zakończone! Możesz przejrzeć wyniki i zasugerować poprawki lub następne kroki. Jeśli wszystko wygląda dobrze, pot<PERSON><PERSON><PERSON>, aby zwrócić wynik do zadania nadrzędnego."}, "questions": {"hasQuestion": "Kilo Code ma pytanie:"}, "taskCompleted": "Zadanie zakończone", "powershell": {"issues": "Wygląda na to, że masz problemy z Windows PowerShell, proszę zapoznaj się z tym"}, "autoApprove": {"title": "Automatyczne zatwierdzanie:", "none": "Brak", "description": "Automatyczne zatwierdzanie pozwala Kilo Code wykonywać działania bez pytania o pozwolenie. Włącz tylko dla działań, którym w pełni ufasz. Bardziej szczegółowa konfiguracja dostępna w <settingsLink>Ustawieniach</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}} s"}, "contextCondense": {"title": "Kontekst skondensowany", "condensing": "Kondensowanie kontekstu...", "errorHeader": "<PERSON><PERSON> udało się skondensować kontekstu", "tokens": "tokeny"}, "followUpSuggest": {"copyToInput": "Kopiuj do pola wprowadzania (lub Shift + kliknięcie)", "autoSelectCountdown": "Automatyczny wybór za {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Roo Code {{version}} wydany", "description": "Roo Code {{version}} wprowadza potężne nowe funkcje i znaczące ulepszenia, aby ul<PERSON><PERSON><PERSON><PERSON> Twój przepływ pracy programistycznej.", "whatsNew": "Co nowego", "feature1": "<bold>Indeksowanie bazy kodu ukończone z fazy eksperymentalnej</bold>: Pełne indeksowanie bazy kodu jest teraz stabilne i gotowe do użytku produkcyjnego z ulepszonymi funkcjami wyszukiwania i rozumienia kontekstu.", "feature2": "<bold>Nowa funkcja listy zadań</bold>: Utrzymuj swoje zadania na właściwym torze dzięki zintegrowanemu zarząd<PERSON>iu zada<PERSON>, które pomaga ci pozostać zorganizowanym i skupionym na celach rozwojowych.", "feature3": "<bold>Ulepszone przejścia z Architekta do Kodu</bold>: Płynne transfery z planowania w trybie Architekta do implementacji w trybie Kodu.", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Uzyskaj więcej szczegółów i dołącz do dyskusji na <discordLink>Discord</discordLink> i <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Kilo Code chce użyć przeglądarki:", "consoleLogs": "<PERSON><PERSON> k<PERSON>", "noNewLogs": "(Brak nowych logów)", "screenshot": "Zrzut ekranu przeglądarki", "cursor": "kursor", "navigation": {"step": "<PERSON>rok {{current}} z {{total}}", "previous": "Poprzedni", "next": "Następny"}, "sessionStarted": "Sesja przeglądarki rozpoczęta", "actions": {"title": "Akcja przeglądarki: ", "launch": "Uruchom przeglądarkę na {{url}}", "click": "<PERSON><PERSON><PERSON>j ({{coordinate}})", "type": "Wpisz \"{{text}}\"", "scrollDown": "Przewiń w dół", "scrollUp": "Przewiń w górę", "close": "Zamknij przeglądarkę"}}, "codeblock": {"tooltips": {"expand": "Rozwiń blok kodu", "collapse": "Zwiń blok kodu", "enable_wrap": "Włącz zawijanie wierszy", "disable_wrap": "Wyłącz zawijanie wierszy", "copy_code": "<PERSON><PERSON><PERSON><PERSON> kod"}}, "systemPromptWarning": "OSTRZEŻENIE: Aktywne niestandardowe zastąpienie instrukcji systemowych. <PERSON>że to poważnie zakłócić funkcjonalność i powodować nieprzewidywalne zachowanie.", "profileViolationWarning": "Bieżący profil narusza ustawienia Twojej organizacji", "shellIntegration": {"title": "Ostrzeżenie wykonania polecenia", "description": "<PERSON>je polecenie jest wykonywane bez integracji powłoki terminala VSCode. Aby ukryć to ostrzeżenie, moż<PERSON>z wyłączyć integrację powłoki w sekcji <strong>Terminal</strong> w <settingsLink>ustawieniach Kilo Code</settingsLink> lub rozwi<PERSON>ć problemy z integracją terminala VSCode korzystając z poniższego linku.", "troubleshooting": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby zobaczyć dokumentację integracji powłoki."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Osiągnięto limit automatycznie zatwierdzonych żądań", "description": "Kilo Code osiągnął automatycznie zatwierdzony limit {{count}} żądania/żądań API. <PERSON>zy chcesz zresetować licznik i kontynuować zadanie?", "button": "Zresetuj i kontynuuj"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code chce przeszukać bazę kodu w poszukiwaniu <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code chce przeszukać bazę kodu w poszukiwaniu <code>{{query}}</code> w <code>{{path}}</code>:", "didSearch": "Znaleziono {{count}} wynik(ów) dla <code>{{query}}</code>:", "resultTooltip": "Wynik podobieństwa: {{score}} (k<PERSON><PERSON><PERSON>, aby otworz<PERSON> plik)"}, "read-batch": {"approve": {"title": "Zatwierdź wszystko"}, "deny": {"title": "<PERSON><PERSON><PERSON><PERSON> wszystko"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> gotowy", "indexing": "Indeksowanie {{percentage}}%", "indexed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Błąd indeksu", "status": "Status indeksu"}, "versionIndicator": {"ariaLabel": "Wersja {{version}} - <PERSON><PERSON><PERSON><PERSON>, aby w<PERSON><PERSON><PERSON><PERSON>lić informacje o wydaniu"}}