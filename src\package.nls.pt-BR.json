{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Assistente de codificação de IA de código aberto para planejamento, construção e correção de código.", "command.newTask.title": "Nova Tarefa", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "Adicionar ao Contexto", "command.openInNewTab.title": "Abrir em Nova Aba", "command.focusInput.title": "Focar Campo de Entrada", "command.setCustomStoragePath.title": "Definir Caminho de Armazenamento Personalizado", "command.importSettings.title": "Importar Configurações", "command.terminal.addToContext.title": "Adicionar <PERSON>teúdo do Terminal ao Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceitar Entrada/Sugestão", "command.generateCommitMessage.title": "Gerar Mensagem de Commit com Kilo", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Hist<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Abrir no Editor", "command.settings.title": "Configurações", "command.documentation.title": "Documentação", "command.profile.title": "Perfil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Comandos que podem ser executados automaticamente quando 'Sempre aprovar operações de execução' está ativado", "settings.vsCodeLmModelSelector.description": "Configurações para a API do modelo de linguagem do VSCode", "settings.vsCodeLmModelSelector.vendor.description": "O fornecedor do modelo de linguagem (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "A família do modelo de linguagem (ex: gpt-4)", "settings.customStoragePath.description": "Caminho de armazenamento personalizado. Deixe vazio para usar o local padrão. Suporta caminhos absolutos (ex: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Habilitar correções rápidas do Kilo Code.", "settings.autoImportSettingsPath.description": "Caminho para um arquivo de configuração do Kilo Code para importar automaticamente na inicialização da extensão. Suporta caminhos absolutos e caminhos relativos ao diretório inicial (por exemplo, '~/Documents/kilo-code-settings.json'). Deixe em branco para desativar a importação automática.", "ghost.input.title": "Pressione 'Enter' para confirmar ou 'Escape' para cancelar", "ghost.input.placeholder": "Descreva o que você quer fazer...", "ghost.commands.generateSuggestions": "Kilo Code: Gerar Sugestões de Edição", "ghost.commands.displaySuggestions": "Exibir Sugestões de Edição", "ghost.commands.cancelSuggestions": "Cancelar Sugestões de Edição", "ghost.commands.applyCurrentSuggestion": "Aplicar Sugestão de Edição Atual", "ghost.commands.applyAllSuggestions": "<PERSON><PERSON><PERSON><PERSON> as Sugestões de Edição", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "Ir Para a Próxima Sugestão", "ghost.commands.goToPreviousSuggestion": "Ir Para a Sugestão Anterior"}