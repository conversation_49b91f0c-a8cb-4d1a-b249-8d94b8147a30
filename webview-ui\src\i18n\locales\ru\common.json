{"number_format": {"thousand_suffix": "тыс", "million_suffix": "млн", "billion_suffix": "млрд"}, "feedback": {"title": "Обратная связь", "description": "Мы будем рады услышать ваши отзывы или помочь с любыми проблемами, которые у вас возникают.", "githubIssues": "Сообщить о проблеме на GitHub", "githubDiscussions": "Присоединиться к обсуждениям на GitHub", "discord": "Присоединиться к нашему сообществу в Discord", "customerSupport": "Служба поддержки"}, "answers": {"yes": "Да", "no": "Нет", "cancel": "Отмена", "remove": "Удалить", "keep": "Оставить"}, "ui": {"search_placeholder": "Поиск..."}, "mermaid": {"loading": "Создание диаграммы mermaid...", "render_error": "Не удалось отобразить диаграмму", "fixing_syntax": "Исправление синтаксиса Mermaid...", "fix_syntax_button": "Исправить синтаксис с помощью ИИ", "original_code": "Исходный код:", "errors": {"unknown_syntax": "Неизвестная ошибка синтаксиса", "fix_timeout": "Истекло время ожидания запроса исправления LLM", "fix_failed": "Не удалось исправить с помощью LLM", "fix_attempts": "Не удалось исправить синтаксис после {{attempts}} попыток. Последняя ошибка: {{error}}", "no_fix_provided": "LLM не смог предоставить исправление", "fix_request_failed": "Запрос на исправление не выполнен"}, "buttons": {"zoom": "Масш<PERSON><PERSON><PERSON>", "zoomIn": "Увеличить", "zoomOut": "Уменьшить", "copy": "Копировать", "save": "Сохранить изображение", "viewCode": "Посмотреть код", "viewDiagram": "Посмотреть диаграмму", "close": "Закрыть"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Me<PERSON>"}, "tabs": {"diagram": "Диаграмма", "code": "<PERSON>од"}, "feedback": {"imageCopied": "Изображение скопировано в буфер обмена", "copyError": "Ошибка копирования изображения"}}, "file": {"errors": {"invalidDataUri": "Неверный формат URI данных", "copyingImage": "Ошибка копирования изображения: {{error}}", "openingImage": "Ошибка открытия изображения: {{error}}", "pathNotExists": "Путь не существует: {{path}}", "couldNotOpen": "Не удалось открыть файл: {{error}}", "couldNotOpenGeneric": "Не удалось открыть файл!"}, "success": {"imageDataUriCopied": "URI данных изображения скопирован в буфер обмена"}}}