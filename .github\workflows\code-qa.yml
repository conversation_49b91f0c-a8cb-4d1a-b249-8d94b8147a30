name: Code QA Kilo Code

on:
    workflow_dispatch:
    push:
        branches: [main]
    pull_request:
        types: [opened, reopened, ready_for_review, synchronize]
        branches: [main]

env:
    NODE_VERSION: 20.19.2
    PNPM_VERSION: 10.8.1

jobs:
    compile:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: "pnpm"
            - name: Install dependencies
              run: pnpm install
            - name: Build
              run: pnpm build
            - name: Check types
              run: pnpm check-types
            - name: Lint
              run: pnpm lint

    check-translations:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "18"
                  cache: "pnpm"
            - name: Install dependencies
              run: pnpm install
            - name: Verify all translations are complete
              run: node scripts/find-missing-translations.js

    test-extension:
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                os: [ubuntu-latest, windows-latest]
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: "pnpm"
            - name: Install dependencies
              run: pnpm install
            - name: Build (to build and copy WASM files)
              run: pnpm build
            - name: Run unit tests
              working-directory: src
              run: pnpm test

    test-webview:
        runs-on: ${{ matrix.os }}
        strategy:
            matrix:
                os: [ubuntu-latest, windows-latest]
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}
            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: "18"
                  cache: "pnpm"
            - name: Install dependencies
              run: pnpm install
            - name: Run unit tests
              working-directory: webview-ui
              run: pnpm test

    unit-test:
        needs: [test-extension, test-webview]
        runs-on: ubuntu-latest
        steps:
            - name: NO-OP
              run: echo "All unit tests passed."
