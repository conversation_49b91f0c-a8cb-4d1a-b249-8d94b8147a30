{"title": "MCPサーバー", "done": "完了", "description": "<0>Model Context Protocol</0>は、ローカルで実行されているMCPサーバーとの通信を可能にし、Ki<PERSON> Codeの機能を拡張するための追加ツールやリソースを提供します。<1>コミュニティによって作成されたサーバー</1>を使用したり、Kilo Codeにワークフロー専用の新しいツールを作成するよう依頼したりできます（例：「最新のnpmドキュメントを取得するツールを追加する」）。", "instructions": "手順", "enableToggle": {"title": "MCPサーバーを有効化", "description": "これをONにすると、Kilo Codeが接続されたMCPサーバーのツールを使えるようになるよ。Kilo Codeの機能が増える！追加ツールを使わないなら、APIトークンのコストを抑えるためにOFFにしてね。"}, "enableServerCreation": {"title": "MCPサーバー作成を有効化", "description": "これをONにすると、Kilo Codeが<1>新しい</1>カスタムMCPサーバーを作るのを手伝ってくれるよ。<0>サーバー作成について詳しく</0>", "hint": "ヒント: APIトークンのコストを抑えたいときは、<PERSON><PERSON> Codeに新しいMCPサーバーを作らせないときにこの設定をOFFにしてね。"}, "editGlobalMCP": "グローバルMCPを編集", "editProjectMCP": "プロジェクトMCPを編集", "learnMoreEditingSettings": "MCP設定ファイルの編集方法を詳しく見る", "tool": {"alwaysAllow": "常に許可", "parameters": "パラメータ", "noDescription": "説明なし", "togglePromptInclusion": "プロンプトへの含有を切り替える"}, "tabs": {"tools": "ツール", "resources": "リソース", "errors": "エラー"}, "emptyState": {"noTools": "ツールが見つかりません", "noResources": "リソースが見つかりません", "noErrors": "エラーが見つかりません"}, "networkTimeout": {"label": "ネットワークタイムアウト", "description": "サーバー応答の最大待機時間", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分", "5minutes": "5分", "10minutes": "10分", "15minutes": "15分", "30minutes": "30分", "60minutes": "60分"}}, "deleteDialog": {"title": "MCPサーバーを削除", "description": "本当にMCPサーバー「{{serverName}}」を削除する？この操作は元に戻せないよ。", "cancel": "キャンセル", "delete": "削除"}, "serverStatus": {"retrying": "再試行中...", "retryConnection": "再接続"}, "refreshMCP": "MCPサーバーを更新", "execution": {"running": "実行中", "completed": "完了", "error": "エラー"}}