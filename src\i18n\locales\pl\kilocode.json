{"info": {"settings_imported": "Ustawienia zostały pomyślnie zaimportowane."}, "userFeedback": {"message_update_failed": "<PERSON>e udało się zaktualizować wiadomości", "no_checkpoint_found": "Nie znaleziono punktu kontrolnego przed tą wiadomością", "message_updated": "Wiadomość pomyślnie zaktualizowana"}, "lowCreditWarning": {"title": "Ostrzeżenie o Niskim Kredycie!", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, czy moż<PERSON>z doładować darmowymi kredytami lub kupić więcej!"}, "notLoggedInError": "Nie można ukończyć żądania, upew<PERSON>j się, że jesteś połączony i zalogowany u wybranego dostawcy.\n\n{{error}}", "rules": {"actions": {"delete": "Usuń", "confirmDelete": "<PERSON>zy na pewno chcesz usunąć {{filename}}?", "deleted": "Usunięto {{filename}}"}, "errors": {"noWorkspaceFound": "Nie znaleziono folderu obszaru roboczego", "fileAlreadyExists": "Plik {{filename}} ju<PERSON> istnie<PERSON>", "failedToCreateRuleFile": "Nie udało się utworzyć pliku reguły.", "failedToDeleteRuleFile": "<PERSON>e udało się usunąć pliku reguły."}, "templates": {"workflow": {"description": "Opis przepływu pracy tutaj...", "stepsHeader": "## <PERSON><PERSON><PERSON>", "step1": "Krok 1", "step2": "Krok 2"}, "rule": {"description": "Opis reguły tutaj...", "guidelinesHeader": "## W<PERSON><PERSON><PERSON>ne", "guideline1": "Wytyczna 1", "guideline2": "Wytyczna 2"}}}, "commitMessage": {"activated": "Generator komunikatów dla commitów Kilo Code aktywowany", "gitNotFound": "⚠️ Repozytorium Git nie zostało znalezione lub git jest niedostępny", "gitInitError": "⚠️ Błąd inicjalizacji Git: {{error}}", "generating": "Kilo: Generowanie komunikatu zatwierdzenia...", "noChanges": "Kilo: Nie znaleziono żadnych zmian do przeanalizowania", "generated": "Kilo: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commitu wygenerowana!", "generationFailed": "Kilo: <PERSON><PERSON> u<PERSON> się wygenerować komunikatu zatwierdzenia: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Generowanie wiadomości na podstawie niezatwierdzonych zmian", "activationFailed": "Kilo: <PERSON>e u<PERSON>ło się aktywować generatora wiadomości: {{error}}", "providerRegistered": "Kilo: Zarejestrowano dostawcę komunikatów commit"}, "autocomplete": {"statusBar": {"disabled": "$(circle-slash) <PERSON><PERSON>", "enabled": "$(sparkle) Kilo <PERSON>", "tooltip": {"lastCompletion": "Ostatnie zakończenie:", "disabled": "Kilo Code automatyczne uzupełnianie (wyłączone)", "basic": "Kilo Code Autouzupełnianie", "tokenError": "<PERSON><PERSON> z autouzupełniania, na<PERSON>ży ustawić prawidłowy token", "sessionTotal": "Całkowity koszt sesji:", "model": "Model:"}, "warning": "$(warning) Kilo <PERSON>", "cost": {"lessThanCent": "<0,01 zł", "zero": "0,00 zł"}}, "toggleMessage": "<PERSON><PERSON> {{status}}"}, "ghost": {"progress": {"analyzing": "Analizowanie twojego kodu...", "processing": "Przetwarzanie sugerowanych zmian...", "generating": "Generowanie sugerowanych zmian...", "showing": "Wyświetlanie sugerowanych poprawek...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Szybkie Zadanie", "placeholder": "np. „zrefaktoruj tę funkcję, aby była bardziej wydajna\""}, "commands": {"displaySuggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> su<PERSON>", "cancelSuggestions": "<PERSON><PERSON><PERSON>", "applyCurrentSuggestion": "Zastosuj bi<PERSON>żącą sugerowaną edycję", "applyAllSuggestions": "Zastosuj wszystkie sugerowane poprawki", "generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON><PERSON> Sugerowanych Poprawek", "promptCodeSuggestion": "Szybkie zadanie", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: <PERSON><PERSON><PERSON><PERSON> edy<PERSON>je"}, "chatParticipant": {"name": "Agent", "fullName": "Agent <PERSON><PERSON>", "description": "Mogę pomóc Ci w szybkich zadaniach i zasugerować poprawki."}, "messages": {"provideCodeSuggestions": "Generowanie sugestii kodu..."}}}