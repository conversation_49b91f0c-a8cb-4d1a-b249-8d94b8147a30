{"extension.displayName": "Kilo Code AI Agent (kombinerade funktioner fr<PERSON><PERSON> / Roo)", "extension.description": "Open Source AI-kodningsassistent för planering, byggande och fixande av kod.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "Ny uppgift", "command.mcpServers.title": "MCP-servrar", "command.prompts.title": "Lägen", "command.history.title": "Historik", "command.marketplace.title": "Marknadsplats", "command.openInEditor.title": "Öppna i redigerare", "command.settings.title": "Inställningar", "command.documentation.title": "Dokumentation", "command.openInNewTab.title": "Öppna i ny flik", "command.explainCode.title": "Förklara kod", "command.fixCode.title": "Fixa kod", "command.improveCode.title": "Förbättra kod", "command.addToContext.title": "Lägg till i kontext", "command.focusInput.title": "Fokusera inmatningsfält", "command.setCustomStoragePath.title": "<PERSON><PERSON><PERSON> in anpassad lagringssökväg", "command.terminal.addToContext.title": "Lägg till terminalinnehåll i kontext", "command.terminal.fixCommand.title": "<PERSON>xa detta kommando", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON> kommando", "command.acceptInput.title": "Acceptera inmatning/förslag", "command.generateCommitMessage.title": "Generera commit-meddelande med Kilo", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Kommandon som kan köras automatiskt när 'Always approve execute operations' är aktiverat", "settings.vsCodeLmModelSelector.description": "Inställningar för VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Leverantören av språkmodellen (t.ex. copilot)", "settings.vsCodeLmModelSelector.family.description": "Familjen av språkmodellen (t.ex. gpt-4)", "settings.customStoragePath.description": "Anpassad lagringssökväg. Lämna tomt för att använda standardplatsen. Stöder absoluta sökvägar (t.ex. 'D:\\KiloCodeStorage')", "command.importSettings.title": "Importera inställningar", "settings.enableCodeActions.description": "Aktivera Kilo Code snabbkorrigeringar.", "settings.autoImportSettingsPath.description": "Sökväg till en Kilo Code-konfigurationsfil som ska importeras automatiskt vid uppstart av tillägget. Stöder absoluta sökvägar och sökvägar relativt till hemkatalogen (t.ex. '~/Documents/kilo-code-settings.json'). Lämna tomt för att inaktivera automatisk import.", "ghost.input.title": "<PERSON><PERSON> '<PERSON><PERSON>' för att bekräfta eller 'Escape' för att avbryta", "ghost.input.placeholder": "Beskriv vad du vill göra...", "ghost.commands.generateSuggestions": "Kilo Code: <PERSON><PERSON>ingsförslag", "ghost.commands.displaySuggestions": "Visa Redigeringsförslag", "ghost.commands.cancelSuggestions": "Avbryt Redigeringsförslag", "ghost.commands.applyCurrentSuggestion": "Tillämpa Nuvarande Redigeringsförslag", "ghost.commands.applyAllSuggestions": "Tillämpa Alla Redigeringsförslag", "ghost.commands.promptCodeSuggestion": "Snabb Uppgift", "ghost.commands.goToNextSuggestion": "Gå Till Nästa Förslag", "ghost.commands.goToPreviousSuggestion": "Gå Till Föregående Förslag"}