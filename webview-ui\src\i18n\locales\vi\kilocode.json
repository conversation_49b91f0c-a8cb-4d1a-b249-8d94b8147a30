{"welcome": {"greeting": "Chào mừng bạn đến với Kilo Code!", "introText1": "Kilo Code là một agent l<PERSON><PERSON> tr<PERSON>nh AI miễn phí và mã nguồn mở.", "introText2": "<PERSON><PERSON>t động với các mô hình AI mới nhất nh<PERSON> 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, v<PERSON> hơn 450 mô hình khác.", "introText3": "Tạo tài khoản miễn phí và nhận token trị giá $20 để sử dụng với bất kỳ mô hình AI nào.", "ctaButton": "<PERSON><PERSON><PERSON> tài k<PERSON>n mi<PERSON>n phí", "manualModeButton": "Sử dụng khóa API của riêng bạn", "alreadySignedUp": "Đã đăng ký?", "loginText": "<PERSON><PERSON><PERSON> nh<PERSON>p tại đây"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON><PERSON> tín dụng", "lowBalance": "Số d<PERSON> của bạn thấp"}, "notifications": {"toolRequest": "<PERSON><PERSON><PERSON> cầu công cụ đang chờ phê duyệt", "browserAction": "<PERSON><PERSON><PERSON> động trình duyệt đang chờ phê duyệt", "command": "<PERSON><PERSON><PERSON> đang chờ phê du<PERSON>t"}, "settings": {"sections": {"mcp": "<PERSON><PERSON><PERSON> chủ MCP"}, "contextManagement": {"allowVeryLargeReads": {"label": "<PERSON> phép đọc tệp rất lớn", "description": "<PERSON><PERSON>, <PERSON>lo Code sẽ thực hiện đọc tệp hoặc đầu ra MCP rất lớn ngay cả khi có khả năng cao làm tràn cửa sổ ngữ cảnh (kích thước nội dung >80% cửa sổ ngữ cảnh)."}}, "provider": {"account": "<PERSON><PERSON><PERSON> k<PERSON> Ki<PERSON>", "apiKey": "Khóa API Kilo Code", "login": "<PERSON><PERSON><PERSON> nh<PERSON>p v<PERSON><PERSON>", "logout": "<PERSON><PERSON>ng xuất khỏi Kilo Code"}, "systemNotifications": {"label": "<PERSON><PERSON><PERSON> thông b<PERSON><PERSON> hệ thống", "description": "<PERSON><PERSON>, <PERSON>lo Code sẽ gửi thông báo hệ thống cho các sự kiện quan trọng như hoàn thành nhiệm vụ hoặc lỗi.", "testButton": "<PERSON><PERSON><PERSON> tra thông báo", "testTitle": "Kilo Code", "testMessage": "<PERSON><PERSON><PERSON> là thông báo kiểm tra từ Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "<PERSON>lo <PERSON> muốn tóm tắt cuộc trò chuyện của bạn", "condenseConversation": "<PERSON><PERSON><PERSON> tắt cuộc trò chuy<PERSON>n"}}, "newTaskPreview": {"task": "Nhiệm vụ"}, "profile": {"title": "<PERSON><PERSON> sơ", "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "logOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "currentBalance": "SỐ DƯ HIỆN TẠI", "loading": "<PERSON><PERSON> tả<PERSON>..."}, "docs": "<PERSON><PERSON><PERSON> l<PERSON>", "rules": {"tooltip": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>uy tắc & <PERSON><PERSON> trình làm việc Kilo Code", "ariaLabel": "<PERSON><PERSON> t<PERSON>", "tabs": {"rules": "<PERSON><PERSON>", "workflows": "<PERSON><PERSON> trình làm vi<PERSON>c"}, "description": {"rules": "<PERSON>uy tắc cho phép bạn cung cấp hướng dẫn cho Kilo Code mà nó phải tuân theo trong tất cả các chế độ và cho tất cả các prompt. <PERSON>úng là một cách bền vững để bao gồm ngữ cảnh và tùy chọn cho tất cả các cuộc trò chuyện trong workspace của bạn hoặc toàn cầu.", "workflows": "Quy trình làm việc là một mẫu được chuẩn bị cho một cuộc trò chuyện. Quy trình làm việc cho phép bạn xác định các prompt mà bạn sử dụng thường <PERSON>, và có thể bao gồm một loạt các bước để hướng dẫn Kilo Code thông qua các tác vụ lặp đi lặp lại, chẳng hạn như triển khai dịch vụ hoặc gửi PR. Để gọi quy trình làm việc, h<PERSON><PERSON> nh<PERSON>p", "workflowsInChat": "trong chat."}, "sections": {"globalRules": "<PERSON><PERSON> <PERSON> c<PERSON>u", "workspaceRules": "<PERSON>uy tắc Workspace", "globalWorkflows": "<PERSON><PERSON> trình làm vi<PERSON><PERSON>u", "workspaceWorkflows": "<PERSON><PERSON> trình làm việc Workspace"}, "validation": {"invalidFileExtension": "Chỉ cho phép phần mở rộng .md, .txt hoặc không có phần mở rộng"}, "placeholders": {"workflowName": "tên-quy-trình-làm-vi<PERSON><PERSON> (.md, .txt hoặc không có phần mở rộng)", "ruleName": "tên-quy-tắc (.md, .txt hoặc không có phần mở rộng)"}, "newFile": {"newWorkflowFile": "<PERSON><PERSON><PERSON> quy trình làm việc mới...", "newRuleFile": "<PERSON><PERSON><PERSON> quy tắc mới..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "khởi chạy trình <PERSON>", "browser_action": "h<PERSON>nh động trình <PERSON>", "browser_action_result": "kết quả hành động trình du<PERSON>t", "checkpoint_saved": "đã lưu điểm kiểm tra", "command_output": "k<PERSON><PERSON> qu<PERSON> l<PERSON>nh", "command": "thực thi lệnh", "completion_result": "kết qu<PERSON> hoàn thành", "error": "thông báo lỗi", "followup": "câu hỏi tiếp theo", "condense_context": "thu gọn ngữ cảnh", "mcp_server_response": "<PERSON><PERSON><PERSON> hồi từ máy chủ MCP", "reasoning": "<PERSON><PERSON>", "unknown": "lo<PERSON>i tin nhắn không xác đ<PERSON>nh", "tool": "sử dụng công cụ", "text": "<PERSON><PERSON><PERSON> c<PERSON>", "use_mcp_server": "Sử dụng máy chủ MCP", "user": "p<PERSON><PERSON><PERSON> hồi của người dùng"}, "clickToScroll": "Xem {{messageType}} (#{{messageNumber}})"}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "restoreAndSend": "Khôi phục & Gửi"}, "ideaSuggestionsBox": {"newHere": "Mới ở đây?", "suggestionText": "<suggestionButton><PERSON><PERSON><PERSON><PERSON> vào đây</suggestionButton> để có một ý tưởng tuyệt vời, sau đó chạm vào <sendIcon /> và xem tôi làm phép thuật!", "ideas": {"idea1": "Tạo một hình cầu 3D quay và phát sáng phản ứng với chuyển động chuột. <PERSON><PERSON><PERSON> cho ứng dụng hoạt động trong trình duyệt.", "idea2": "Tạo một trang web portfolio cho nhà phát triển phần mềm Python", "idea3": "Tạo một bản mô phỏng ứng dụng tài chính trong trình duyệt. <PERSON>u đó kiểm tra xem nó có hoạt động không", "idea4": "Tạo một trang web thư mục chứa các mô hình tạo video AI tốt nhất hiện tại", "idea5": "Tạo một trình tạo gradient CSS xuất các stylesheet tùy chỉnh và hiển thị bản xem trước trực tiếp", "idea6": "<PERSON><PERSON><PERSON> các thẻ tiết lộ nội dung động một cách đẹp mắt khi di chuột qua", "idea7": "T<PERSON><PERSON> một trường sao tương tác êm dịu di chuyển theo cử chỉ chuột"}}}