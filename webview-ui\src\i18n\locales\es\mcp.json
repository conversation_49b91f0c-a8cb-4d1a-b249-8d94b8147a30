{"title": "Servidores MCP", "done": "Listo", "description": "El <0>Model Context Protocol</0> permite la comunicación con servidores MCP que se ejecutan localmente y proporcionan herramientas y recursos adicionales para extender las capacidades de Kilo Code. Puedes usar <1>servidores creados por la comunidad</1> o pedir a Kilo Code que cree nuevas herramientas específicas para tu flujo de trabajo (por ejemplo, \"añadir una herramienta que obtenga la documentación más reciente de npm\").", "instructions": "Instrucciones", "enableToggle": {"title": "Activar servidores MCP", "description": "Actívalo para que Kilo Code pueda usar herramientas de servidores MCP conectados. Esto le da más capacidades a Kilo Code. Si no planeas usar estas herramientas extra, desactívalo para ayudar a reducir los costes de tokens API."}, "enableServerCreation": {"title": "Activar creación de servidores MCP", "description": "Actívalo para que Kilo Code te ayude a crear <1>nuevos</1> servidores MCP personalizados. <0>Más información sobre la creación de servidores</0>", "hint": "Consejo: Para reducir los costes de tokens API, desactiva esta opción cuando no le pidas a Kilo Code que cree un nuevo servidor MCP."}, "editGlobalMCP": "Editar MCP global", "editProjectMCP": "Editar MCP del proyecto", "learnMoreEditingSettings": "Más información sobre cómo editar archivos de configuración MCP", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON>", "parameters": "Parámetros", "noDescription": "Sin descripción", "togglePromptInclusion": "Alternar inclusión en el prompt"}, "tabs": {"tools": "Herramientas", "resources": "Recursos", "errors": "Errores"}, "emptyState": {"noTools": "No se encontraron herramientas", "noResources": "No se encontraron recursos", "noErrors": "No se encontraron errores"}, "networkTimeout": {"label": "Tiempo de espera de red", "description": "Tiempo máximo de espera para respuestas del servidor", "options": {"15seconds": "15 segundos", "30seconds": "30 segundos", "1minute": "1 minuto", "5minutes": "5 minutos", "10minutes": "10 minutos", "15minutes": "15 minutos", "30minutes": "30 minutos", "60minutes": "60 minutos"}}, "deleteDialog": {"title": "Eliminar servidor <PERSON>", "description": "¿Seguro que quieres eliminar el servidor MCP \"{{serverName}}\"? Esta acción no se puede deshacer.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar"}, "serverStatus": {"retrying": "Reintentando...", "retryConnection": "Reintentar conexión"}, "refreshMCP": "Act<PERSON>iza<PERSON>", "execution": {"running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Completado", "error": "Error"}}