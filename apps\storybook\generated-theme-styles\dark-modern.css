/* Default Dark Modern theme - Generated from VS Code */
--vscode-checkbox-border: #3c3c3c;
--vscode-editor-background: #1f1f1f;
--vscode-editor-foreground: #cccccc;
--vscode-editor-inactiveSelectionBackground: #3a3d41;
--vscode-editorIndentGuide-background1: #404040;
--vscode-editorIndentGuide-activeBackground1: #707070;
--vscode-editor-selectionHighlightBackground: #add6ff26;
--vscode-list-dropBackground: #383b3d;
--vscode-activityBarBadge-background: #0078d4;
--vscode-sideBarTitle-foreground: #cccccc;
--vscode-input-placeholderForeground: #989898;
--vscode-menu-background: #1f1f1f;
--vscode-menu-foreground: #cccccc;
--vscode-menu-separatorBackground: #454545;
--vscode-menu-border: #454545;
--vscode-menu-selectionBackground: #0078d4;
--vscode-statusBarItem-remoteForeground: #ffffff;
--vscode-statusBarItem-remoteBackground: #0078d4;
--vscode-ports-iconRunningProcessForeground: #369432;
--vscode-sideBarSectionHeader-background: #181818;
--vscode-sideBarSectionHeader-border: #2b2b2b;
--vscode-tab-selectedBackground: #222222;
--vscode-tab-selectedForeground: #ffffffa0;
--vscode-tab-lastPinnedBorder: #ccc3;
--vscode-list-activeSelectionIconForeground: #fff;
--vscode-terminal-inactiveSelectionBackground: #3a3d41;
--vscode-widget-border: #313131;
--vscode-actionBar-toggledBackground: #383a49;
--vscode-activityBar-activeBorder: #0078d4;
--vscode-activityBar-background: #181818;
--vscode-activityBar-border: #2b2b2b;
--vscode-activityBar-foreground: #d7d7d7;
--vscode-activityBar-inactiveForeground: #868686;
--vscode-activityBarBadge-foreground: #ffffff;
--vscode-badge-background: #616161;
--vscode-badge-foreground: #f8f8f8;
--vscode-button-background: #0078d4;
--vscode-button-border: #ffffff12;
--vscode-button-foreground: #ffffff;
--vscode-button-hoverBackground: #026ec1;
--vscode-button-secondaryBackground: #313131;
--vscode-button-secondaryForeground: #cccccc;
--vscode-button-secondaryHoverBackground: #3c3c3c;
--vscode-chat-slashCommandBackground: #26477866;
--vscode-chat-slashCommandForeground: #85b6ff;
--vscode-chat-editedFileForeground: #e2c08d;
--vscode-checkbox-background: #313131;
--vscode-debugToolBar-background: #181818;
--vscode-descriptionForeground: #9d9d9d;
--vscode-dropdown-background: #313131;
--vscode-dropdown-border: #3c3c3c;
--vscode-dropdown-foreground: #cccccc;
--vscode-dropdown-listBackground: #1f1f1f;
--vscode-editor-findMatchBackground: #9e6a03;
--vscode-editorGroup-border: #ffffff17;
--vscode-editorGroupHeader-tabsBackground: #181818;
--vscode-editorGroupHeader-tabsBorder: #2b2b2b;
--vscode-editorGutter-addedBackground: #2ea043;
--vscode-editorGutter-deletedBackground: #f85149;
--vscode-editorGutter-modifiedBackground: #0078d4;
--vscode-editorLineNumber-activeForeground: #cccccc;
--vscode-editorLineNumber-foreground: #6e7681;
--vscode-editorOverviewRuler-border: #010409;
--vscode-editorWidget-background: #202020;
--vscode-errorForeground: #f85149;
--vscode-focusBorder: #0078d4;
--vscode-foreground: #cccccc;
--vscode-icon-foreground: #cccccc;
--vscode-input-background: #313131;
--vscode-input-border: #3c3c3c;
--vscode-input-foreground: #cccccc;
--vscode-inputOption-activeBackground: #2489db82;
--vscode-inputOption-activeBorder: #2488db;
--vscode-keybindingLabel-foreground: #cccccc;
--vscode-notificationCenterHeader-background: #1f1f1f;
--vscode-notificationCenterHeader-foreground: #cccccc;
--vscode-notifications-background: #1f1f1f;
--vscode-notifications-border: #2b2b2b;
--vscode-notifications-foreground: #cccccc;
--vscode-panel-background: #181818;
--vscode-panel-border: #2b2b2b;
--vscode-panelInput-border: #2b2b2b;
--vscode-panelTitle-activeBorder: #0078d4;
--vscode-panelTitle-activeForeground: #cccccc;
--vscode-panelTitle-inactiveForeground: #9d9d9d;
--vscode-peekViewEditor-background: #1f1f1f;
--vscode-peekViewEditor-matchHighlightBackground: #bb800966;
--vscode-peekViewResult-background: #1f1f1f;
--vscode-peekViewResult-matchHighlightBackground: #bb800966;
--vscode-pickerGroup-border: #3c3c3c;
--vscode-progressBar-background: #0078d4;
--vscode-quickInput-background: #222222;
--vscode-quickInput-foreground: #cccccc;
--vscode-settings-dropdownBackground: #313131;
--vscode-settings-dropdownBorder: #3c3c3c;
--vscode-settings-headerForeground: #ffffff;
--vscode-settings-modifiedItemIndicator: #bb800966;
--vscode-sideBar-background: #181818;
--vscode-sideBar-border: #2b2b2b;
--vscode-sideBar-foreground: #cccccc;
--vscode-sideBarSectionHeader-foreground: #cccccc;
--vscode-statusBar-background: #181818;
--vscode-statusBar-border: #2b2b2b;
--vscode-statusBar-debuggingBackground: #0078d4;
--vscode-statusBar-debuggingForeground: #ffffff;
--vscode-statusBar-focusBorder: #0078d4;
--vscode-statusBar-foreground: #cccccc;
--vscode-statusBar-noFolderBackground: #1f1f1f;
--vscode-statusBarItem-focusBorder: #0078d4;
--vscode-statusBarItem-prominentBackground: #6e768166;
--vscode-tab-activeBackground: #1f1f1f;
--vscode-tab-activeBorder: #1f1f1f;
--vscode-tab-activeBorderTop: #0078d4;
--vscode-tab-activeForeground: #ffffff;
--vscode-tab-selectedBorderTop: #6caddf;
--vscode-tab-border: #2b2b2b;
--vscode-tab-hoverBackground: #1f1f1f;
--vscode-tab-inactiveBackground: #181818;
--vscode-tab-inactiveForeground: #9d9d9d;
--vscode-tab-unfocusedActiveBorder: #1f1f1f;
--vscode-tab-unfocusedActiveBorderTop: #2b2b2b;
--vscode-tab-unfocusedHoverBackground: #1f1f1f;
--vscode-terminal-foreground: #cccccc;
--vscode-terminal-tab-activeBorder: #0078d4;
--vscode-textBlockQuote-background: #2b2b2b;
--vscode-textBlockQuote-border: #616161;
--vscode-textCodeBlock-background: #2b2b2b;
--vscode-textLink-activeForeground: #4daafc;
--vscode-textLink-foreground: #4daafc;
--vscode-textPreformat-foreground: #d0d0d0;
--vscode-textPreformat-background: #3c3c3c;
--vscode-textSeparator-foreground: #21262d;
--vscode-titleBar-activeBackground: #181818;
--vscode-titleBar-activeForeground: #cccccc;
--vscode-titleBar-border: #2b2b2b;
--vscode-titleBar-inactiveBackground: #1f1f1f;
--vscode-titleBar-inactiveForeground: #9d9d9d;
--vscode-welcomePage-tileBackground: #2b2b2b;
--vscode-welcomePage-progress-foreground: #0078d4;
