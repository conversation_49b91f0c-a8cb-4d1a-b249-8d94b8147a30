{"title": "Режими", "done": "Готово", "modes": {"title": "Режими", "createNewMode": "Створити новий режим", "importMode": "Імпортувати Режим", "editModesConfig": "Редагувати налаштування режимів", "editGlobalModes": "Редагувати Global Modes", "editProjectModes": "Редагувати Project Modes (.kilocodemodes)", "createModeHelpText": "Режими - це спеціалізовані персони, які налаштовують поведінку Kilo Code. <0>Дізнайся про Використання Режимів</0> або <1>Налаштування Режимів.</1>", "selectMode": "Пошук режимів", "noMatchFound": "Режими не знайдено"}, "apiConfiguration": {"title": "Налаштування API", "select": "Виб<PERSON><PERSON><PERSON>, яке налаштування API використовувати для цього режиму"}, "tools": {"title": "Доступні Інструменти", "builtInModesText": "Інструменти для вбудованих режимів не можна змінювати", "editTools": "Редагувати інструменти", "doneEditing": "Завершити редагування", "allowedFiles": "Дозволені файли:", "toolNames": {"read": "Читати Файли", "edit": "Редагувати Файли", "browser": "Використовувати Браузер", "command": "Виконувати Команди", "mcp": "Використовувати MCP"}, "noTools": "Немає"}, "roleDefinition": {"title": "Визначення Ролі", "resetToDefault": "Скинути до стандартних", "description": "Визнач експертизу та особистість Kilo Code для цього режиму. Цей опис формує, як Kilo Code представляє себе та підходить до завдань."}, "description": {"title": "Короткий опис (для людей)", "resetToDefault": "Скинути до стандартного опису", "description": "Короткий опис, що відображається у випадаючому меню вибору режиму."}, "whenToUse": {"title": "Коли Використовувати (необов'язково)", "description": "О<PERSON><PERSON><PERSON><PERSON>, коли слід використовувати цей режим. Це допомагає Orchestrator вибрати правильний режим для завдання.", "resetToDefault": "Скинути до стандартного опису 'Коли Використовувати'"}, "customInstructions": {"title": "Спеціальні Інструкції для Режиму (необов'язково)", "resetToDefault": "Скинути до стандартних", "description": "Додай поведінкові вказівки, специфічні для режиму {{modeName}}.", "loadFromFile": "Спеціальні інструкції для режиму {{mode}} також можна завантажити з папки <span>.kilocode/rules/</span> у твоєму робочому просторі (.kilocoderules-{{slug}} застаріло і незабаром перестане працювати)."}, "exportMode": {"title": "Експортувати Режим", "description": "Експортувати цей режим з правилами з папки .kilocode/rules-{{slug}}/ об'єднаними в YAML файл для поширення. Оригінальні файли залишаються незмінними.", "exporting": "Експортування..."}, "importMode": {"selectLevel": "Виберіть, куди імпортувати цей режим:", "import": "Імпортувати", "importing": "Імпортування...", "global": {"label": "Глобальний Рівень", "description": "Доступний у всіх проектах. Якщо експортований режим містив файли правил, вони будуть відтворені в глобальній папці .kilocode/rules-{slug}/."}, "project": {"label": "Рівень Проекту", "description": "Доступний лише в цьому робочому просторі. Якщо експортований режим містив файли правил, вони будуть відтворені в папці .kilocode/rules-{slug}/."}}, "advanced": {"title": "Розширене: Перевизначити System Prompt"}, "globalCustomInstructions": {"title": "Спеціальні Інструкції для Всіх Режимів", "description": "Ці інструкції застосовуються до всіх режимів. Вони надають базовий набір поведінок, які можуть бути покращені специфічними для режиму інструкціями нижче. <0>Дізнатися більше</0>", "loadFromFile": "Інструкції також можна завантажити з папки <span>.kilocode/rules/</span> у твоєму робочому просторі (.kilocoderules застаріло і незабаром перестане працювати)."}, "systemPrompt": {"preview": "Попередній перегляд System Prompt", "copy": "Скопіювати system prompt в буфер обміну", "title": "System Prompt (режим {{modeName}})"}, "supportPrompts": {"title": "Підтри<PERSON><PERSON><PERSON><PERSON><PERSON> Prompts", "resetPrompt": "Скинути {{promptType}} prompt до стандартного", "prompt": "Prompt", "enhance": {"apiConfiguration": "Налаштування API", "apiConfigDescription": "Ти можеш вибрати налаштування API для постійного використання при покращенні prompts, або просто використовувати те, що зараз вибрано", "useCurrentConfig": "Використовувати поточне вибране налаштування API", "testPromptPlaceholder": "Введи prompt для тестування покращення", "previewButton": "Попередній перегляд Покращення Prompt", "testEnhancement": "Тестувати Покращення"}, "types": {"ENHANCE": {"label": "Покращити Prompt", "description": "Використовуй покращення prompt для отримання персоналізованих пропозицій або вдосконалень для твоїх вводів. Це гарантує, що Kilo Code розуміє твій намір і надає найкращі можливі відповіді. Доступно через іконку ✨ в чаті."}, "EXPLAIN": {"label": "Пояснити Код", "description": "Отримай детальні пояснення фрагментів коду, функцій або цілих файлів. Корисно для розуміння складного коду або вивчення нових патернів. Доступно в діях коду (іконка лампочки в редакторі) та контекстному меню редактора (правий клік на вибраному коді)."}, "FIX": {"label": "Виправити Проблеми", "description": "Отримай допомогу у визначенні та вирішенні багів, помилок або проблем якості коду. Надає покрокове керівництво для виправлення проблем. Доступно в діях коду (іконка лампочки в редакторі) та контекстному меню редактора (правий клік на вибраному коді)."}, "IMPROVE": {"label": "Покращити Код", "description": "Отримай пропозиції щодо оптимізації коду, кращих практик та архітектурних покращень, зберігаючи функціональність. Доступно в діях коду (іконка лампочки в редакторі) та контекстному меню редактора (правий клік на вибраному коді)."}, "ADD_TO_CONTEXT": {"label": "Додати до Контексту", "description": "Додай контекст до твого поточного завдання або розмови. Корисно для надання додаткової інформації або уточнень. Доступно в діях коду (іконка лампочки в редакторі) та контекстному меню редактора (правий клік на вибраному коді)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Додати Вміст Терміналу до Контексту", "description": "Додай вивід терміналу до твого поточного завдання або розмови. Корисно для надання виводів команд або логів. Доступно в контекстному меню терміналу (правий клік на вибраному вмісті терміналу)."}, "TERMINAL_FIX": {"label": "Виправити Команду Терміналу", "description": "Отримай допомогу у виправленні команд терміналу, які не вдалися або потребують покращення. Доступно в контекстному меню терміналу (правий клік на вибраному вмісті терміналу)."}, "TERMINAL_EXPLAIN": {"label": "Пояснити Команду Терміналу", "description": "Отримай детальні пояснення команд терміналу та їх виводів. Доступно в контекстному меню терміналу (правий клік на вибраному вмісті терміналу)."}, "NEW_TASK": {"label": "Почати Нове Завдання", "description": "Почни нове завдання з вводом користувача. Доступно в Палітрі Команд."}, "COMMIT_MESSAGE": {"label": "Генерація Повідомлення Commit", "description": "Генеруй описові повідомлення commit на основі твоїх staged git змін. Налаштуй prompt для створення повідомлень, які відповідають найкращим практикам твого репозиторію."}}}, "advancedSystemPrompt": {"title": "Розширене: Перевизначити System Prompt", "description": "<2>⚠️ Попередження:</2> Ця розширена функція обходить захисні механізми. <1>ПРОЧИТАЙ ЦЕ ПЕРЕД ВИКОРИСТАННЯМ!</1>Перевизнач стандартний system prompt, створивши файл у <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Створити Новий Режим", "close": "Закрити", "name": {"label": "Назва", "placeholder": "Введи назву режиму"}, "slug": {"label": "Slug", "description": "Slug використовується в URL та назвах файлів. Має бути в нижньому регістрі та містити лише літери, цифри та дефіси."}, "saveLocation": {"label": "Місце Збереження", "description": "Виб<PERSON><PERSON><PERSON>, де зберегти цей режим. Режими, специфічні для проекту, мають пріоритет над глобальними.", "global": {"label": "Глобальний", "description": "Доступний у всіх робочих просторах"}, "project": {"label": "Специфічний для проекту (.kilocodemodes)", "description": "Доступний лише в цьому робочому просторі, має пріоритет над глобальним"}}, "roleDefinition": {"label": "Визначення Ролі", "description": "Визнач експертизу та особистість Kilo Code для цього режиму."}, "description": {"label": "Короткий опис (для людей)", "description": "Короткий опис, що відображається у випадаючому меню вибору режиму."}, "whenToUse": {"label": "Коли Використовувати (необов'язково)", "description": "Вказівки для Kilo Code щодо того, коли слід використовувати цей режим. Це допомагає Orchestrator вибрати правильний режим для завдання."}, "tools": {"label": "Доступні Інструменти", "description": "Вибери, які інструменти може використовувати цей режим."}, "customInstructions": {"label": "Спеціальні Інструкції (необов'язково)", "description": "Додай поведінкові вказівки, специфічні для цього режиму."}, "buttons": {"cancel": "Скасувати", "create": "Створити Режим"}, "deleteMode": "Видалити режим"}, "allFiles": "всі файли", "deleteMode": {"title": "Видалити Режим", "message": "Ти впевнений, що хочеш видалити режим \"{{modeName}}\"?", "rulesFolder": "Цей режим має папку правил за адресою {{folderPath}}, яка також буде видалена.", "descriptionNoRules": "Ти впевнений, що хочеш видалити цей користувацький режим?", "confirm": "Видалити", "cancel": "Скасувати"}}