{"answers": {"yes": "Sí", "no": "No", "cancel": "Cancel·lar", "remove": "Eliminar", "keep": "Mantenir"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "Ens encantaria escoltar els teus comentaris o ajudar-te amb qualsevol problema que estiguis experimentant.", "githubIssues": "Informa d'un problema a GitHub", "githubDiscussions": "Uneix-te a les discussions de GitHub", "discord": "Uneix-te a la nostra comunitat de Discord", "customerSupport": "Suport al Client"}, "ui": {"search_placeholder": "Cerca..."}, "mermaid": {"loading": "Generant diagrama mermaid...", "render_error": "No es pot renderitzar el diagrama", "fixing_syntax": "Corregint la sintaxi de Mermaid...", "fix_syntax_button": "Corregir sintaxi amb IA", "original_code": "Codi original:", "errors": {"unknown_syntax": "Error de sintaxi desconegut", "fix_timeout": "La sol·licitud de correcció de l'IA ha esgotat el temps", "fix_failed": "La correcció de l'IA ha fallat", "fix_attempts": "No s'ha pogut corregir la sintaxi després de {{attempts}} intents. Últim error: {{error}}", "no_fix_provided": "L'IA no ha pogut proporcionar una correcció", "fix_request_failed": "La sol·licitud de correcció ha fallat"}, "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "save": "Desar imatge", "viewCode": "<PERSON>eure codi", "viewDiagram": "<PERSON><PERSON>e diagrama", "close": "<PERSON><PERSON>"}, "modal": {"codeTitle": "Codi Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Codi"}, "feedback": {"imageCopied": "Imatge copiada al porta-retalls", "copyError": "Error copiant la imatge"}}, "file": {"errors": {"invalidDataUri": "Format d'URI de dades no vàlid", "copyingImage": "Error copiant la imatge: {{error}}", "openingImage": "Error obrint la imatge: {{error}}", "pathNotExists": "El camí no existeix: {{path}}", "couldNotOpen": "No s'ha pogut obrir el fitxer: {{error}}", "couldNotOpenGeneric": "No s'ha pogut obrir el fitxer!"}, "success": {"imageDataUriCopied": "URI de dades de la imatge copiada al porta-retalls"}}}