{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "<PERSON><PERSON> compleet ontwi<PERSON>kel<PERSON><PERSON> van AI-agents in je editor.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "views.activitybar.title": "Kilo Code", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplaats", "command.openInEditor.title": "<PERSON>en in Editor", "command.settings.title": "Instellingen", "command.documentation.title": "Documentatie", "command.openInNewTab.title": "Openen in Nieuw Tabblad", "command.explainCode.title": "Leg Code Uit", "command.fixCode.title": "Repareer Code", "command.improveCode.title": "Verbeter Code", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "command.focusInput.title": "Focus op Invoerveld", "command.setCustomStoragePath.title": "Aangepast Opslagpad Instellen", "command.importSettings.title": "Instellingen Importeren", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> aan <PERSON>n", "command.terminal.fixCommand.title": "Repareer Dit Commando", "command.terminal.explainCommand.title": "Leg Dit Commando Uit", "command.acceptInput.title": "Invoer/Suggestie Accepteren", "command.generateCommitMessage.title": "<PERSON><PERSON> <PERSON> be<PERSON>t met <PERSON><PERSON>", "command.profile.title": "<PERSON><PERSON>", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Commando's die automatisch kunnen worden uitgevoerd wanneer 'Altijd goedkeuren uitvoerbewerkingen' is ingeschakeld", "settings.vsCodeLmModelSelector.description": "Instellingen voor VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "De leverancier van het taalmodel (bijv. copilot)", "settings.vsCodeLmModelSelector.family.description": "De familie van het taalmodel (bijv. gpt-4)", "settings.customStoragePath.description": "Aangepast opslagpad. Laat leeg om de standaardlocatie te gebruiken. Ondersteunt absolute paden (bijv. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Snelle correcties van Kilo Code inschakelen.", "settings.autoImportSettingsPath.description": "Pad naar een <PERSON><PERSON>-configuratiebestand om automatisch te importeren bij het opstarten van de extensie. Ondersteunt absolute paden en paden ten opzichte van de thuismap (bijv. '~/Documents/kilo-code-settings.json'). Laat leeg om automatisch importeren uit te schakelen.", "ghost.input.title": "<PERSON>uk op 'En<PERSON>' om te bevestigen of 'Escape' om te annuleren", "ghost.input.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> wat je wilt doen...", "ghost.commands.generateSuggestions": "Kilo Code: Bewerkingssuggesties Genereren", "ghost.commands.displaySuggestions": "Bewerkingssuggesties Weergeven", "ghost.commands.cancelSuggestions": "Bewerkingssuggesties Annuleren", "ghost.commands.applyCurrentSuggestion": "Huidige Bewerkingssuggestie Toepassen", "ghost.commands.applyAllSuggestions": "Alle Bewerkingssuggesties Toepassen", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "Ga Naar Volgende Suggestie", "ghost.commands.goToPreviousSuggestion": "Ga Naar Vorige <PERSON>"}