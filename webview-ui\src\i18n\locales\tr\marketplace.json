{"title": "Kilo Code Marketplace", "tabs": {"installed": "Yüklü", "settings": "<PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON><PERSON>"}, "done": "Tamamlandı", "refresh": "<PERSON><PERSON><PERSON>", "filters": {"search": {"placeholder": "Marketplace öğelerini ara...", "placeholderMcp": "MCP'leri ara...", "placeholderMode": "Modları ara..."}, "type": {"label": "<PERSON><PERSON><PERSON> göre filtrele:", "all": "<PERSON><PERSON><PERSON>", "mode": "Mod", "mcpServer": "MCP Sunucusu"}, "sort": {"label": "Sırala:", "name": "İsim", "author": "<PERSON><PERSON>", "lastUpdated": "<PERSON>"}, "tags": {"label": "Etiketlere göre filtrele:", "clear": "Etiketleri temizle", "placeholder": "Etiket aramak ve seçmek için yazın...", "noResults": "Eşleşen etiket bulunamadı", "selected": "Seçilen etiketlerden herhangi birine sahip öğeler gösteriliyor", "clickToFilter": "Öğeleri filtrelemek için etiketlere tıklayın"}, "none": "Hiç<PERSON>i"}, "type-group": {"modes": "<PERSON><PERSON><PERSON>", "mcps": "MCP Sunucuları"}, "items": {"empty": {"noItems": "Marketplace öğesi bulunamadı", "withFilters": "Filtrelerinizi ayarlamayı deneyin", "noSources": "Kaynaklar sekmesinde kaynak eklemeyi deneyin", "adjustFilters": "Filtrelerinizi veya arama terimlerinizi ayarlamayı deneyin", "clearAllFilters": "<PERSON><PERSON><PERSON> filtrel<PERSON> te<PERSON>"}, "count": "{{count}} <PERSON><PERSON><PERSON> bulundu", "components": "{{count}} bile<PERSON>en", "matched": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "refresh": {"button": "<PERSON><PERSON><PERSON>", "refreshing": "Yenileniyor...", "mayTakeMoment": "Bu biraz zaman alabilir."}, "card": {"by": "{{author}} ta<PERSON><PERSON><PERSON><PERSON>n", "from": "{{source}} kaynağından", "install": "<PERSON><PERSON><PERSON>", "installProject": "<PERSON><PERSON><PERSON>", "installGlobal": "<PERSON><PERSON><PERSON> (Global)", "remove": "Kaldır", "removeProject": "Kaldır", "removeGlobal": "<PERSON><PERSON><PERSON><PERSON> (Global)", "viewSource": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewOnSource": "{{source}} <PERSON>zer<PERSON><PERSON> gö<PERSON>ü<PERSON>le", "noWorkspaceTooltip": "Marketplace öğelerini yüklemek için bir çalışma alanı açın", "installed": "Yüklü", "removeProjectTooltip": "<PERSON><PERSON><PERSON> projeden kaldır", "removeGlobalTooltip": "Global yapılandırmadan kaldır", "actionsMenuLabel": "<PERSON><PERSON> fazla e<PERSON>m"}}, "install": {"title": "{{name}} <PERSON><PERSON><PERSON>", "titleMode": "{{name}} <PERSON><PERSON><PERSON>", "titleMcp": "{{name}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "scope": "<PERSON><PERSON><PERSON><PERSON>", "project": "Proje (mevcut çalışma alanı)", "global": "Global (tüm çalışma alanları)", "method": "<PERSON><PERSON><PERSON><PERSON>", "configuration": "Yapılandırma", "configurationDescription": "Bu MCP sunucusu için gerekli parametreleri yapılandırın", "button": "<PERSON><PERSON><PERSON>", "successTitle": "{{name}} Y<PERSON><PERSON><PERSON>", "successDescription": "<PERSON><PERSON><PERSON>me başarıyla ta<PERSON>landı", "installed": "Başarıyla yüklendi!", "whatNextMcp": "Artık bu MCP sunucusunu yapılandırabilir ve kullanabilirsiniz. Sekmeleri değiştirmek için kenar çubuğundaki MCP simgesine tıklayın.", "whatNextMode": "Artık bu modu kullanabilirsiniz. Sekmeleri değiştirmek için kenar çubuğundaki Modlar simgesine tıklayın.", "done": "Tamamlandı", "goToMcp": "MCP Sekmesine Git", "goToModes": "<PERSON><PERSON>ar <PERSON> G<PERSON>", "moreInfoMcp": "{{name}} MCP be<PERSON><PERSON>ini görü<PERSON>le", "validationRequired": "Lütfen {{paramName}} i<PERSON><PERSON> bir <PERSON>n", "prerequisites": "<PERSON><PERSON>"}, "sources": {"title": "Marketplace Kaynaklarını Yapılandır", "description": "Marketplace öğeleri içeren Git depolarını ekleyin. Bu depolar marketplace'e göz atarken getirilecektir.", "add": {"title": "<PERSON><PERSON>", "urlPlaceholder": "Git deposu URL'si (örn., https://github.com/username/repo)", "urlFormats": "Desteklenen formatlar: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git), veya Git protokolü (git://github.com/username/repo.git)", "namePlaceholder": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON> (maks 20 karakter)", "button": "<PERSON><PERSON><PERSON>"}, "current": {"title": "<PERSON><PERSON><PERSON>", "empty": "Yapılandırılmış kaynak yok. Başlamak için bir kaynak ekleyin.", "refresh": "Bu kaynağı yenile", "remove": "Kaynağı kaldır"}, "errors": {"emptyUrl": "URL boş olamaz", "invalidUrl": "Geçersiz URL formatı", "nonVisibleChars": "URL boşluklar dışında görünmeyen karakterler içeriyor", "invalidGitUrl": "URL geçerli bir Git deposu URL'si olmalıdır (örn., https://github.com/username/repo)", "duplicateUrl": "Bu URL zaten listede var (büyük/küçük harf ve boşluk duyarsız eşleşme)", "nameTooLong": "İsim 20 karakter veya daha az olmalıdır", "nonVisibleCharsName": "İsim boşluklar dışında görünmeyen karakterler içeriyor", "duplicateName": "<PERSON>u isim zaten k<PERSON> (büyük/küçük harf ve boşluk duyarsız eşleşme)", "emojiName": "Emoji karakterleri görüntüleme sorunlarına neden olabilir", "maxSources": "Maks<PERSON>um {{max}} kaynak izin verilir"}}, "footer": {"issueText": "Bir marketplace öğesi ile ilgili sorun bulduğun veya yeni öğeler için önerilerin var mı? <0>GitHub'da issue aç</0> ve bize bildir!"}}