{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Assistent de codificació de IA de codi obert per planificar, crear i corregir codi.", "command.newTask.title": "Nova Tasca", "command.explainCode.title": "Explicar Codi", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "Millorar Codi", "command.addToContext.title": "<PERSON><PERSON><PERSON>r al Context", "command.openInNewTab.title": "Obrir en una Nova Pestanya", "command.focusInput.title": "Enfocar Camp d'Entrada", "command.setCustomStoragePath.title": "Establir Ruta d'Emmagatzematge Personalitzada", "command.importSettings.title": "Importa<PERSON>", "command.terminal.addToContext.title": "Afegir Contingut del Terminal al Context", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar <PERSON><PERSON><PERSON>", "command.acceptInput.title": "Acceptar Entrada/Suggeriment", "command.generateCommitMessage.title": "Generar Missatge de Commit amb Kilo", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Servidors MCP", "command.prompts.title": "Modes", "command.history.title": "Historial", "command.marketplace.title": "Mercat", "command.openInEditor.title": "Obrir a l'Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Documentació", "command.profile.title": "Perfil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Ordres que es poden executar automàticament quan 'Aprova sempre les operacions d'execució' està activat", "settings.vsCodeLmModelSelector.description": "Configuració per a l'API del model de llenguatge VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveïdor del model de llenguatge (p. ex. copilot)", "settings.vsCodeLmModelSelector.family.description": "La família del model de llenguatge (p. ex. gpt-4)", "settings.customStoragePath.description": "Ruta d'emmagatzematge personalitzada. Deixeu-la buida per utilitzar la ubicació predeterminada. Admet rutes absolutes (p. ex. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Habilitar correccions ràpides de Kilo Code.", "settings.autoImportSettingsPath.description": "Ruta a un fitxer de configuració de Kilo Code per importar automàticament en iniciar l'extensió. Admet rutes absolutes i rutes relatives al directori d'inici (per exemple, '~/Documents/kilo-code-settings.json'). Deixeu-ho en blanc per desactivar la importació automàtica.", "ghost.input.title": "Premeu 'Enter' per confirmar o 'Escape' per cancel·lar", "ghost.input.placeholder": "Descriviu què voleu fer...", "ghost.commands.generateSuggestions": "Kilo Code: <PERSON><PERSON>", "ghost.commands.displaySuggestions": "<PERSON>rar Edicions <PERSON>", "ghost.commands.cancelSuggestions": "Cancel·lar <PERSON><PERSON><PERSON>", "ghost.commands.applyCurrentSuggestion": "Aplicar Edició Suggerida Actual", "ghost.commands.applyAllSuggestions": "Aplicar Totes les Edicions Suggerides", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "<PERSON>r al Suggeriment Següent", "ghost.commands.goToPreviousSuggestion": "Anar al Suggeriment Anterior"}