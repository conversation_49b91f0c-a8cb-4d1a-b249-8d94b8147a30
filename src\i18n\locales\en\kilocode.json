{"info": {"settings_imported": "Settings imported successfully."}, "userFeedback": {"message_update_failed": "Failed to update message", "no_checkpoint_found": "No checkpoint found before this message", "message_updated": "Message updated successfully"}, "lowCreditWarning": {"title": "Low Credit Warning!", "message": "Check to see if you can top up with free credits or purchase some more!"}, "notLoggedInError": "Cannot complete request, make sure you are connected and logged in with the selected provider.\n\n{{error}}", "rules": {"actions": {"delete": "Delete", "confirmDelete": "Are you sure you want to delete {{filename}}?", "deleted": "Deleted {{filename}}"}, "errors": {"noWorkspaceFound": "No workspace folder found", "fileAlreadyExists": "File {{filename}} already exists", "failedToCreateRuleFile": "Failed to create rule file.", "failedToDeleteRuleFile": "Failed to delete rule file."}, "templates": {"workflow": {"description": "Workflow description here...", "stepsHeader": "## Steps", "step1": "Step 1", "step2": "Step 2"}, "rule": {"description": "Rule description here...", "guidelinesHeader": "## Guidelines", "guideline1": "Guideline 1", "guideline2": "Guideline 2"}}}, "commitMessage": {"activated": "Kilo Code commit message generator activated", "gitNotFound": "⚠️ Git repository not found or git not available", "gitInitError": "⚠️ Git initialization error: {{error}}", "generating": "<PERSON><PERSON>: Generating commit message...", "noChanges": "Kilo: No changes found to analyze", "generated": "<PERSON><PERSON>: Commit message generated!", "generationFailed": "<PERSON><PERSON>: Failed to generate commit message: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Generating message using unstaged changes", "activationFailed": "<PERSON><PERSON>: Failed to activate message generator: {{error}}", "providerRegistered": "Kilo: Commit message provider registered"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Complete", "disabled": "$(circle-slash) Kilo Complete", "warning": "$(warning) Kilo Complete", "tooltip": {"basic": "Kilo Code Autocomplete", "disabled": "Kilo Code Autocomplete (disabled)", "tokenError": "A valid token must be set to use autocomplete", "lastCompletion": "Last completion:", "sessionTotal": "Session total cost:", "model": "Model:"}, "cost": {"zero": "$0.00", "lessThanCent": "<$0.01"}}, "toggleMessage": "Kilo Complete {{status}}"}, "ghost": {"progress": {"title": "Kilo Code", "analyzing": "Analyzing your code...", "generating": "Generating suggested edits...", "processing": "Processing suggested edits...", "showing": "Displaying suggested edits..."}, "input": {"title": "Kilo Code: Quick Task", "placeholder": "e.g., 'refactor this function to be more efficient'"}, "commands": {"generateSuggestions": "Kilo Code: Generate Suggested Edits", "displaySuggestions": "Display Suggested Edits", "cancelSuggestions": "Cancel Suggested Edits", "applyCurrentSuggestion": "Apply Current Suggested Edit", "applyAllSuggestions": "Apply All Suggested Edits", "promptCodeSuggestion": "Quick Task", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: Suggested Edits"}, "chatParticipant": {"fullName": "Kilo Code Agent", "name": "Agent", "description": "I can help you with quick tasks and suggested edits."}}}