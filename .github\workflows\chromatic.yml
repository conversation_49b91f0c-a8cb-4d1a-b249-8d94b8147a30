name: Chromatic Visual Testing

on:
    workflow_dispatch:
    push:
        branches: [main]
    pull_request:
        types: [opened, reopened, ready_for_review, synchronize]
        branches: [main]

# Add concurrency to cancel in-progress jobs when a new workflow is triggered
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

env:
    NODE_VERSION: 20.19.2
    PNPM_VERSION: 10.8.1

jobs:
    chromatic:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  # Chromatic needs full git history for baseline comparison
                  fetch-depth: 0

            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: "pnpm"

            - name: Install dependencies
              run: pnpm install

            - name: Build Storybook
              run: pnpm --filter @roo-code/storybook build-storybook

            - name: Run Chromatic
              id: chromatic
              uses: chromaui/action@latest
              with:
                  projectToken: chpt_f6ec377cea9b457
                  storybookBaseDir: apps/storybook/
                  storybookBuildDir: apps/storybook/storybook-static
                  onlyChanged: true
                  exitOnceUploaded: ${{ github.event_name == 'pull_request' }}
                  # Skip dependabot PRs and renovate PRs using glob patterns
                  skip: "@(dependabot/**|renovate/**)"
                  # Enable GitHub PR comments integration
                  token: ${{ secrets.GITHUB_TOKEN }}
                  # Auto-accept changes on main branch to keep it clean
                  autoAcceptChanges: ${{ github.ref == 'refs/heads/main' && 'main' || '' }}

            # Add a summary of the Chromatic build results to the workflow summary
            - name: Chromatic Build Summary
              if: always() && steps.chromatic.outputs.url != ''
              run: |
                  echo "## Chromatic Build Results" >> $GITHUB_STEP_SUMMARY
                  echo "" >> $GITHUB_STEP_SUMMARY
                  echo "### Build Information" >> $GITHUB_STEP_SUMMARY
                  echo "- **Build URL:** [View on Chromatic](${{ steps.chromatic.outputs.url }})" >> $GITHUB_STEP_SUMMARY
                  echo "- **Changes:** ${{ steps.chromatic.outputs.changeCount || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
                  echo "- **Components:** ${{ steps.chromatic.outputs.componentCount || 'N/A' }}" >> $GITHUB_STEP_SUMMARY
                  echo "- **Snapshots:** ${{ steps.chromatic.outputs.specCount || 'N/A' }}" >> $GITHUB_STEP_SUMMARY

                  if [[ "${{ steps.chromatic.outputs.changeCount }}" != "0" ]]; then
                    echo "" >> $GITHUB_STEP_SUMMARY
                    echo "⚠️ **Visual changes detected!** Please review in Chromatic." >> $GITHUB_STEP_SUMMARY
                  else
                    echo "" >> $GITHUB_STEP_SUMMARY
                    echo "✅ **No visual changes detected.**" >> $GITHUB_STEP_SUMMARY
                  fi
