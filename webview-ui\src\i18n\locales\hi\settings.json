{"common": {"save": "सहेजें", "done": "पूर्ण", "cancel": "रद्<PERSON> करें", "reset": "रीसेट करें", "select": "चुनें", "add": "हेडर जोड़ें", "remove": "हटाएं"}, "header": {"title": "सेटिंग्स", "saveButtonTooltip": "परिवर्तन सहेजें", "nothingChangedTooltip": "कुछ भी नहीं बदला", "doneButtonTooltip": "असहेजे परिवर्तनों को छोड़ें और सेटिंग्स पैनल बंद करें"}, "unsavedChangesDialog": {"title": "असहेजे परिवर्तन", "description": "क्या आप परिवर्तनों को छोड़कर जारी रखना चाहते हैं?", "cancelButton": "रद्<PERSON> करें", "discardButton": "परिवर्तन छोड़ें"}, "sections": {"providers": "प्रदाता", "autoApprove": "अनुमोदन", "browser": "ब्राउज़र", "checkpoints": "चेकपॉइंट", "notifications": "सूचनाएँ", "contextManagement": "संदर<PERSON>भ", "terminal": "टर्मिनल", "prompts": "प्रॉम्प्ट्स", "experimental": "प्रायोगिक", "language": "भाषा", "about": "परिचय", "display": "प्रदर्शित करें"}, "prompts": {"description": "प्रॉम्प्ट्स को बेहतर बनाना, कोड की व्याख्या करना और समस्याओं को ठीक करना जैसी त्वरित कार्रवाइयों के लिए उपयोग किए जाने वाले सहायक प्रॉम्प्ट्स को कॉन्फ़िगर करें। ये प्रॉम्प्ट्स Kilo Code को सामान्य विकास कार्यों के लिए बेहतर सहायता प्रदान करने में मदद करते हैं।"}, "codeIndex": {"title": "कोडबेस इंडेक्सिंग", "enableLabel": "कोडबेस इंडेक्सिंग सक्षम करें", "enableDescription": "बेहतर खोज और संदर्भ समझने के लिए कोड इंडेक्सिंग सक्षम करें", "providerLabel": "एम्बेडिंग प्रदाता", "selectProviderPlaceholder": "प्रदाता चुनें", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "API कुंजी:", "geminiApiKeyPlaceholder": "अपना जेमिनी एपीआई कुंजी दर्ज करें", "openaiCompatibleProvider": "OpenAI संगत", "openAiKeyLabel": "OpenAI API कुंजी", "openAiKeyPlaceholder": "अपना OpenAI API कुंजी दर्ज करें", "openAiCompatibleBaseUrlLabel": "<PERSON><PERSON><PERSON><PERSON>", "openAiCompatibleApiKeyLabel": "<PERSON> कुंजी", "openAiCompatibleApiKeyPlaceholder": "अपना API कुंजी दर्ज करें", "openAiCompatibleModelDimensionLabel": "एम्बेडिंग आयाम:", "modelDimensionLabel": "मॉडल आयाम", "openAiCompatibleModelDimensionPlaceholder": "उदा., 1536", "openAiCompatibleModelDimensionDescription": "आपके मॉडल के लिए एम्बेडिंग आयाम (आउटपुट साइज)। इस मान के लिए अपने प्रदाता के दस्तावेज़ीकरण की जांच करें। सामान्य मान: 384, 768, 1536, 3072।", "modelLabel": "मॉडल", "selectModelPlaceholder": "मॉडल चुनें", "ollamaUrlLabel": "Ollama URL:", "qdrantUrlLabel": "Qdrant URL", "qdrantKeyLabel": "<PERSON><PERSON><PERSON> कुंजी:", "startIndexingButton": "शुरू करें", "clearIndexDataButton": "इंडेक्स साफ़ करें", "unsavedSettingsMessage": "इंडेक्सिंग प्रक्रिया शुरू करने से पहले कृपया अपनी सेटिंग्स सहेजें।", "clearDataDialog": {"title": "क्या आप सुनिश्चित हैं?", "description": "यह क्रिया पूर्ववत नहीं की जा सकती। यह आपके कोडबेस इंडेक्स डेटा को स्थायी रूप से हटा देगी।", "cancelButton": "रद्<PERSON> करें", "confirmButton": "डेटा साफ़ करें"}, "description": "अपने प्रोजेक्ट की सिमेंटिक खोज को सक्षम करने के लिए कोडबेस इंडेक्सिंग सेटिंग्स कॉन्फ़िगर करें। <0>और जानें</0>", "statusTitle": "स्थिति", "settingsTitle": "इंडेक्सिंग सेटिंग्स", "disabledMessage": "कोडबेस इंडेक्सिंग वर्तमान में अक्षम है। इंडेक्सिंग विकल्पों को कॉन्फ़िगर करने के लिए इसे ग्लोबल सेटिंग्स में सक्षम करें।", "embedderProviderLabel": "एम्बेडर प्रदाता", "modelPlaceholder": "मॉडल नाम दर्ज करें", "selectModel": "एक मॉडल चुनें", "ollamaBaseUrlLabel": "<PERSON><PERSON><PERSON>", "qdrantApiKeyLabel": "<PERSON>drant API कुंजी", "qdrantApiKeyPlaceholder": "अपनी Qdrant API कुंजी दर्ज करें (वैकल्पिक)", "setupConfigLabel": "सेटअप", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "सेटिंग्स सहेजने में विफल", "modelDimensions": "({{dimension}} आयाम)", "saveSuccess": "सेटिंग्स सफलतापूर्वक सहेजी गईं", "saving": "सहेज रहे हैं...", "saveSettings": "सहेजें", "indexingStatuses": {"standby": "स्टैंडबाई", "indexing": "इंडेक्सिंग", "indexed": "इंडेक्स किया गया", "error": "त्रुटि"}, "close": "ब<PERSON><PERSON> करें", "validation": {"invalidQdrantUrl": "अमान्य Qdrant URL", "invalidOllamaUrl": "अमान्य Ollama URL", "invalidBaseUrl": "अमान्य बेस URL", "qdrantUrlRequired": "Qdrant URL आवश्यक है", "openaiApiKeyRequired": "OpenAI API कुंजी आवश्यक है", "modelSelectionRequired": "मॉडल चयन आवश्यक है", "apiKeyRequired": "API कुंजी आवश्यक है", "modelIdRequired": "मॉडल आईडी आवश्यक है", "modelDimensionRequired": "मॉडल आयाम आवश्यक है", "geminiApiKeyRequired": "Gemini API कुंजी आवश्यक है", "ollamaBaseUrlRequired": "<PERSON><PERSON><PERSON> आधार URL आवश्यक है", "baseUrlRequired": "आधार URL आवश्यक है", "modelDimensionMinValue": "मॉडल आयाम 0 से बड़ा होना चाहिए"}, "advancedConfigLabel": "उन्नत कॉन्फ़िगरेशन", "searchMinScoreLabel": "खोज स्कोर थ्रेसहोल्ड", "searchMinScoreDescription": "खोज परिणामों के लिए आवश्यक न्यूनतम समानता स्कोर (0.0-1.0)। कम मान अधिक परिणाम लौटाते हैं लेकिन कम प्रासंगिक हो सकते हैं। उच्च मान कम लेकिन अधिक प्रासंगिक परिणाम लौटाते हैं।", "searchMinScoreResetTooltip": "डिफ़ॉल्ट मान पर रीसेट करें (0.4)", "searchMaxResultsLabel": "अधिकतम खोज परिणाम", "searchMaxResultsDescription": "कोडबेस इंडेक्स को क्वेरी करते समय वापस करने के लिए खोज परिणामों की अधिकतम संख्या। उच्च मान अधिक संदर्भ प्रदान करते हैं लेकिन कम प्रासंगिक परिणाम शामिल कर सकते हैं।", "resetToDefault": "डिफ़ॉल्ट पर रीसेट करें"}, "autoApprove": {"description": "Kilo Code को अनुमोदन की आवश्यकता के बिना स्वचालित रूप से ऑपरेशन करने की अनुमति दें। इन सेटिंग्स को केवल तभी सक्षम करें जब आप AI पर पूरी तरह से भरोसा करते हों और संबंधित सुरक्षा जोखिमों को समझते हों।", "readOnly": {"label": "पढ़ें", "description": "जब सक्षम होता है, तो Kilo Code आपके अनुमोदित बटन पर क्लिक किए बिना स्वचालित रूप से निर्देशिका सामग्री देखेगा और फाइलें पढ़ेगा।", "outsideWorkspace": {"label": "वर्कस्पेस के बाहर की फाइलें शामिल करें", "description": "<PERSON>lo Code को अनुमोदन की आवश्यकता के बिना वर्तमान वर्कस्पेस के बाहर की फाइलें पढ़ने की अनुमति दें।"}}, "write": {"label": "लिखें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से फाइलें बनाएँ और संपादित करें", "delayLabel": "लिखने के बाद विलंब ताकि डायग्नोस्टिक संभावित समस्याओं का पता लगा सकें", "outsideWorkspace": {"label": "वर्कस्पेस के बाहर की फाइलें शामिल करें", "description": "Kilo Code को अनुमोदन की आवश्यकता के बिना वर्तमान वर्कस्पेस के बाहर फाइलें बनाने और संपादित करने की अनुमति दें।"}, "protected": {"label": "संरक्षित फाइलें शामिल करें", "description": "Kilo Code को अनुमोदन की आवश्यकता के बिना संरक्षित फाइलें (.kilocodeignore और .kilocode/ कॉन्फ़िगरेशन फाइलें जैसी) बनाने और संपादित करने की अनुमति दें।"}}, "browser": {"label": "ब्राउज़र", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से ब्राउज़र क्रियाएँ करें — नोट: केवल तभी लागू होता है जब मॉडल कंप्यूटर उपयोग का समर्थन करता है"}, "retry": {"label": "पुनः प्रयास", "description": "जब सर्वर त्रुटि प्रतिक्रिया देता है तो स्वचालित रूप से विफल API अनुरोधों को पुनः प्रयास करें", "delayLabel": "अनुरोध को पुनः प्रयास करने से पहले विलंब"}, "mcp": {"label": "MCP", "description": "MCP सर्वर व्यू में व्यक्तिगत MCP टूल्स के स्वतः अनुमोदन को सक्षम करें (इस सेटिंग और टूल के \"हमेशा अनुमति दें\" चेकबॉक्स दोनों की आवश्यकता है)"}, "modeSwitch": {"label": "मोड", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से विभिन्न मोड के बीच स्विच करें"}, "subtasks": {"label": "उप-कार्य", "description": "अनुमोदन की आवश्यकता के बिना उप-कार्यों के निर्माण और पूर्णता की अनुमति दें"}, "followupQuestions": {"label": "प्रश्न", "description": "कॉन्फ़िगर किए गए टाइमआउट के बाद अनुवर्ती प्रश्नों के लिए पहले सुझाए गए उत्तर को स्वचालित रूप से चुनें", "timeoutLabel": "पहले उत्तर को स्वचालित रूप से चुनने से पहले प्रतीक्षा करने का समय"}, "execute": {"label": "निष्पादित करें", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से अनुमत टर्मिनल कमांड निष्पादित करें", "allowedCommands": "अनुमत स्वतः-निष्पादन कमांड", "allowedCommandsDescription": "कमांड प्रीफिक्स जो स्वचालित रूप से निष्पादित किए जा सकते हैं जब \"निष्पादन ऑपरेशन हमेशा अनुमोदित करें\" सक्षम है। सभी कमांड की अनुमति देने के लिए * जोड़ें (सावधानी से उपयोग करें)।", "commandPlaceholder": "कमांड प्रीफिक्स दर्ज करें (उदा. 'git ')", "addButton": "जोड़ें"}, "showMenu": {"label": "चैट व्यू में स्वतः अनुमोदन मेनू दिखाएँ", "description": "जब सक्षम होता है, तो स्वतः अनुमोदन मेनू चैट व्यू के निचले भाग में दिखाई देगा, जिससे स्वतः अनुमोदन सेटिंग्स तक त्वरित पहुंच मिलेगी"}, "updateTodoList": {"label": "टूडू", "description": "अनुमोदन की आवश्यकता के बिना स्वचालित रूप से टूडू सूची अपडेट करें"}, "apiRequestLimit": {"title": "अधिकतम अनुरोध", "description": "कार्य जारी रखने के लिए अनुमति मांगने से पहले स्वचालित रूप से इतने API अनुरोध करें।", "unlimited": "असीमित"}}, "providers": {"providerDocumentation": "{{provider}} दस्तावेज़ीकरण", "configProfile": "कॉन्फिगरेशन प्रोफाइल", "description": "विभिन्न API कॉन्फ़िगरेशन सहेजें ताकि प्रदाताओं और सेटिंग्स के बीच त्वरित रूप से स्विच कर सकें।", "apiProvider": "API प्रदाता", "model": "मॉडल", "nameEmpty": "नाम खाली नहीं हो सकता", "nameExists": "इस नाम वाला प्रोफ़ाइल पहले से मौजूद है", "deleteProfile": "प्रोफ़ाइल हटाएं", "invalidArnFormat": "अमान्य ARN प्रारूप। कृपया ऊपर दिए गए उदाहरण देखें।", "enterNewName": "नया नाम दर्ज करें", "addProfile": "प्रोफ़ाइल जोड़ें", "renameProfile": "प्रोफ़ाइल का नाम बदलें", "newProfile": "नया कॉन्फ़िगरेशन प्रोफ़ाइल", "enterProfileName": "प्रोफ़ाइल नाम दर्ज करें", "createProfile": "प्रोफ़ाइल बनाएं", "cannotDeleteOnlyProfile": "केवल एकमात्र प्रोफ़ाइल को हटाया नहीं जा सकता", "searchPlaceholder": "प्रोफ़ाइल खोजें", "searchProviderPlaceholder": "प्रदाता खोजें", "noProviderMatchFound": "कोई प्रदाता नहीं मिला", "noMatchFound": "कोई मिलान प्रोफ़ाइल नहीं मिला", "vscodeLmDescription": "VS कोड भाषा मॉडल API आपको अन्य VS कोड एक्सटेंशन (जैसे GitHub Copilot) द्वारा प्रदान किए गए मॉडल चलाने की अनुमति देता है। शुरू करने का सबसे आसान तरीका VS कोड मार्केटप्लेस से Copilot और Copilot चैट एक्सटेंशन इंस्टॉल करना है।", "awsCustomArnUse": "आप जिस मॉडल का उपयोग करना चाहते हैं, उसके लिए एक वैध AWS बेडरॉक ARN दर्ज करें। प्रारूप उदाहरण:", "awsCustomArnDesc": "सुनिश्चित करें कि ARN में क्षेत्र ऊपर चयनित AWS क्षेत्र से मेल खाता है।", "openRouterApiKey": "OpenRouter API कुंजी", "getOpenRouterApiKey": "OpenRouter API कुंजी प्राप्त करें", "apiKeyStorageNotice": "API कुंजियाँ VSCode के सुरक्षित स्टोरेज में सुरक्षित रूप से संग्रहीत हैं", "cerebras": {"apiKey": "Cerebras <PERSON> कुंजी", "getApiKey": "Cerebras API कुंजी प्राप्त करें"}, "glamaApiKey": "Glama API कुंजी", "getGlamaApiKey": "<PERSON>lama API कुंजी प्राप्त करें", "useCustomBaseUrl": "कस्टम बेस URL का उपयोग करें", "useReasoning": "तर्क सक्षम करें", "useHostHeader": "कस्टम होस्ट हेडर का उपयोग करें", "useLegacyFormat": "पुराने OpenAI API प्रारूप का उपयोग करें", "customHeaders": "कस्टम हेडर्स", "headerName": "हेडर नाम", "headerValue": "हेडर मूल्य", "noCustomHeaders": "कोई कस्टम हेडर परिभाषित नहीं है। एक जोड़ने के लिए + बटन पर क्लिक करें।", "requestyApiKey": "Requesty API कुंजी", "refreshModels": {"label": "मॉडल रिफ्रेश करें", "hint": "नवीनतम मॉडल देखने के लिए कृपया सेटिंग्स को फिर से खोलें।", "loading": "मॉडल सूची अपडेट हो रही है...", "success": "मॉडल सूची सफलतापूर्वक अपडेट की गई!", "error": "मॉडल सूची अपडेट करने में विफल। कृपया पुनः प्रयास करें।"}, "getRequestyApiKey": "Requesty API कुंजी प्राप्त करें", "openRouterTransformsText": "संदर्भ आकार के लिए प्रॉम्प्ट और संदेश श्रृंखलाओं को संपीड़ित करें (<a>OpenRouter ट्रांसफॉर्म</a>)", "anthropicApiKey": "Anthropic API कुंजी", "getAnthropicApiKey": "Anthropic API कुंजी प्राप्त करें", "anthropicUseAuthToken": "X-Api-Key के बजाय Anthropic API कुंजी को Authorization हेडर के रूप में पास करें", "chutesApiKey": "Chutes <PERSON> कुंजी", "getChutesApiKey": "<PERSON><PERSON> API कुंजी प्राप्त करें", "deepSeekApiKey": "DeepSeek API कुंजी", "getDeepSeekApiKey": "DeepSeek API कुंजी प्राप्त करें", "geminiApiKey": "Gemini API कुंजी", "getGroqApiKey": "<PERSON><PERSON><PERSON> <PERSON> कुंजी प्राप्त करें", "groqApiKey": "<PERSON><PERSON><PERSON> <PERSON> कुंजी", "getGeminiApiKey": "Gemini API कुंजी प्राप्त करें", "openAiApiKey": "OpenAI API कुंजी", "apiKey": "<PERSON> कुंजी", "openAiBaseUrl": "बेस URL", "getOpenAiApiKey": "OpenAI API कुंजी प्राप्त करें", "mistralApiKey": "Mistral API कुंजी", "getMistralApiKey": "Mistral / Codestral API कुंजी प्राप्त करें", "codestralBaseUrl": "Codestral बेस URL (वैकल्पिक)", "codestralBaseUrlDesc": "Codestral मॉडल के लिए वैकल्पिक URL सेट करें।", "xaiApiKey": "xAI API कुंजी", "getXaiApiKey": "xAI API कुंजी प्राप्त करें", "litellmApiKey": "LiteLLM API कुंजी", "litellmBaseUrl": "LiteLL<PERSON> आधार URL", "awsCredentials": "AWS क्रेडेंशियल्स", "awsProfile": "AWS प्रोफाइल", "awsProfileName": "AWS प्रोफाइल नाम", "awsAccessKey": "A<PERSON> एक्सेस कुंजी", "awsSecretKey": "A<PERSON> सीक्रेट कुंजी", "awsSessionToken": "AWS सत्र टोकन", "awsRegion": "<PERSON><PERSON> क्षेत्र", "awsCrossRegion": "क्रॉस-क्षेत्र अनुमान का उपयोग करें", "awsBedrockVpc": {"useCustomVpcEndpoint": "कस्टम VPC एंडपॉइंट का उपयोग करें", "vpcEndpointUrlPlaceholder": "VPC एंडपॉइंट URL दर्ज करें (वैकल्पिक)", "examples": "उदाहरण:"}, "enablePromptCaching": "प्रॉम्प्ट कैशिंग सक्षम करें", "enablePromptCachingTitle": "समर्थित मॉडल के लिए प्रदर्शन में सुधार और लागत को कम करने के लिए प्रॉम्प्ट कैशिंग सक्षम करें।", "cacheUsageNote": "नोट: यदि आप कैश उपयोग नहीं देखते हैं, तो एक अलग मॉडल चुनने का प्रयास करें और फिर अपने वांछित मॉडल को पुनः चुनें।", "vscodeLmModel": "भाषा मॉडल", "vscodeLmWarning": "नोट: यह एक बहुत ही प्रायोगिक एकीकरण है और प्रदाता समर्थन भिन्न होगा। यदि आपको किसी मॉडल के समर्थित न होने की त्रुटि मिलती है, तो यह प्रदाता की ओर से एक समस्या है।", "googleCloudSetup": {"title": "Google Cloud Vertex AI का उपयोग करने के लिए, आपको आवश्यकता है:", "step1": "1. Google Cloud खाता बनाएं, Vertex AI API सक्षम करें और वांछित Claude मॉडल सक्षम करें।", "step2": "2. Google Cloud CLI इंस्टॉल करें और एप्लिकेशन डिफ़ॉल्ट क्रेडेंशियल्स कॉन्फ़िगर करें।", "step3": "3. या क्रेडेंशियल्स के साथ एक सर्विस अकाउंट बनाएं।"}, "googleCloudCredentials": "Google Cloud क्रेडेंशियल्स", "googleCloudKeyFile": "Google Cloud कुंजी फ़ाइल पथ", "googleCloudProjectId": "Google Cloud प्रोजेक्ट ID", "googleCloudRegion": "Google Cloud क्षेत्र", "lmStudio": {"baseUrl": "बेस URL (वैकल्पिक)", "modelId": "मॉडल ID", "speculativeDecoding": "स्पेक्युलेटिव डिकोडिंग सक्षम करें", "draftModelId": "ड्राफ्ट मॉडल ID", "draftModelDesc": "स्पेक्युलेटिव डिकोडिंग के सही काम करने के लिए ड्राफ्ट मॉडल को समान मॉडल परिवार से होना चाहिए।", "selectDraftModel": "ड्राफ्ट मॉडल चुनें", "noModelsFound": "कोई ड्राफ्ट मॉडल नहीं मिला। कृपया सुनिश्चित करें कि LM Studio सर्वर मोड सक्षम के साथ चल रहा है।", "description": "LM Studio आपको अपने कंप्यूटर पर स्थानीय रूप से मॉडल चलाने की अनुमति देता है। आरंभ करने के निर्देशों के लिए, उनकी <a>क्विकस्टार्ट गाइड</a> देखें। आपको इस एक्सटेंशन के साथ उपयोग करने के लिए LM Studio की <b>स्थानीय सर्वर</b> सुविधा भी शुरू करनी होगी। <span>नोट:</span> Kilo Code जटिल प्रॉम्प्ट्स का उपयोग करता है और Claude मॉडल के साथ सबसे अच्छा काम करता है। कम क्षमता वाले मॉडल अपेक्षित रूप से काम नहीं कर सकते हैं।"}, "ollama": {"baseUrl": "बेस URL (वैकल्पिक)", "modelId": "मॉडल ID", "description": "<PERSON><PERSON>ma आपको अपने कंप्यूटर पर स्थानीय रूप से मॉडल चलाने की अनुमति देता है। आरंभ करने के निर्देशों के लिए, उनकी क्विकस्टार्ट गाइड देखें।", "warning": "नोट: Kilo Code जटिल प्रॉम्प्ट्स का उपयोग करता है और Claude मॉडल के साथ सबसे अच्छा काम करता है। कम क्षमता वाले मॉडल अपेक्षित रूप से काम नहीं कर सकते हैं।"}, "unboundApiKey": "Unbound API कुंजी", "getUnboundApiKey": "Unbound API कुंजी प्राप्त करें", "unboundRefreshModelsSuccess": "मॉडल सूची अपडेट हो गई है! अब आप नवीनतम मॉडलों में से चुन सकते हैं।", "unboundInvalidApiKey": "अमान्य API कुंजी। कृपया अपनी API कुंजी की जांच करें और पुनः प्रयास करें।", "humanRelay": {"description": "कोई API कुंजी आवश्यक नहीं है, लेकिन उपयोगकर्ता को वेब चैट AI में जानकारी कॉपी और पेस्ट करने में मदद करनी होगी।", "instructions": "उपयोग के दौरान, एक डायलॉग बॉक्स पॉप अप होगा और वर्तमान संदेश स्वचालित रूप से क्लिपबोर्ड पर कॉपी हो जाएगा। आपको इन्हें AI के वेब संस्करणों (जैसे ChatGPT या Claude) में पेस्ट करना होगा, फिर AI की प्रतिक्रिया को डायलॉग बॉक्स में वापस कॉपी करें और पुष्टि बटन पर क्लिक करें।"}, "openRouter": {"providerRouting": {"title": "OpenRouter प्रदाता रूटिंग", "description": "OpenRouter आपके मॉडल के लिए सर्वोत्तम उपलब्ध प्रदाताओं को अनुरोध भेजता है। डिफ़ॉल्ट रूप से, अपटाइम को अधिकतम करने के लिए अनुरोधों को शीर्ष प्रदाताओं के बीच संतुलित किया जाता है। हालांकि, आप इस मॉडल के लिए उपयोग करने के लिए एक विशिष्ट प्रदाता चुन सकते हैं।", "learnMore": "प्रदाता रूटिंग के बारे में अधिक जानें"}}, "customModel": {"capabilities": "अपने कस्टम OpenAI-संगत मॉडल के लिए क्षमताओं और मूल्य निर्धारण को कॉन्फ़िगर करें। मॉडल क्षमताओं को निर्दिष्ट करते समय सावधान रहें, क्योंकि वे Kilo Code के प्रदर्शन को प्रभावित कर सकती हैं।", "maxTokens": {"label": "अधिकतम आउटपुट टोकन", "description": "मॉडल एक प्रतिक्रिया में अधिकतम कितने टोकन जनरेट कर सकता है। (सर्वर को अधिकतम टोकन सेट करने की अनुमति देने के लिए -1 निर्दिष्ट करें।)"}, "contextWindow": {"label": "संदर्भ विंडो आकार", "description": "कुल टोकन (इनपुट + आउटपुट) जो मॉडल प्रोसेस कर सकता है।"}, "imageSupport": {"label": "छवि समर्थन", "description": "क्या यह मॉडल छवियों को प्रोसेस और समझने में सक्षम है?"}, "computerUse": {"label": "कंप्यूटर उपयोग", "description": "क्या यह मॉडल ब्राउज़र के साथ इंटरैक्ट करने में सक्षम है? (उदा. <PERSON> 3.7 Sonnet)।"}, "promptCache": {"label": "प्रॉम्प्ट कैशिंग", "description": "क्या यह मॉडल प्रॉम्प्ट्स को कैश करने में सक्षम है?"}, "pricing": {"input": {"label": "इनपुट मूल्य", "description": "इनपुट/प्रॉम्प्ट में प्रति मिलियन टोकन की लागत। यह मॉडल को संदर्भ और निर्देश भेजने की लागत को प्रभावित करता है।"}, "output": {"label": "आउटपुट मूल्य", "description": "मॉडल की प्रतिक्रिया में प्रति मिलियन टोकन की लागत। यह जनरेट की गई सामग्री और पूर्णताओं की लागत को प्रभावित करता है।"}, "cacheReads": {"label": "कैश रीड्स मूल्य", "description": "कैश से पढ़ने के लिए प्रति मिलियन टोकन की लागत। यह वह मूल्य है जो कैश की गई प्रतिक्रिया प्राप्त करने पर लगाया जाता है।"}, "cacheWrites": {"label": "कैश राइट्स मूल्य", "description": "कैश में लिखने के लिए प्रति मिलियन टोकन की लागत। यह वह मूल्य है जो पहली बार प्रॉम्प्ट को कैश करने पर लगाया जाता है।"}}, "resetDefaults": "डिफ़ॉल्ट पर रीसेट करें"}, "rateLimitSeconds": {"label": "दर सीमा", "description": "API अनुरोधों के बीच न्यूनतम समय।"}, "reasoningEffort": {"label": "मॉडल तर्क प्रयास", "high": "उच्च", "medium": "मध्यम", "low": "निम्न"}, "setReasoningLevel": "तर्क प्रयास सक्षम करें", "claudeCode": {"pathLabel": "क्लाउड कोड पथ", "description": "आपके क्लाउड कोड सीएलआई का वैकल्पिक पथ। यदि सेट नहीं है तो डिफ़ॉल्ट 'claude' है।", "placeholder": "डिफ़ॉल्ट: claude"}, "geminiCli": {"description": "यह प्रदाता Gemini CLI टूल से OAuth प्रमाणीकरण का उपयोग करता है और API कुंजियों की आवश्यकता नहीं है।", "oauthPath": "OAuth क्रेडेंशियल पथ (वैकल्पिक)", "oauthPathDescription": "OAuth क्रेडेंशियल फ़ाइल का पथ। डिफ़ॉल्ट स्थान (~/.gemini/oauth_creds.json) का उपयोग करने के लिए खाली छोड़ें।", "instructions": "यदि आपने अभी तक प्रमाणीकरण नहीं किया है, तो कृपया पहले", "instructionsContinued": "को अपने टर्मिनल में चलाएं।", "setupLink": "Gemini CLI सेटअप निर्देश", "requirementsTitle": "महत्वपूर्ण आवश्यकताएं", "requirement1": "पहले, आपको Gemini CLI टूल इंस्टॉल करना होगा", "requirement2": "फिर, अपने टर्मिनल में gemini चलाएं और सुनिश्चित करें कि आप Google से लॉग इन करें", "requirement3": "केवल व्यक्तिगत Google खातों के साथ काम करता है (Google Workspace खाते नहीं)", "requirement4": "API कुंजियों का उपयोग नहीं करता - प्रमाणीकरण OAuth के माध्यम से संभाला जाता है", "requirement5": "Gemini CLI टूल को पहले इंस्टॉल और प्रमाणित करने की आवश्यकता है", "freeAccess": "<PERSON>A<PERSON> प्रमाणीकरण के माध्यम से मुफ्त पहुंच"}}, "browser": {"enable": {"label": "ब्राउज़र टूल सक्षम करें", "description": "जब सक्षम होता है, तो Kilo Code कंप्यूटर उपयोग का समर्थन करने वाले मॉडल का उपयोग करते समय वेबसाइटों के साथ बातचीत करने के लिए ब्राउज़र का उपयोग कर सकता है। <0>अधिक जानें</0>"}, "viewport": {"label": "व्यूपोर्ट आकार", "description": "ब्राउज़र इंटरैक्शन के लिए व्यूपोर्ट आकार चुनें। यह वेबसाइटों के प्रदर्शन और उनके साथ बातचीत को प्रभावित करता है।", "options": {"largeDesktop": "बड़ा डेस्कटॉप (1280x800)", "smallDesktop": "छोटा डेस्कटॉप (900x600)", "tablet": "टैबलेट (768x1024)", "mobile": "मोबाइल (360x640)"}}, "screenshotQuality": {"label": "स्क्रीनशॉट गुणवत्ता", "description": "ब्राउज़र स्क्रीनशॉट की WebP गुणवत्ता समायोजित करें। उच्च मान स्पष्ट स्क्रीनशॉट प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "remote": {"label": "दूरस्थ ब्राउज़र कनेक्शन का उपयोग करें", "description": "रिमोट डीबगिंग सक्षम के साथ चल रहे Chrome ब्राउज़र से कनेक्ट करें (--remote-debugging-port=9222)।", "urlPlaceholder": "कस्टम URL (उदा. http://localhost:9222)", "testButton": "कनेक्शन का परीक्षण करें", "testingButton": "परीक्षण हो रहा है...", "instructions": "DevTools प्रोटोकॉल होस्ट पता दर्ज करें या Chrome स्थानीय इंस्टेंस स्वतः खोजने के लिए खाली छोड़ दें। टेस्ट कनेक्शन बटन यदि प्रदान किया गया है तो कस्टम URL का प्रयास करेगा, या यदि फ़ील्ड खाली है तो स्वतः खोज करेगा।"}}, "checkpoints": {"enable": {"label": "स्वचालित चेकपॉइंट सक्षम करें", "description": "जब सक्षम होता है, तो Kilo Code कार्य निष्पादन के दौरान स्वचालित रूप से चेकपॉइंट बनाएगा, जिससे परिवर्तनों की समीक्षा करना या पहले की स्थितियों पर वापस जाना आसान हो जाएगा। <0>अधिक जानें</0>"}}, "notifications": {"sound": {"label": "ध्वनि प्रभाव सक्षम करें", "description": "जब सक्षम होता है, तो Kilo Code सूचनाओं और घटनाओं के लिए ध्वनि प्रभाव चलाएगा।", "volumeLabel": "वॉल्यूम"}, "tts": {"label": "टेक्स्ट-टू-स्पीच सक्षम करें", "description": "जब सक्षम होता है, तो Kilo Code टेक्स्ट-टू-स्पीच का उपयोग करके अपनी प्रतिक्रियाओं को बोलकर पढ़ेगा।", "speedLabel": "गति"}}, "contextManagement": {"description": "AI के संदर्भ विंडो में शामिल जानकारी को नियंत्रित करें, जो token उपयोग और प्रतिक्रिया गुणवत्ता को प्रभावित करता है", "autoCondenseContextPercent": {"label": "बुद्धिमान संदर्भ संघनन को ट्रिगर करने की सीमा", "description": "जब संदर्भ विंडो इस सीमा तक पहुंचती है, तो Kilo Code इसे स्वचालित रूप से संघनित कर देगा।"}, "condensingApiConfiguration": {"label": "संदर्भ संघनन के लिए API कॉन्फ़िगरेशन", "description": "संदर्भ संघनन कार्यों के लिए किस API कॉन्फ़िगरेशन का उपयोग करना है, यह चुनें। वर्तमान सक्रिय कॉन्फ़िगरेशन का उपयोग करने के लिए अचयनित छोड़ें।", "useCurrentConfig": "डिफ़ॉल्ट"}, "customCondensingPrompt": {"label": "कस्टम संदर्भ संघनन प्रॉम्प्ट", "description": "संदर्भ संघनन के लिए कस्टम सिस्टम प्रॉम्प्ट। डिफ़ॉल्ट प्रॉम्प्ट का उपयोग करने के लिए खाली छोड़ें।", "placeholder": "अपना कस्टम संघनन प्रॉम्प्ट यहाँ दर्ज करें...\n\nआप डिफ़ॉल्ट प्रॉम्प्ट जैसी ही संरचना का उपयोग कर सकते हैं:\n- पिछली बातचीत\n- वर्तमान कार्य\n- प्रमुख तकनीकी अवधारणाएँ\n- प्रासंगिक फ़ाइलें और कोड\n- समस्या समाधान\n- लंबित कार्य और अगले चरण", "reset": "डिफ़ॉल्ट पर रीसेट करें", "hint": "खाली = डिफ़ॉल्ट प्रॉम्प्ट का उपयोग करें"}, "autoCondenseContext": {"name": "बुद्धिमान संदर्भ संघनन को स्वचालित रूप से ट्रिगर करें", "description": "जब सक्षम हो, तो Kilo Code स्वचालित रूप से संदर्भ को संघनित करेगा जब सीमा पहुंच जाएगी। जब अक्षम हो, तो आप अभी भी मैन्युअल रूप से संदर्भ संघनन को ट्रिगर कर सकते हैं।"}, "openTabs": {"label": "खुले टैब संदर्भ सीमा", "description": "संदर्भ में शामिल करने के लिए VSCode खुले टैब की अधिकतम संख्या। उच्च मान अधिक संदर्भ प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "workspaceFiles": {"label": "वर्कस्पेस फाइल संदर्भ सीमा", "description": "वर्तमान कार्य निर्देशिका विवरण में शामिल करने के लिए फाइलों की अधिकतम संख्या। उच्च मान अधिक संदर्भ प्रदान करते हैं लेकिन token उपयोग बढ़ाते हैं।"}, "rooignore": {"label": "सूचियों और खोजों में .kilocodeignore फाइलें दिखाएँ", "description": "जब सक्षम होता है, .kilocodeignore में पैटर्न से मेल खाने वाली फाइलें लॉक प्रतीक के साथ सूचियों में दिखाई जाएंगी। जब अक्षम होता है, ये फाइलें फाइल सूचियों और खोजों से पूरी तरह छिपा दी जाएंगी।"}, "maxReadFile": {"label": "फ़ाइल पढ़ने का स्वचालित काटने की सीमा", "description": "जब मॉडल प्रारंभ/अंत मान नहीं देता है, तो Kilo Code इतनी पंक्तियाँ पढ़ता है। यदि यह संख्या फ़ाइल की कुल पंक्तियों से कम है, तो Kilo Code कोड परिभाषाओं का पंक्ति क्रमांक इंडेक्स बनाता है। विशेष मामले: -1 Kilo Code को पूरी फ़ाइल पढ़ने का निर्देश देता है (इंडेक्सिंग के बिना), और 0 कोई पंक्ति न पढ़ने और न्यूनतम संदर्भ के लिए केवल पंक्ति इंडेक्स प्रदान करने का निर्देश देता है। कम मान प्रारंभिक संदर्भ उपयोग को कम करते हैं, जो बाद में सटीक पंक्ति श्रेणी पढ़ने की अनुमति देता है। स्पष्ट प्रारंभ/अंत अनुरोध इस सेटिंग से सीमित नहीं हैं।", "lines": "पंक्तियाँ", "always_full_read": "हमेशा पूरी फ़ाइल पढ़ें"}, "maxConcurrentFileReads": {"label": "एक साथ फ़ाइल पढ़ने की सीमा", "description": "'read_file' टूल द्वारा एक साथ प्रोसेस की जा सकने वाली अधिकतम फ़ाइलों की संख्या। उच्च मान कई छोटी फ़ाइलों को पढ़ने की गति बढ़ा सकते हैं लेकिन मेमोरी उपयोग बढ़ा देते हैं।"}, "condensingThreshold": {"label": "संघनन ट्रिगर सीमा", "selectProfile": "प्रोफ़ाइल के लिए सीमा कॉन्फ़िगर करें", "defaultProfile": "वैश्विक डिफ़ॉल्ट (सभी प्रोफ़ाइल)", "defaultDescription": "जब संदर्भ इस प्रतिशत तक पहुंचता है, तो यह सभी प्रोफ़ाइल के लिए स्वचालित रूप से संघनित हो जाएगा जब तक कि उनकी कस्टम सेटिंग्स न हों", "profileDescription": "केवल इस प्रोफ़ाइल के लिए कस्टम सीमा (वैश्विक डिफ़ॉल्ट को ओवरराइड करता है)", "inheritDescription": "यह प्रोफ़ाइल वैश्विक डिफ़ॉल्ट सीमा को इनहेरिट करता है ({{threshold}}%)", "usesGlobal": "(वैश्विक {{threshold}}% का उपयोग करता है)"}}, "terminal": {"basic": {"label": "टर्मिनल सेटिंग्स: मूल", "description": "मूल टर्मिनल सेटिंग्स"}, "advanced": {"label": "टर्मिनल सेटिंग्स: उन्नत", "description": "निम्नलिखित विकल्पों को लागू करने के लिए टर्मिनल को पुनरारंभ करने की आवश्यकता हो सकती है"}, "outputLineLimit": {"label": "टर्मिनल आउटपुट सीमा", "description": "कमांड निष्पादित करते समय टर्मिनल आउटपुट में शामिल करने के लिए पंक्तियों की अधिकतम संख्या। पार होने पर पंक्तियाँ मध्य से हटा दी जाएंगी, token बचाते हुए। <0>अधिक जानें</0>"}, "shellIntegrationTimeout": {"label": "टर्मिनल शेल एकीकरण टाइमआउट", "description": "कमांड निष्पादित करने से पहले शेल एकीकरण के आरंभ होने के लिए प्रतीक्षा का अधिकतम समय। लंबे शेल स्टार्टअप समय वाले उपयोगकर्ताओं के लिए, यदि आप टर्मिनल में \"Shell Integration Unavailable\" त्रुटियाँ देखते हैं तो इस मान को बढ़ाने की आवश्यकता हो सकती है। <0>अधिक जानें</0>"}, "shellIntegrationDisabled": {"label": "टर्मिनल शेल एकीकरण अक्षम करें", "description": "इसे सक्षम करें यदि टर्मिनल कमांड सही ढंग से काम नहीं कर रहे हैं या आपको 'शेल एकीकरण अनुपलब्ध' त्रुटियाँ दिखाई देती हैं। यह कमांड चलाने के लिए एक सरल विधि का उपयोग करता है, कुछ उन्नत टर्मिनल सुविधाओं को दरकिनार करते हुए। <0>अधिक जानें</0>"}, "commandDelay": {"label": "टर्मिनल कमांड विलंब", "description": "कमांड निष्पादन के बाद जोड़ने के लिए मिलीसेकंड में विलंब। 0 का डिफ़ॉल्ट सेटिंग विलंब को पूरी तरह से अक्षम कर देता है। यह टाइमिंग समस्याओं वाले टर्मिनलों में कमांड आउटपुट को पूरी तरह से कैप्चर करने में मदद कर सकता है। अधिकांश टर्मिनलों में यह `PROMPT_COMMAND='sleep N'` सेट करके कार्यान्वित किया जाता है और Powershell प्रत्येक कमांड के अंत में `start-sleep` जोड़ता है। मूल रूप से यह VSCode बग#237208 के लिए एक समाधान था और इसकी आवश्यकता नहीं हो सकती है। <0>अधिक जानें</0>"}, "compressProgressBar": {"label": "प्रगति बार आउटपुट संपीड़ित करें", "description": "जब सक्षम किया जाता है, तो कैरिज रिटर्न (\\r) के साथ टर्मिनल आउटपुट को संसाधित करता है, जो वास्तविक टर्मिनल द्वारा सामग्री प्रदर्शित करने के तरीके का अनुकरण करता है। यह प्रगति बार के मध्यवर्ती स्थितियों को हटाता है, केवल अंतिम स्थिति को बनाए रखता है, जिससे अधिक प्रासंगिक जानकारी के लिए संदर्भ स्थान संरक्षित होता है। <0>अधिक जानें</0>"}, "powershellCounter": {"label": "PowerShell काउंटर समाधान सक्षम करें", "description": "सक्षम होने पर, कमांड के सही निष्पादन को सुनिश्चित करने के लिए PowerShell कमांड में एक काउंटर जोड़ता है। यह उन PowerShell टर्मिनलों के साथ मदद करता है जिनमें आउटपुट कैप्चर करने में समस्याएं हो सकती हैं। <0>अधिक जानें</0>"}, "zshClearEolMark": {"label": "ZSH EOL मार्क साफ़ करें", "description": "सक्षम होने पर, PROMPT_EOL_MARK='' सेट करके ZSH लाइन-समाप्ति मार्क को साफ़ करता है। यह कमांड आउटपुट की व्याख्या में समस्याओं को रोकता है जब आउटपुट '%' जैसे विशेष वर्णों के साथ समाप्त होता है। <0>अधिक जानें</0>"}, "zshOhMy": {"label": "Oh My Zsh एकीकरण सक्षम करें", "description": "सक्षम होने पर, Oh My Zsh शेल एकीकरण सुविधाओं को सक्षम करने के लिए ITERM_SHELL_INTEGRATION_INSTALLED=Yes सेट करता है। इस सेटिंग को लागू करने के लिए IDE को पुनरारंभ करने की आवश्यकता हो सकती है। <0>अधिक जानें</0>"}, "zshP10k": {"label": "Powerlevel10k एकीकरण सक्षम करें", "description": "सक्षम होने पर, Powerlevel10k शेल एकीकरण सुविधाओं को सक्षम करने के लिए POWERLEVEL9K_TERM_SHELL_INTEGRATION=true सेट करता है। <0>अधिक जानें</0>"}, "zdotdir": {"label": "ZDOTDIR प्रबंधन सक्षम करें", "description": "सक्षम होने पर, zsh शेल एकीकरण को सही ढंग से संभालने के लिए ZDOTDIR के लिए एक अस्थायी डायरेक्टरी बनाता है। यह आपके zsh कॉन्फ़िगरेशन को बनाए रखते हुए VSCode शेल एकीकरण को zsh के साथ सही ढंग से काम करने की सुनिश्चितता करता है। <0>अधिक जानें</0>"}, "inheritEnv": {"label": "पर्यावरण चर विरासत में लें", "description": "सक्षम होने पर, टर्मिनल VSCode के मूल प्रक्रिया से पर्यावरण चर विरासत में लेता है, जैसे उपयोगकर्ता प्रोफ़ाइल में परिभाषित शेल एकीकरण सेटिंग्स। यह VSCode की वैश्विक सेटिंग `terminal.integrated.inheritEnv` को सीधे टॉगल करता है। <0>अधिक जानें</0>"}}, "advanced": {"diff": {"label": "diffs के माध्यम से संपादन सक्षम करें", "description": "जब सक्षम होता है, Kilo Code फाइलों को तेजी से संपादित कर सकेगा और स्वचालित रूप से काटे गए पूर्ण-फाइल लेखन को अस्वीकार करेगा। नवीनतम Claude 4 Sonnet मॉडल के साथ सबसे अच्छा काम करता है।", "strategy": {"label": "Diff रणनीति", "options": {"standard": "मानक (एकल ब्लॉक)", "multiBlock": "प्रायोगिक: मल्टी-ब्लॉक diff", "unified": "प्रायोगिक: एकीकृत diff"}, "descriptions": {"standard": "मानक diff रणनीति एक समय में एक कोड ब्लॉक पर परिवर्तन लागू करती है।", "unified": "एकीकृत diff रणनीति diffs लागू करने के लिए कई दृष्टिकोण लेती है और सर्वोत्तम दृष्टिकोण चुनती है।", "multiBlock": "मल्टी-ब्लॉक diff रणनीति एक अनुरोध में एक फाइल में कई कोड ब्लॉक अपडेट करने की अनुमति देती है।"}}, "matchPrecision": {"label": "मिलान सटीकता", "description": "यह स्लाइडर नियंत्रित करता है कि diffs लागू करते समय कोड अनुभागों को कितनी सटीकता से मेल खाना चाहिए। निम्न मान अधिक लचीले मिलान की अनुमति देते हैं लेकिन गलत प्रतिस्थापन का जोखिम बढ़ाते हैं। 100% से नीचे के मानों का उपयोग अत्यधिक सावधानी के साथ करें।"}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "प्रायोगिक एकीकृत diff रणनीति का उपयोग करें", "description": "प्रायोगिक एकीकृत diff रणनीति सक्षम करें। यह रणनीति मॉडल त्रुटियों के कारण पुनः प्रयासों की संख्या को कम कर सकती है, लेकिन अप्रत्याशित व्यवहार या गलत संपादन का कारण बन सकती है। केवल तभी सक्षम करें जब आप जोखिमों को समझते हों और सभी परिवर्तनों की सावधानीपूर्वक समीक्षा करने के लिए तैयार हों।"}, "SEARCH_AND_REPLACE": {"name": "प्रायोगिक खोज और प्रतिस्थापन उपकरण का उपयोग करें", "description": "प्रायोगिक खोज और प्रतिस्थापन उपकरण सक्षम करें, जो Kilo Code को एक अनुरोध में खोज शब्द के कई उदाहरणों को बदलने की अनुमति देता है।"}, "INSERT_BLOCK": {"name": "प्रायोगिक सामग्री सम्मिलित करने के उपकरण का उपयोग करें", "description": "प्रायोगिक सामग्री सम्मिलित करने के उपकरण को सक्षम करें, जो Kilo Code को diff बनाए बिना विशिष्ट लाइन नंबरों पर सामग्री सम्मिलित करने की अनुमति देता है।"}, "POWER_STEERING": {"name": "प्रायोगिक \"पावर स्टीयरिंग\" मोड का उपयोग करें", "description": "जब सक्षम किया जाता है, तो Kilo Code मॉडल को उसके वर्तमान मोड परिभाषा के विवरण के बारे में अधिक बार याद दिलाएगा। इससे भूमिका परिभाषाओं और कस्टम निर्देशों के प्रति अधिक मजबूत अनुपालन होगा, लेकिन प्रति संदेश अधिक token का उपयोग होगा।"}, "AUTOCOMPLETE": {"name": "प्रयोगात्मक \"ऑटोकंप्लीट\" सुविधा का उपयोग करें", "description": "सक्षम होने पर, Kilo Code आपके टाइप करते ही इनलाइन कोड सुझाव प्रदान करेगा।"}, "MULTI_SEARCH_AND_REPLACE": {"name": "प्रायोगिक मल्टी ब्लॉक diff उपकरण का उपयोग करें", "description": "जब सक्षम किया जाता है, तो Kilo Code मल्टी ब्लॉक diff उपकरण का उपयोग करेगा। यह एक अनुरोध में फ़ाइल में कई कोड ब्लॉक अपडेट करने का प्रयास करेगा।"}, "CONCURRENT_FILE_READS": {"name": "समवर्ती फ़ाइल पढ़ना सक्षम करें", "description": "सक्षम होने पर, Kilo Code एक ही अनुरोध में कई फ़ाइलें पढ़ सकता है अक्षम होने पर, Kilo Code को एक बार में एक फ़ाइल पढ़नी होगी। कम सक्षम मॉडल के साथ काम करते समय या जब आप फ़ाइल एक्सेस पर अधिक नियंत्रण चाहते हैं तो इसे अक्षम करना मददगार हो सकता है।"}, "MARKETPLACE": {"name": "Marketplace सक्षम करें", "description": "जब सक्षम होता है, तो आप Marketplace से MCP और कस्टम मोड इंस्टॉल कर सकते हैं।"}, "MULTI_FILE_APPLY_DIFF": {"name": "समानांतर फ़ाइल संपादन सक्षम करें", "description": "जब सक्षम किया जाता है, तो Kilo Code एक ही अनुरोध में कई फ़ाइलों को संपादित कर सकता है। जब अक्षम किया जाता है, तो Kilo Code को एक समय में एक फ़ाइल संपादित करनी होगी। इसे अक्षम करना तब मदद कर सकता है जब आप कम सक्षम मॉडल के साथ काम कर रहे हों या जब आप फ़ाइल संशोधनों पर अधिक नियंत्रण चाहते हों।"}}, "promptCaching": {"label": "प्रॉम्प्ट कैशिंग अक्षम करें", "description": "जब चेक किया जाता है, तो Kilo Code इस मॉडल के लिए प्रॉम्प्ट कैशिंग का उपयोग नहीं करेगा।"}, "temperature": {"useCustom": "कस्टम तापमान का उपयोग करें", "description": "मॉडल की प्रतिक्रियाओं में यादृच्छिकता को नियंत्रित करता है।", "rangeDescription": "उच्च मान आउटपुट को अधिक यादृच्छिक बनाते हैं, निम्न मान इसे अधिक निर्धारित बनाते हैं।"}, "modelInfo": {"supportsImages": "छवियों का समर्थन करता है", "noImages": "छवियों का समर्थन नहीं करता है", "supportsComputerUse": "कंप्यूटर उपयोग का समर्थन करता है", "noComputerUse": "कंप्यूटर उपयोग का समर्थन नहीं करता है", "supportsPromptCache": "प्रॉम्प्ट कैशिंग का समर्थन करता है", "noPromptCache": "प्रॉम्प्ट कैशिंग का समर्थन नहीं करता है", "maxOutput": "अधिकतम आउटपुट", "inputPrice": "इनपुट मूल्य", "outputPrice": "आउटपुट मूल्य", "cacheReadsPrice": "कैश रीड्स मूल्य", "cacheWritesPrice": "कैश राइट्स मूल्य", "enableStreaming": "स्ट्रीमिंग सक्षम करें", "enableR1Format": "R1 मॉडल पैरामीटर सक्षम करें", "enableR1FormatTips": "QWQ जैसी R1 मॉडलों का उपयोग करते समय इसे सक्षम करना आवश्यक है, ताकि 400 त्रुटि से बचा जा सके", "useAzure": "Azure का उपयोग करें", "azureApiVersion": "Azure API संस्करण सेट करें", "gemini": {"freeRequests": "* प्रति मिनट {{count}} अनुरोधों तक मुफ्त। उसके बाद, बिलिंग प्रॉम्प्ट आकार पर निर्भर करती है।", "pricingDetails": "अधिक जानकारी के लिए, मूल्य निर्धारण विवरण देखें।", "billingEstimate": "* बिलिंग एक अनुमान है - सटीक लागत प्रॉम्प्ट आकार पर निर्भर करती है।"}}, "modelPicker": {"automaticFetch": "एक्सटेंशन <serviceLink>{{serviceName}}</serviceLink> पर उपलब्ध मॉडलों की नवीनतम सूची स्वचालित रूप से प्राप्त करता है। यदि आप अनिश्चित हैं कि कौन सा मॉडल चुनना है, तो Kilo Code <defaultModelLink>{{defaultModelId}}</defaultModelLink> के साथ सबसे अच्छा काम करता है। आप वर्तमान में उपलब्ध निःशुल्क विकल्पों के लिए \"free\" भी खोज सकते हैं।", "label": "मॉडल", "searchPlaceholder": "खोजें", "noMatchFound": "कोई मिलान नहीं मिला", "useCustomModel": "कस्टम उपयोग करें: {{modelId}}"}, "footer": {"feedback": "यदि आपके कोई प्रश्न या प्रतिक्रिया है, तो <githubLink>github.com/Kilo-Org/kilocode</githubLink> पर एक मुद्दा खोलने या <redditLink>reddit.com/r/kilocode</redditLink> या <discordLink>kilocode.ai/discord</discordLink> में शामिल होने में संकोच न करें", "support": "वित्तीय प्रश्नों के लिए, कृपया <supportLink>https://kilocode.ai/support</supportLink> पर ग्राहक सहायता से संपर्क करें", "telemetry": {"label": "त्रुटि और उपयोग रिपोर्टिंग की अनुमति दें", "description": "उपयोग डेटा और त्रुटि रिपोर्ट भेजकर Kilo Code को बेहतर बनाने में मदद करें। कोड, प्रॉम्प्ट, या व्यक्तिगत जानकारी कभी भी नहीं भेजी जाती है। अधिक विवरण के लिए हमारी गोपनीयता नीति देखें।"}, "settings": {"import": "इम्पोर्ट", "export": "एक्सपोर्ट", "reset": "रीसेट करें"}}, "thinkingBudget": {"maxTokens": "अधिकतम tokens", "maxThinkingTokens": "अधिकतम thinking tokens"}, "validation": {"apiKey": "आपको एक मान्य API कुंजी प्रदान करनी होगी।", "awsRegion": "Amazon Bedrock का उपयोग करने के लिए आपको एक क्षेत्र चुनना होगा।", "googleCloud": "आपको एक मान्य Google Cloud प्रोजेक्ट ID और क्षेत्र प्रदान करना होगा।", "modelId": "आपको एक मान्य मॉडल ID प्रदान करनी होगी।", "modelSelector": "आपको एक मान्य मॉडल चयनकर्ता प्रदान करना होगा।", "openAi": "आपको एक मान्य बेस URL, API कुंजी और मॉडल ID प्रदान करनी होगी।", "arn": {"invalidFormat": "अमान्य ARN प्रारूप। कृपया प्रारूप आवश्यकताएं जांचें।", "regionMismatch": "चेतावनी: आपक<PERSON> ARN में क्षेत्र ({{arnRegion}}) आपके चयनित क्षेत्र ({{region}}) से मेल नहीं खाता। इससे पहुंच संबंधी समस्याएं हो सकती हैं। प्रदाता ARN से क्षेत्र का उपयोग करेगा।"}, "modelAvailability": "आपके द्वारा प्रदान की गई मॉडल ID ({{modelId}}) उपलब्ध नहीं है। कृपया कोई अन्य मॉडल चुनें।", "providerNotAllowed": "प्रदाता '{{provider}}' आपके संगठन द्वारा अनुमत नहीं है", "modelNotAllowed": "मॉडल '{{model}}' प्रदाता '{{provider}}' के लिए आपके संगठन द्वारा अनुमत नहीं है", "profileInvalid": "इस प्रोफ़ाइल में एक प्रदाता या मॉडल शामिल है जो आपके संगठन द्वारा अनुमत नहीं है"}, "placeholders": {"apiKey": "API कुंजी दर्ज करें...", "profileName": "प्रोफ़ाइल नाम दर्ज करें", "accessKey": "एक्सेस कुंजी दर्ज करें...", "secretKey": "गुप्त कुंजी दर्ज करें...", "sessionToken": "सत्र टोकन दर्ज करें...", "credentialsJson": "क्रेडेंशियल्स JSON दर्ज करें...", "keyFilePath": "कुंजी फ़ाइल पथ दर्ज करें...", "projectId": "प्रोजेक्ट ID दर्ज करें...", "customArn": "ARN दर्ज करें (उदा. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "बेस URL दर्ज करें...", "modelId": {"lmStudio": "उदा. meta-llama-3.1-8b-instruct", "lmStudioDraft": "उदा. lmstudio-community/llama-3.2-1b-instruct", "ollama": "उदा. llama3.1"}, "numbers": {"maxTokens": "उदा. 4096", "contextWindow": "उदा. 128000", "inputPrice": "उदा. 0.0001", "outputPrice": "उदा. 0.0002", "cacheWritePrice": "उदा. 0.00005"}}, "defaults": {"ollamaUrl": "डिफ़ॉल्ट: http://localhost:11434", "lmStudioUrl": "डिफ़ॉल्ट: http://localhost:1234", "geminiUrl": "डिफ़ॉल्ट: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "कस्टम ARN", "useCustomArn": "कस्टम ARN का उपयोग करें..."}, "display": {"taskTimeline": {"label": "कार्य टाइमलाइन दिखाएँ", "description": "कार्य संदेशों की एक दृश्य समयरेखा प्रदर्शित करें, जो प्रकार के अनुसार रंगीन हैं, जिससे आप कार्य की प्रगति को जल्दी से देख सकें और कार्य के इतिहास में विशिष्ट बिंदुओं पर वापस स्क्रॉल कर सकें।"}}, "includeMaxOutputTokens": "अधिकतम आउटपुट टोकन शामिल करें", "includeMaxOutputTokensDescription": "API अनुरोधों में अधिकतम आउटपुट टोकन पैरामीटर भेजें। कुछ प्रदाता इसका समर्थन नहीं कर सकते हैं।"}