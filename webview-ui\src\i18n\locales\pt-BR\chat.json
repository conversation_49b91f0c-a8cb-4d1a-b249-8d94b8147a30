{"greeting": "O que o Kilo Code pode fazer por você?", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Custo da API:", "contextWindow": "<PERSON><PERSON>:", "closeAndStart": "Fechar tarefa e iniciar nova", "export": "Exportar histó<PERSON><PERSON> de <PERSON>fas", "delete": "Excluir tarefa (Shift + Clique para pular confirmação)", "condenseContext": "Condensar contexto de forma inteligente", "share": "Compart<PERSON><PERSON> tarefa", "shareWithOrganization": "Compartilhar com organização", "shareWithOrganizationDescription": "Apenas membros da sua organização podem acessar", "sharePublicly": "Compartilhar publicamente", "sharePubliclyDescription": "Qualquer pessoa com o link pode acessar", "connectToCloud": "Conectar ao Cloud", "connectToCloudDescription": "Entre no Kilo Code Cloud para compartilhar tarefas", "sharingDisabledByOrganization": "Compartilhamento desabilitado pela organização", "shareSuccessOrganization": "Link da organização copiado para a área de transferência", "shareSuccessPublic": "Link público copiado para a área de transferência"}, "history": {"title": "Hist<PERSON><PERSON><PERSON>"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espaço disponível: {{amount}} tokens", "tokensUsed": "Tokens usados: {{used}} de {{total}}", "reservedForResponse": "Reservado para resposta do modelo: {{amount}} tokens"}, "retry": {"title": "Tentar novamente", "tooltip": "Tentar a operação novamente"}, "startNewTask": {"title": "Iniciar nova tarefa", "tooltip": "Começar uma nova tarefa"}, "reportBug": {"title": "<PERSON><PERSON>"}, "proceedAnyways": {"title": "Prosseguir mesmo assim", "tooltip": "Continuar enquanto o comando executa"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salvar as alterações do arquivo"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rejeitar esta ação"}, "completeSubtaskAndReturn": "Completar subtarefa e retornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprovar esta ação"}, "runCommand": {"title": "Executar comando", "tooltip": "Executar este comando"}, "proceedWhileRunning": {"title": "Prosseguir durante execução", "tooltip": "Continuar apesar dos avisos"}, "killCommand": {"title": "Interromper Comando", "tooltip": "Interromper o comando atual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON> ta<PERSON>", "tooltip": "Continuar a tarefa atual"}, "terminate": {"title": "Terminar", "tooltip": "Encerrar a tarefa atual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar a operação atual"}, "scrollToBottom": "Rolar para o final do chat", "about": "<PERSON><PERSON>, refatore e depure código com assistência de IA. Confira nossa <DocsLink>documentação</DocsLink> para saber mais.", "onboarding": "<strong>Sua lista de tarefas neste espaço de trabalho está vazia.</strong> Comece digitando uma tarefa abaixo. Não sabe como começar? Leia mais sobre o que o Kilo Code pode fazer por você nos <DocsLink>documentos</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Orquestração de Tarefas", "description": "<PERSON><PERSON><PERSON> as tarefas em partes menores e gerenciáveis."}, "stickyModels": {"title": "Modos Fixos", "description": "Cada modo lembra o seu último modelo usado"}, "tools": {"title": "Ferramentas", "description": "Permita que a IA resolva problemas navegando na web, executando comandos e muito mais."}, "customizableModes": {"title": "Modos personalizáveis", "description": "Personas especializadas com comportamentos próprios e modelos atribuídos"}}, "selectMode": "Selecionar modo de interação", "selectApiConfig": "Selecionar configuração da API", "selectModelConfig": "Selecionar modelo", "enhancePrompt": "Aprimorar prompt com contexto adicional", "addImages": "Adicionar imagens à mensagem", "sendMessage": "Enviar mensagem", "stopTts": "Parar conversão de texto em fala", "typeMessage": "Digite uma mensagem...", "typeTask": "<PERSON><PERSON><PERSON><PERSON>, encontrar, perguntar algo", "addContext": "@ para adicionar contexto, / para alternar modos", "dragFiles": "segure shift para arrastar arquivos", "dragFilesImages": "segure shift para arrastar arquivos/imagens", "enhancePromptDescription": "O botão 'Aprimorar prompt' ajuda a melhorar seu pedido fornecendo contexto adicional, esclarecimentos ou reformulações. Tente digitar um pedido aqui e clique no botão novamente para ver como funciona.", "modeSelector": {"title": "Modos", "marketplace": "Marketplace de Modos", "settings": "Configurações de Modos", "description": "Personas especializadas que adaptam o comportamento do Kilo Code."}, "errorReadingFile": "Erro ao ler arquivo:", "noValidImages": "Nenhuma imagem válida foi processada", "separator": "Separador", "edit": "Editar...", "forNextMode": "para o próximo modo", "error": "Erro", "diffError": {"title": "Edição mal-sucedida"}, "troubleMessage": "Kilo Code está tendo problemas...", "apiRequest": {"title": "Requisição API", "failed": "Requisição API falhou", "streaming": "Requisição API...", "cancelled": "Requisição API cancelada", "streamingFailed": "Streaming API falhou"}, "checkpoint": {"initial": "Ponto de verificação inicial", "regular": "Ponto de verificação", "initializingWarning": "Ainda inicializando ponto de verificação... Se isso demorar muito, você pode desativar os pontos de verificação nas <settingsLink>configurações</settingsLink> e reiniciar sua tarefa.", "menu": {"viewDiff": "Ver diferenças", "restore": "Restaurar ponto de verificação", "restoreFiles": "Restaurar a<PERSON>", "restoreFilesDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto.", "restoreFilesAndTask": "Restaurar arquivos e tarefa", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta ação não pode ser desfeita.", "restoreFilesAndTaskDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto e exclui todas as mensagens após este ponto."}, "current": "Atual"}, "instructions": {"wantsToFetch": "Kilo Code quer buscar instruções detalhadas para ajudar com a tarefa atual"}, "fileOperations": {"wantsToRead": "Kilo Code quer ler este arquivo:", "wantsToReadOutsideWorkspace": "Kilo Code quer ler este arquivo fora do espaço de trabalho:", "didRead": "Kilo Code leu este arquivo:", "wantsToEdit": "Kilo Code quer editar este arquivo:", "wantsToEditOutsideWorkspace": "Kilo Code quer editar este arquivo fora do espaço de trabalho:", "wantsToEditProtected": "Kilo Code quer editar um arquivo de configuração protegido:", "wantsToCreate": "Kilo Code quer criar um novo arquivo:", "wantsToSearchReplace": "Kilo Code quer realizar busca e substituição neste arquivo:", "didSearchReplace": "Kilo Code realizou busca e substituição neste arquivo:", "wantsToInsert": "Kilo Code quer inserir conteúdo neste arquivo:", "wantsToInsertWithLineNumber": "Kilo Code quer inserir conteúdo neste arquivo na linha {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code quer adicionar conteúdo ao final deste arquivo:", "wantsToReadAndXMore": "Kilo Code quer ler este arquivo e mais {{count}}:", "wantsToReadMultiple": "Kilo Code deseja ler múltiplos arquivos:", "wantsToApplyBatchChanges": "Kilo Code quer aplicar alterações a múltiplos arquivos:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code quer visualizar os arquivos de nível superior neste diretório:", "didViewTopLevel": "Kilo Code visualizou os arquivos de nível superior neste diretório:", "wantsToViewRecursive": "Kilo Code quer visualizar recursivamente todos os arquivos neste diretório:", "didViewRecursive": "Kilo Code visualizou recursivamente todos os arquivos neste diretório:", "wantsToViewDefinitions": "Kilo Code quer visualizar nomes de definição de código-fonte usados neste diretório:", "didViewDefinitions": "Kilo Code visualizou nomes de definição de código-fonte usados neste diretório:", "wantsToSearch": "Kilo Code quer pesquisar neste diretório por <code>{{regex}}</code>:", "didSearch": "Kilo Code pesquisou neste diretório por <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code quer pesquisar neste diretório (fora do espaço de trabalho) por <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code pesquisou neste diretório (fora do espaço de trabalho) por <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code quer visualizar os arquivos de nível superior neste diretório (fora do espaço de trabalho):", "didViewTopLevelOutsideWorkspace": "Kilo Code visualizou os arquivos de nível superior neste diretório (fora do espaço de trabalho):", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code quer visualizar recursivamente todos os arquivos neste diretório (fora do espaço de trabalho):", "didViewRecursiveOutsideWorkspace": "Kilo Code visualizou recursivamente todos os arquivos neste diretório (fora do espaço de trabalho):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code quer visualizar nomes de definição de código-fonte usados neste diretório (fora do espaço de trabalho):", "didViewDefinitionsOutsideWorkspace": "Kilo Code visualizou nomes de definição de código-fonte usados neste diretório (fora do espaço de trabalho):"}, "commandOutput": "Saída do comando", "response": "Resposta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "Kilo Code quer usar uma ferramenta no servidor MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code quer acessar um recurso no servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code quer mudar para o modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "Kilo Code quer mudar para o modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "Kilo Code mudou para o modo <code>{{mode}}</code>", "didSwitchWithReason": "Kilo Code mudou para o modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code quer criar uma nova subtarefa no modo <code>{{mode}}</code>:", "wantsToFinish": "Kilo Code quer finalizar esta subtarefa", "newTaskContent": "Instruções da subtarefa", "completionContent": "Subtarefa concluída", "resultContent": "Resultados da subtarefa", "defaultResult": "Por favor, continue com a próxima tarefa.", "completionInstructions": "Subtarefa concluída! Você pode revisar os resultados e sugerir correções ou próximos passos. Se tudo parecer bom, confirme para retornar o resultado à tarefa principal."}, "questions": {"hasQuestion": "Kilo Code tem uma pergunta:"}, "taskCompleted": "Tarefa concluída", "powershell": {"issues": "Parece que você está tendo problemas com o Windows PowerShell, por favor veja este"}, "autoApprove": {"title": "Aprovação automática:", "none": "<PERSON><PERSON><PERSON><PERSON>", "description": "A aprovação automática permite que o Kilo Code execute ações sem pedir permissão. Ative apenas para ações nas quais você confia totalmente. Configuração mais detalhada disponível nas <settingsLink>Configurações</settingsLink>."}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "errorHeader": "Falha ao condensar contexto", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar para entrada (ou Shift + clique)", "autoSelectCountdown": "Seleção automática em {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Roo Code {{version}} Lançado", "description": "Roo Code {{version}} traz novos recursos poderosos e melhorias significativas para aprimorar seu fluxo de trabalho de desenvolvimento.", "whatsNew": "O que há de novo", "feature1": "<bold>Indexação de Base de Código Graduada do Experimental</bold>: A indexação completa da base de código agora é estável e pronta para uso em produção com busca aprimorada e compreensão de contexto.", "feature2": "<bold>Nova Funcionalidade de Lista de Tarefas</bold>: Mantenha suas tarefas no caminho certo com gerenciamento integrado de tarefas que ajuda você a se manter organizado e focado em seus objetivos de desenvolvimento.", "feature3": "<bold>Transições Aprimoradas de Arquiteto para Código</bold>: Transferências suaves do planejamento no modo Arquiteto para implementação no modo Código.", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Obtenha mais detalhes e participe da discussão no <discordLink>Discord</discordLink> e <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Kilo Code quer usar o navegador:", "consoleLogs": "Logs do console", "noNewLogs": "(Sem novos logs)", "screenshot": "Captura de tela do navegador", "cursor": "cursor", "navigation": {"step": "Passo {{current}} de {{total}}", "previous": "Anterior", "next": "Próximo"}, "sessionStarted": "Sessão do navegador iniciada", "actions": {"title": "Ação do navegador: ", "launch": "Iniciar nave<PERSON> em {{url}}", "click": "Clique ({{coordinate}})", "type": "Digitar \"{{text}}\"", "scrollDown": "Rolar para baixo", "scrollUp": "Rolar para cima", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloco de código", "collapse": "Re<PERSON>lher bloco de código", "enable_wrap": "Ativar quebra de linha", "disable_wrap": "<PERSON>ati<PERSON> que<PERSON> de linha", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "systemPromptWarning": "AVISO: Substituição personalizada de instrução do sistema ativa. Isso pode comprometer gravemente a funcionalidade e causar comportamento imprevisível.", "profileViolationWarning": "O perfil atual viola as configurações da sua organização", "shellIntegration": {"title": "Aviso de execução de comando", "description": "Seu comando está sendo executado sem a integração de shell do terminal VSCode. Para suprimir este aviso, você pode desativar a integração de shell na seção <strong>Terminal</strong> das <settingsLink>configurações do Kilo Code</settingsLink> ou solucionar problemas de integração do terminal VSCode usando o link abaixo.", "troubleshooting": "Clique aqui para a documentação de integração de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite de Solicitações Auto-aprovadas Atingido", "description": "Kilo Code atingiu o limite auto-aprovado de {{count}} solicitação(ões) de API. Deseja redefinir a contagem e prosseguir com a tarefa?", "button": "Redefinir e Continuar"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code quer pesquisar na base de código por <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code quer pesquisar na base de código por <code>{{query}}</code> em <code>{{path}}</code>:", "didSearch": "Encontrado {{count}} resultado(s) para <code>{{query}}</code>:", "resultTooltip": "Pontuação de similaridade: {{score}} (clique para abrir o arquivo)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON>r tudo"}, "deny": {"title": "<PERSON><PERSON><PERSON> tudo"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> pronto", "indexing": "Indexando {{percentage}}%", "indexed": "Indexado", "error": "<PERSON>rro do <PERSON>", "status": "Status do índice"}, "versionIndicator": {"ariaLabel": "V<PERSON><PERSON> {{version}} - Clique para ver as notas de lançamento"}}