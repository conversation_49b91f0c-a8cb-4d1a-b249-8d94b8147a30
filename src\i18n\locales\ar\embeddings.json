{"unknownError": "خطأ غير معروف", "authenticationFailed": "فشل إنشاء التضمين (embedding): فشل التحقق من الهوية. تأكد من مفتاح الـ API.", "failedWithStatus": "فشل إنشاء التضمين بعد {{attempts}} محاولة: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "فشل إنشاء التضمين بعد {{attempts}} محاولة: {{errorMessage}}", "failedMaxAttempts": "فشل إنشاء التضمين بعد {{attempts}} محاولة", "textExceedsTokenLimit": "النص عند الفهرس {{index}} تجاوز الحد الأعلى للتوكينات ({{itemTokens}} > {{maxTokens}}). تم تجاهله.", "rateLimitRetry": "تجاوزنا حد الاستخدام، بنعيد المحاولة بعد {{delayMs}} مللي ثانية (محاولة رقم {{attempt}} من {{maxRetries}})", "ollama": {"couldNotReadErrorBody": "ما قدرنا نقرأ محتوى الخطأ", "requestFailed": "طلب Ollama API فشل بالحالة {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "الاستجابة من Ollama API غير صالحة: المصفوفة 'embeddings' مفقودة أو مو مصفوفة.", "embeddingFailed": "فشل إنشاء التضمين في Ollama: {{message}}", "serviceNotRunning": "خدمة Ollama غير شغّالة على {{baseUrl}}", "serviceUnavailable": "خدمة Ollama غير متوفّرة (الحالة: {{status}})", "modelNotFound": "نموذج Ollama غير موجود: {{modelId}}", "modelNotEmbeddingCapable": "نموذج Ollama لا يدعم التضمين: {{modelId}}", "hostNotFound": "مضي<PERSON>ي<PERSON> موجود: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "خطأ غير معروف أثناء معالجة الملف {{filePath}}", "unknownErrorDeletingPoints": "خطأ غير معروف أثناء حذف النقاط للملف {{filePath}}", "failedToProcessBatchWithError": "فشل في معالجة الدفعة بعد {{maxRetries}} محاولة: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "فشل الاتصال بقاعدة بيانات المتجهات Qdrant. تأكد أن Qdrant شغّال ومتوفّر على {{qdrantUrl}}. الخطأ: {{errorMessage}}"}, "validation": {"authenticationFailed": "فشل التحقق من الهوية. يرجى التحقق من مفتاح الـ API في الإعدادات.", "connectionFailed": "فشل الاتصال بخدمة التضمين. يرجى التحقق من إعدادات الاتصال والتأكد من تشغيل الخدمة.", "modelNotAvailable": "النموذج المحدد غير متوفّر. يرجى التحقق من إعدادات النموذج.", "configurationError": "إعدادات التضمين غير صالحة. يرجى مراجعة الإعدادات.", "serviceUnavailable": "خدمة التضمين غير متوفّرة. يرجى التأكد من تشغيلها وإمكانية الوصول إليها.", "invalidEndpoint": "نقطة نهاية API غير صالحة. يرجى التحقق من إعدادات الرابط.", "invalidEmbedderConfig": "إعدادات التضمين غير صالحة. يرجى التحقق من الإعدادات.", "invalidApiKey": "مفتاح API غير صالح. يرجى التحقق من إعدادات مفتاح الـ API.", "invalidBaseUrl": "الرابط الأساسي غير صالح. يرجى التحقق من إعدادات الرابط.", "invalidModel": "النموذج غير صالح. يرجى التحقق من إعدادات النموذج.", "invalidResponse": "استجابة غير صالحة من خدمة التضمين. يرجى التحقق من الإعدادات."}, "serviceFactory": {"openAiConfigMissing": "إعدادات OpenAI مفقودة لإنشاء التضمين", "ollamaConfigMissing": "إعدادات Ollama مفقودة لإنشاء التضمين", "openAiCompatibleConfigMissing": "إعدادات OpenAI المتوافق مفقودة لإنشاء التضمين", "geminiConfigMissing": "إعدادات Gemini مفقودة لإنشاء التضمين", "invalidEmbedderType": "نوع التضمين المُعدّ غير صالح: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "لا يمكن تحديد أبعاد المتجه للنموذج '{{modelId}}' مع المزوّد '{{provider}}'. يرجى التأكد من ضبط 'أبعاد التضمين' بشكل صحيح في إعدادات مزوّد OpenAI المتوافق.", "vectorDimensionNotDetermined": "لا يمكن تحديد أبعاد المتجه للنموذج '{{modelId}}' مع المزوّد '{{provider}}'. تحقق من ملفات النموذج أو الإعدادات.", "qdrantUrlMissing": "<PERSON>ا<PERSON><PERSON> Qdrant مفقود لإنشاء مخزن المتجهات", "codeIndexingNotConfigured": "لا يمكن إنشاء الخدمات: فهرسة الكود غير مُعدّة بشكل صحيح"}}