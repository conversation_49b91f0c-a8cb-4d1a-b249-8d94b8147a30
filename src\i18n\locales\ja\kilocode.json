{"info": {"settings_imported": "設定のインポートに成功しました。"}, "userFeedback": {"message_update_failed": "メッセージの更新に失敗しました", "no_checkpoint_found": "このメッセージの前にチェックポイントが見つかりませんでした", "message_updated": "メッセージが正常に更新されました"}, "lowCreditWarning": {"title": "クレジット残高警告！", "message": "無料クレジットでチャージするか、追加購入できるか確認してください！"}, "notLoggedInError": "リクエストを完了できません。選択したプロバイダーに接続してログインしていることを確認してください。\n\n{{error}}", "rules": {"actions": {"delete": "削除", "confirmDelete": "{{filename}}を削除してもよろしいですか？", "deleted": "{{filename}}を削除しました"}, "errors": {"noWorkspaceFound": "ワークスペースフォルダが見つかりません", "fileAlreadyExists": "ファイル{{filename}}は既に存在します", "failedToCreateRuleFile": "ルールファイルの作成に失敗しました。", "failedToDeleteRuleFile": "ルールファイルの削除に失敗しました。"}, "templates": {"workflow": {"description": "ワークフローの説明をここに...", "stepsHeader": "## ステップ", "step1": "ステップ1", "step2": "ステップ2"}, "rule": {"description": "ルールの説明をここに...", "guidelinesHeader": "## ガイドライン", "guideline1": "ガイドライン1", "guideline2": "ガイドライン2"}}}, "commitMessage": {"activated": "Kilo Codeコミットメッセージ生成器が起動しました", "gitNotFound": "⚠️ Gitリポジトリが見つからないか、gitが利用できません", "gitInitError": "⚠️ Gitの初期化エラー: {{error}}", "generating": "Kilo: コミットメッセージを生成しています...", "noChanges": "Kilo: 分析すべき変更は見つかりませんでした", "generated": "Kilo: コミットメッセージが生成されました！", "generationFailed": "Kilo: コミットメッセージの生成に失敗しました: {{errorMessage}}", "generatingFromUnstaged": "Kilo: ステージングされていない変更を使用してメッセージを生成中", "activationFailed": "Kilo: メッセージジェネレーターの起動に失敗しました: {{error}}", "providerRegistered": "Kilo: コミットメッセージプロバイダーが登録されました"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo 自動補完", "disabled": "$(circle-slash) Kilo 自動補完", "warning": "$(warning) Kilo 自動補完", "tooltip": {"disabled": "Kilo Code オートコンプリート (無効)", "tokenError": "オートコンプリートを使用するには有効なトークンを設定する必要があります", "lastCompletion": "最後の回答:", "basic": "Kilo Code オートコンプリート", "model": "モデル:", "sessionTotal": "セッション合計費用:"}, "cost": {"zero": "¥0", "lessThanCent": "<$0.01"}}, "toggleMessage": "Kilo 自動補完 {{status}}"}, "ghost": {"progress": {"analyzing": "コードを分析中...", "generating": "編集候補を生成中...", "processing": "編集の提案を処理中...", "showing": "提案された編集を表示中...", "title": "Kilo Code"}, "input": {"title": "Kilo Code：クイックタスク", "placeholder": "例：「この関数をより効率的になるようにリファクタリングする」"}, "commands": {"displaySuggestions": "提案された編集を表示", "generateSuggestions": "Kilo Code：推奨編集の生成", "applyCurrentSuggestion": "現在の提案された編集を適用する", "cancelSuggestions": "提案された編集をキャンセル", "category": "Kilo Code", "applyAllSuggestions": "提案された編集をすべて適用する", "promptCodeSuggestion": "プロンプト クイックタスク"}, "chatParticipant": {"fullName": "Kilo Code エージェント", "description": "素早いタスクや提案された編集についてお手伝いできます。", "name": "エージェント"}, "codeAction": {"title": "Kilo Code: 提案された編集"}, "messages": {"provideCodeSuggestions": "コードを提案中..."}}}