{"greeting": "<PERSON>lo Code sizin için ne yapa<PERSON>ir?", "task": {"title": "<PERSON><PERSON><PERSON><PERSON>", "seeMore": "<PERSON><PERSON> fazla gör", "seeLess": "<PERSON><PERSON> az gör", "tokens": "Tokenlar:", "cache": "Önbellek:", "apiCost": "API Maliyeti:", "contextWindow": "Bağlam Uzunluğu:", "closeAndStart": "<PERSON><PERSON><PERSON><PERSON> kapat ve yeni bir görev ba<PERSON><PERSON>", "export": "Görev geçmişini dışa aktar", "delete": "G<PERSON><PERSON>vi sil (Onayı atlamak için Shift + Tıkla)", "condenseContext": "Bağlamı akıllıca yoğunlaştır", "share": "<PERSON><PERSON><PERSON><PERSON>", "shareWithOrganization": "Kuruluşla <PERSON>", "shareWithOrganizationDescription": "Sadece k<PERSON>şunuzun üyeleri erişebilir", "sharePublicly": "Herkese açık <PERSON>ş", "sharePubliclyDescription": "Bağlantıya sahip herkes erişebilir", "connectToCloud": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "connectToCloudDescription": "Görevleri paylaşmak için Roo Code Cloud'a giriş yap", "sharingDisabledByOrganization": "Paylaşım kuruluş tarafından devre dışı bırakıldı", "shareSuccessOrganization": "Organizasyon bağlantısı panoya kopyalandı", "shareSuccessPublic": "Genel bağlantı panoya kopyalandı"}, "history": {"title": "Geçmiş"}, "unpin": "Sabitlemeyi iptal et", "pin": "<PERSON><PERSON><PERSON>", "tokenProgress": {"availableSpace": "Kullanılabil<PERSON> alan: {{amount}} token", "tokensUsed": "Kullanılan tokenlar: {{used}} / {{total}}", "reservedForResponse": "Model yanıtı için a<PERSON>ı<PERSON>: {{amount}} token"}, "retry": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "İşlemi tekrar dene"}, "startNewTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> bir g<PERSON><PERSON>v ba<PERSON><PERSON>"}, "reportBug": {"title": "<PERSON><PERSON>"}, "proceedAnyways": {"title": "<PERSON><PERSON>", "tooltip": "Komut çalışırken devam et"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON>lerini kaydet"}, "reject": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "completeSubtaskAndReturn": "<PERSON> görevi tama<PERSON>la ve geri dön", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON> e<PERSON><PERSON> on<PERSON>la"}, "runCommand": {"title": "Komutu Çalıştır", "tooltip": "Bu komutu ç<PERSON>ıştır"}, "proceedWhileRunning": {"title": "Çalışırken Devam Et", "tooltip": "Uyarılara rağmen devam et"}, "killCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Mevcut komutu durdur"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Mevcut göreve devam et"}, "terminate": {"title": "Sonlandır", "tooltip": "Mevcut görevi <PERSON>"}, "cancel": {"title": "İptal", "tooltip": "Mevcut işlemi iptal et"}, "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> altına kaydır", "about": "AI yardımıyla kod o<PERSON>, yeniden düzenleyin ve hatalarını ayıklayın. Daha fazla bilgi edinmek için <DocsLink>belgelerimize</DocsLink> göz atın.", "onboarding": "<strong><PERSON>u çalışma alanındaki görev listeniz boş.</strong> Aşağıya bir görev yazarak başlayın. Nasıl başlayacağınızdan emin değil misiniz? Kilo Code'nun sizin için neler yapabileceği hakkında daha fazla bilgiyi <DocsLink>belgelerde</DocsLink> okuyun.", "rooTips": {"boomerangTasks": {"title": "Görev Orkestrasyonu", "description": "Görevleri da<PERSON>, yönetilebilir par<PERSON>a a<PERSON>ırın."}, "stickyModels": {"title": "Yapışkan Modlar", "description": "Her mod, en son kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modeli hatırlar"}, "tools": {"title": "Araçlar", "description": "AI'nın web'e göz atarak, komutlar çalıştırarak ve daha fazlasını yaparak sorunları çözmesine izin verin."}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ları ve atanmış modelleri ile özelleştirilmiş kişilikler"}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modunu se<PERSON>", "selectApiConfig": "API yapılandırmasını seçin", "selectModelConfig": "<PERSON> <PERSON><PERSON><PERSON>", "enhancePrompt": "Ek bağ<PERSON>la istemi gel<PERSON>ştir", "addImages": "<PERSON>ja resim ekle", "sendMessage": "<PERSON><PERSON>", "stopTts": "<PERSON><PERSON> du<PERSON>", "typeMessage": "Bir mesaj yazın...", "typeTask": "<PERSON><PERSON><PERSON><PERSON>, bul, bir <PERSON>ey sor", "addContext": "Bağlam eklemek için @, mod değiştirmek için /", "dragFiles": "dosyaları sürüklemek için shift tuşuna basılı tutun", "dragFilesImages": "dosyaları/resimleri sürüklemek için shift tuşuna basılı tutun", "enhancePromptDescription": "'İstemi geli<PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>, ek b<PERSON><PERSON><PERSON>, açıklama veya yeniden ifade sağlayarak isteğinizi iyileştirmeye yardımcı olur. Buraya bir istek yazıp düğmeye tekrar tıklayarak nasıl çalıştığını görebilirsiniz.", "modeSelector": {"title": "<PERSON><PERSON><PERSON>", "marketplace": "<PERSON><PERSON>", "settings": "<PERSON><PERSON>", "description": "Roo'nun davranışını özelleştiren uzmanlaşmış kişilikler."}, "errorReadingFile": "<PERSON><PERSON>a okuma hatası:", "noValidImages": "Hiçbir geçerli resim işlenmedi", "separator": "Ayırıcı", "edit": "Düzenle...", "forNextMode": "<PERSON><PERSON><PERSON> mod i<PERSON>in", "error": "<PERSON><PERSON>", "diffError": {"title": "Düzenleme Başarısız"}, "troubleMessage": "Kilo Code sorun yaşıyor...", "apiRequest": {"title": "API İsteği", "failed": "API İsteği Başarısız", "streaming": "API İsteği...", "cancelled": "API İsteği İptal Edildi", "streamingFailed": "API Akışı Başarısız"}, "checkpoint": {"initial": "İlk Kontrol Noktası", "regular": "<PERSON><PERSON><PERSON>", "initializingWarning": "Kontrol noktası hala başlatılıyor... Bu çok uzun sürerse, <settingsLink>ayarlar</settingsLink> bölümünden kontrol noktalarını devre dışı bırakabilir ve görevinizi yeniden başlatabilirsiniz.", "menu": {"viewDiff": "Farkları Görüntüle", "restore": "Kontrol Noktasını Geri <PERSON>", "restoreFiles": "Dosyaları Geri <PERSON>", "restoreFilesDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler.", "restoreFilesAndTask": "Dosyaları ve Görevi Geri <PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "cannotUndo": "Bu işlem geri alınamaz.", "restoreFilesAndTaskDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler ve bu noktadan sonraki tüm mesajları siler."}, "current": "Mevcut"}, "instructions": {"wantsToFetch": "Kilo Code mevcut göreve yardımcı olmak için ayrıntılı talimatlar almak istiyor"}, "fileOperations": {"wantsToRead": "Kilo Code bu dosyayı okumak istiyor:", "wantsToReadOutsideWorkspace": "Kilo Code çalışma alanı dışındaki bu dosyayı okumak istiyor:", "didRead": "Kilo Code bu dosyayı okudu:", "wantsToEdit": "Kilo Code bu dosyayı düzenlemek istiyor:", "wantsToEditOutsideWorkspace": "Kilo Code çalışma alanı dışındaki bu dosyayı düzenlemek istiyor:", "wantsToEditProtected": "Kilo Code korumalı bir yapılandırma dosyasını düzenlemek istiyor:", "wantsToCreate": "Kilo Code yeni bir dosya oluşturmak istiyor:", "wantsToSearchReplace": "Kilo Code bu dosyada arama ve değiştirme yapmak istiyor:", "didSearchReplace": "Kilo Code bu dosyada arama ve değiştirme yaptı:", "wantsToInsert": "Kilo Code bu dosyaya içerik eklemek istiyor:", "wantsToInsertWithLineNumber": "Kilo Code bu dosyanın {{lineNumber}}. satırına içerik eklemek istiyor:", "wantsToInsertAtEnd": "Kilo Code bu dosyanın sonuna içerik eklemek istiyor:", "wantsToReadAndXMore": "Kilo Code bu dosyayı ve {{count}} tane daha okumak istiyor:", "wantsToReadMultiple": "Kilo Code birden fazla dosya okumak istiyor:", "wantsToApplyBatchChanges": "Kilo Code birden fazla dosyaya değişiklik uygulamak istiyor:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code bu dizindeki üst düzey dosyaları görüntülemek istiyor:", "didViewTopLevel": "Kilo Code bu dizindeki üst düzey dosyaları görüntüledi:", "wantsToViewRecursive": "Kilo Code bu dizindeki tüm dosyaları özyinelemeli olarak görüntülemek istiyor:", "didViewRecursive": "Kilo Code bu dizindeki tüm dosyaları özyinelemeli olarak görüntüledi:", "wantsToViewDefinitions": "Kilo Code bu dizinde kullanılan kaynak kod tanımlama isimlerini görüntülemek istiyor:", "didViewDefinitions": "Kilo Code bu dizinde kullanılan kaynak kod tanımlama isimlerini görüntüledi:", "wantsToSearch": "Kilo Code bu dizinde <code>{{regex}}</code> i<PERSON><PERSON> arama yapmak istiyor:", "didSearch": "Kilo Code bu dizinde <code>{{regex}}</code> i<PERSON>in arama yaptı:", "wantsToSearchOutsideWorkspace": "Kilo Code bu dizinde (çalışma alanı dışında) <code>{{regex}}</code> i<PERSON>in arama yapmak istiyor:", "didSearchOutsideWorkspace": "Kilo Code bu dizinde (çalışma alanı dışında) <code>{{regex}}</code> için arama yaptı:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code bu dizindeki (çalışma alanı dışında) üst düzey dosyaları görüntülemek istiyor:", "didViewTopLevelOutsideWorkspace": "Kilo Code bu dizindeki (çalışma alanı dışında) üst düzey dosyaları görüntüledi:", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code bu dizindeki (çalışma alanı dışında) tüm dosyaları özyinelemeli olarak görüntülemek istiyor:", "didViewRecursiveOutsideWorkspace": "Kilo Code bu dizindeki (çalışma alanı dışında) tüm dosyaları özyinelemeli olarak görüntüledi:", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code bu dizinde (çalışma alanı dışında) kullanılan kaynak kod tanımlama isimlerini görüntülemek istiyor:", "didViewDefinitionsOutsideWorkspace": "Kilo Code bu dizinde (çalışma alanı dışında) kullanılan kaynak kod tanımlama isimlerini görüntüledi:"}, "commandOutput": "Komut Çıktısı", "response": "Yan<PERSON>t", "arguments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcp": {"wantsToUseTool": "Kilo Code {{serverName}} MCP sunucusunda bir araç kullanmak istiyor:", "wantsToAccessResource": "Kilo Code {{serverName}} MCP sunucusundaki bir kaynağa erişmek istiyor:"}, "modes": {"wantsToSwitch": "Kilo Code <code>{{mode}}</code> moduna geçmek istiyor", "wantsToSwitchWithReason": "Kilo Code <code>{{mode}}</code> moduna geçmek istiyor <PERSON>: {{reason}}", "didSwitch": "Kilo Code <code>{{mode}}</code> moduna geçti", "didSwitchWithReason": "Kilo Code <code>{{mode}}</code> moduna geç<PERSON>: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code <code>{{mode}}</code> modunda yeni bir alt görev oluşturmak istiyor:", "wantsToFinish": "Kilo Code bu alt görevi bitirmek istiyor", "newTaskContent": "Alt Görev Talimatları", "completionContent": "Alt Görev Tamamlandı", "resultContent": "Alt Görev Sonuçları", "defaultResult": "Lütfen sonraki göreve devam edin.", "completionInstructions": "Alt görev tamamlandı! Sonuçları inceleyebilir ve düzeltmeler veya sonraki adımlar önerebilirsiniz. Her şey iyi görünü<PERSON><PERSON>, sonucu üst göreve döndürmek için on<PERSON>ın."}, "questions": {"hasQuestion": "<PERSON>lo Code'nun bir sorusu var:"}, "taskCompleted": "Görev Tamamlandı", "powershell": {"issues": "Windows PowerShell ile ilgili sorunlar yaşıyor gibi görünüyorsunuz, lütfen şu konuya bakın"}, "autoApprove": {"title": "Otomatik-onay:", "none": "Hiç<PERSON>i", "description": "Otomatik onay, Kilo Code'un izin istemeden işlemler gerçekleştirmesine olanak tanır. Yalnızca tamamen güvendiğiniz eylemler için etkinleştirin. Daha detaylı ya<PERSON>ılandırma <settingsLink>Ayarlar</settingsLink>'da mevcuttur."}, "reasoning": {"thinking": "Düşünüyor", "seconds": "{{count}}sn"}, "contextCondense": {"title": "Bağlam Özetlendi", "condensing": "Bağlam yoğunlaştırılıyor...", "errorHeader": "Bağlam yoğunlaştırılamadı", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON> k<PERSON> (veya Shift + tıklama)", "autoSelectCountdown": "{{count}}s iç<PERSON>e otomatik seçilecek", "countdownDisplay": "{{count}}sn"}, "announcement": {"title": "🎉 Roo Code {{version}} Yayınlandı", "description": "Roo Code {{version}}, geliştirme iş akışınızı geliştirmek için güçlü yeni özellikler ve önemli iyileştirmeler getiriyor.", "whatsNew": "<PERSON><PERSON><PERSON><PERSON>", "feature1": "<bold>Kod Tabanı İndeksleme Deneysel Aşamadan Mezun Oldu</bold>: <PERSON> kod tabanı indeksleme artık kararlı ve geliştirilmiş arama ve bağlam anlayışı ile üretim kullanımına hazır.", "feature2": "<bold><PERSON>ni <PERSON>klar Listesi Özelliği</bold>: Görevlerinizi yolunda tutmak için entegre yapılacaklar yönetimi ile organize kalın ve geliştirme hedeflerinize odaklanın.", "feature3": "<bold><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>çişler</bold>: <PERSON><PERSON> modunda planlamadan Kod modunda uygulamaya sorunsuz aktarımlar.", "hideButton": "<PERSON><PERSON><PERSON><PERSON> gizle", "detailsDiscussLinks": "<discordLink>Discord</discordLink> ve <redditLink>Reddit</redditLink>'te daha fazla ayrıntı alın ve tartışmalara katılın 🚀"}, "browser": {"rooWantsToUse": "Kilo Code tarayıcıyı kullanmak istiyor:", "consoleLogs": "Konsol Kayıtları", "noNewLogs": "(<PERSON><PERSON> kayıt yok)", "screenshot": "Tarayıcı ekran görüntüsü", "cursor": "imleç", "navigation": {"step": "Adım {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>"}, "sessionStarted": "Tarayıcı Oturumu Başlatıldı", "actions": {"title": "Tarayıcı İşlemi: ", "launch": "{{url}} ad<PERSON><PERSON><PERSON> ba<PERSON>lat", "click": "<PERSON><PERSON><PERSON> ({{coordinate}})", "type": "Yaz \"{{text}}\"", "scrollDown": "Aşağı kaydır", "scrollUp": "Yukarı kaydır", "close": "Tarayıcıyı kapat"}}, "codeblock": {"tooltips": {"expand": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> g<PERSON>", "collapse": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "enable_wrap": "Sa<PERSON>ır kaydırmayı etkinleştir", "disable_wrap": "<PERSON><PERSON><PERSON>r kaydırmayı devre dışı bırak", "copy_code": "Kodu k<PERSON>ala"}}, "systemPromptWarning": "UYARI: <PERSON>zel sistem komut geçersiz kılma aktif. Bu işlevselliği ciddi şekilde bozabilir ve öngörülemeyen davranışlara neden olabilir.", "profileViolationWarning": "Geçerli profil kuruluşunuzun ayarlarını ihlal ediyor", "shellIntegration": {"title": "Komut Çalıştırma Uyarısı", "description": "Komutunuz VSCode terminal kabuk entegrasyonu olmadan çalıştırılıyor. Bu uyarıyı gizlemek için <settingsLink>Kilo Code ayarları</settingsLink>'nın <strong>Terminal</strong> bölümünden kabuk entegrasyonunu devre dışı bırakabilir veya aşağıdaki bağlantıyı kullanarak VSCode terminal entegrasyonu sorunlarını giderebilirsiniz.", "troubleshooting": "Kabuk entegrasyonu belgelerini görmek için buraya tıklayın."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Otomatik Onaylanan İstek Limiti Aşıldı", "description": "Kilo Code, {{count}} API isteği/istekleri için otomatik onaylanan limite ulaştı. Sayacı sıfırlamak ve göreve devam etmek istiyor musunuz?", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Devam Et"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code kod tabanında <code>{{query}}</code> aramak istiyor:", "wantsToSearchWithPath": "Kilo Code <code>{{path}}</code> içinde kod tabanında <code>{{query}}</code> aramak istiyor:", "didSearch": "<code>{{query}}</code> için {{count}} sonu<PERSON> bulundu:", "resultTooltip": "Benzerlik puanı: {{score}} (dosyayı açmak için tıklayın)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deny": {"title": "Tümünü Reddet"}}, "indexingStatus": {"ready": "İndeks hazır", "indexing": "İndeksleniyor {{percentage}}%", "indexed": "İndekslendi", "error": "İndeks hatası", "status": "İndeks durumu"}, "versionIndicator": {"ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{version}} - <PERSON><PERSON><PERSON><PERSON><PERSON> notlarını görüntülemek için tıklayın"}}