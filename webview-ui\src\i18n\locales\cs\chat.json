{"greeting": "Co pro tebe může Kilo Code udělat?", "task": {"title": "Úkol", "seeMore": "Zobrazit více", "seeLess": "Zobrazit méně", "tokens": "Tokeny:", "cache": "Cache:", "apiCost": "Cena API:", "condenseContext": "Inteligentně z<PERSON>t kontext", "contextWindow": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>:", "closeAndStart": "Zavřít úkol a začít nový", "export": "Exportovat historii úkolu", "share": "Sdílet <PERSON>l", "delete": "<PERSON><PERSON><PERSON><PERSON> (Shift + Klik pro přeskočení potvrzení)", "shareWithOrganization": "Sdílet s organizací", "shareWithOrganizationDescription": "Přístup mají pouze členové tvé organizace", "sharePublicly": "Sdílet veřejně", "sharePubliclyDescription": "Kdokoli s odkazem má přístup", "connectToCloud": "<PERSON><PERSON><PERSON><PERSON><PERSON> ke cloudu", "connectToCloudDescription": "Přihlaste se do Kilo Code Cloud pro sdílení úkolů", "sharingDisabledByOrganization": "Sdílení zakázáno organizací", "shareSuccessOrganization": "Odkaz organizace zkopírován do schránky", "shareSuccessPublic": "Veřejný odkaz zkopírován do schránky"}, "history": {"title": "Historie"}, "unpin": "Odepnout", "pin": "Připnout", "retry": {"title": "Zkusit znovu", "tooltip": "Zkusit operaci znovu"}, "startNewTask": {"title": "Začít nový úkol", "tooltip": "Začít nový úkol"}, "reportBug": {"title": "Nahlásit chybu"}, "proceedAnyways": {"title": "<PERSON><PERSON>est<PERSON>", "tooltip": "Pokračovat během provádění příkazu"}, "save": {"title": "Uložit", "tooltip": "Uložit změny souboru"}, "tokenProgress": {"availableSpace": "Dostupný prostor: {{amount}} tokenů", "tokensUsed": "Použité tokeny: {{used}} z {{total}}", "reservedForResponse": "Rezervováno pro odpověď modelu: {{amount}} tokenů"}, "reject": {"title": "Odmítnout", "tooltip": "Odmítnout tuto akci"}, "completeSubtaskAndReturn": "Dokončit podúkol a vrátit se", "approve": {"title": "Schv<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> tuto akci"}, "read-batch": {"approve": {"title": "Schválit vše"}, "deny": {"title": "Odmítnout vše"}}, "runCommand": {"title": "Spustit příkaz", "tooltip": "Provést tento příkaz"}, "proceedWhileRunning": {"title": "Pokra<PERSON><PERSON><PERSON> b<PERSON><PERSON> běhu", "tooltip": "Pokračovat navzdory varováním"}, "killCommand": {"title": "Ukončit příkaz", "tooltip": "Ukončit aktuální příkaz"}, "resumeTask": {"title": "Pokračovat v úkolu", "tooltip": "Pokračovat v aktuálním úkolu"}, "terminate": {"title": "Ukončit", "tooltip": "Ukončit aktuální úkol"}, "cancel": {"title": "Zrušit", "tooltip": "Zrušit aktuální operaci"}, "scrollToBottom": "Posunout se na konec chatu", "about": "<PERSON><PERSON><PERSON>, refaktoruj a ladí kód s pomocí AI. Podívej se na naši <DocsLink>dokumentaci</DocsLink> pro více informací.", "onboarding": "Tvůj seznam úkolů v tomto pracovním prostoru je prázdný.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON>", "description": "Rozděl úkoly na menší, zvládnutelné části"}, "stickyModels": {"title": "Přilepen<PERSON> modely", "description": "Kaž<PERSON><PERSON> režim si pamatuje tvůj naposledy použitý model"}, "tools": {"title": "<PERSON>ás<PERSON><PERSON>", "description": "Umožni AI řešit problémy procházením webu, spouštěním příkazů a dalším"}, "customizableModes": {"title": "Přizpůsobitelné <PERSON>", "description": "Specializované persony s vlastním chováním a přiřazenými modely"}}, "selectMode": "Vyber režim pro interakci", "selectApiConfig": "Vyber konfiguraci API", "selectModelConfig": "Vybrat model", "enhancePrompt": "Vylepšit prompt s dodatečným kontextem", "enhancePromptDescription": "Tlačítko 'Vylepšit prompt' pom<PERSON><PERSON>á zlepšit tvůj prompt poskytnutí<PERSON> dodateč<PERSON>ho <PERSON>, vyjasnění nebo přeformulování. Zkus sem napsat prompt a znovu kliknout na tlačítko, a<PERSON><PERSON>, jak to funguje.", "addImages": "Přidat obrázky do zprávy", "sendMessage": "Odeslat zprávu", "stopTts": "Zastavit převod textu na řeč", "typeMessage": "<PERSON><PERSON><PERSON>...", "typeTask": "<PERSON><PERSON><PERSON><PERSON>, naj<PERSON>, zeptej se na něco", "addContext": "@ pro p<PERSON><PERSON><PERSON><PERSON> konte<PERSON>, / pro př<PERSON><PERSON><PERSON>", "dragFiles": "dr<PERSON> <PERSON> pro přetažení souborů", "dragFilesImages": "drž <PERSON> pro přetažení so<PERSON>orů/obr<PERSON>z<PERSON><PERSON>", "errorReadingFile": "Chyba při čtení souboru:", "noValidImages": "Nebyly zpracovány žádné platné obr<PERSON>zky", "separator": "Odd<PERSON><PERSON><PERSON>č", "edit": "Upravit...", "forNextMode": "pro da<PERSON><PERSON><PERSON>", "apiRequest": {"title": "API požadavek", "failed": "API požadavek selhal", "streaming": "API požadavek...", "cancelled": "API požadavek zrušen", "streamingFailed": "Streamování API selhalo"}, "checkpoint": {"initial": "Počáteční kontrolní bod", "regular": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "initializingWarning": "St<PERSON>le se inicializuje kontrolní bod... <PERSON>kud to trvá př<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> vypnout kontrolní body v <settingsLink>nastavení</settingsLink> a restartovat svůj úkol.", "menu": {"viewDiff": "Zobrazit rozd<PERSON>ly", "restore": "Obnovit kontrolní bod", "restoreFiles": "Ob<PERSON><PERSON> soubory", "restoreFilesDescription": "Obnoví soubory tvého projektu zpět na snímek pořízený v tomto bodě.", "restoreFilesAndTask": "Obnovit soubory a úkol", "confirm": "Potvrdit", "cancel": "Zrušit", "cannotUndo": "<PERSON><PERSON> a<PERSON> nel<PERSON> vrátit zpět.", "restoreFilesAndTaskDescription": "Obnoví soubory tvého projektu zpět na snímek pořízený v tomto bodě a smaže všechny zprávy po tomto bodě."}, "current": "Aktuální"}, "contextCondense": {"title": "Kontext zhušťěn", "condensing": "Zhušťování kontextu...", "errorHeader": "Nepodařilo se zhuštit kontext", "tokens": "tokeny"}, "instructions": {"wantsToFetch": "Kilo Code chce načíst podrobné instrukce pro pomoc s aktuálním úkolem"}, "fileOperations": {"wantsToRead": "Kilo Code chce přečíst tento soubor:", "wantsToReadMultiple": "Kilo Code chce přečíst více souborů:", "wantsToReadAndXMore": "<PERSON>lo Code chce přečíst tento soubor a {{count}} da<PERSON><PERSON><PERSON><PERSON>:", "wantsToReadOutsideWorkspace": "Kilo Code chce přečíst tento soubor mimo pracovní prostor:", "didRead": "Kilo Code přečetl tento soubor:", "wantsToEdit": "Kilo Code chce upravit tento soubor:", "wantsToEditOutsideWorkspace": "Kilo Code chce upravit tento soubor mimo pracovní prostor:", "wantsToEditProtected": "Kilo Code chce upravit chráněný konfigurační soubor:", "wantsToApplyBatchChanges": "Kilo Code chce aplikovat změny na více souborů:", "wantsToCreate": "Kilo Code chce vytvořit nový soubor:", "wantsToSearchReplace": "Kilo Code chce hledat a nahradit v tomto souboru:", "didSearchReplace": "Kilo Code provedl hledání a nahrazení v tomto souboru:", "wantsToInsert": "Kilo Code chce vložit obsah do tohoto souboru:", "wantsToInsertWithLineNumber": "Kilo Code chce vložit obsah do tohoto souboru na řádek {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code chce přidat obsah na konec tohoto souboru:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code chce zobrazit soubory nejvyšší úrovně v tomto adresáři:", "didViewTopLevel": "Kilo Code zobrazil soubory nejvyšší úrovně v tomto adresáři:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code chce zobrazit soubory nejvyšší úrovně v tomto adresáři (mimo pracovní prostor):", "didViewTopLevelOutsideWorkspace": "Kilo Code zobrazil soubory nejvyšší úrovně v tomto adresáři (mimo pracovní prostor):", "wantsToViewRecursive": "Kilo Code chce rekurzivně zobrazit všechny soubory v tomto adresáři:", "didViewRecursive": "Kilo Code rekurzivně zobrazil všechny soubory v tomto adresáři:", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code chce rekurzivně zobrazit všechny soubory v tomto adresáři (mimo pracovní prostor):", "didViewRecursiveOutsideWorkspace": "Kilo Code rekurzivně zobrazil všechny soubory v tomto adresáři (mimo pracovní prostor):", "wantsToViewDefinitions": "Kilo Code chce zobrazit názvy definic zdrojového kódu použité v tomto adresáři:", "didViewDefinitions": "Kilo Code zobrazil názvy definic zdrojového kódu použité v tomto adresáři:", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code chce zobrazit názvy definic zdrojového kódu použité v tomto adresáři (mimo pracovní prostor):", "didViewDefinitionsOutsideWorkspace": "Kilo Code zobrazil názvy definic zdrojového kódu použité v tomto adresáři (mimo pracovní prostor):", "wantsToSearch": "Kilo Code chce hledat v tomto adresáři <code>{{regex}}</code>:", "didSearch": "Kilo Code hledal v tomto adresáři <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code chce hledat v tomto adresáři (mimo pracovní prostor) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code hledal v tomto ad<PERSON> (mimo pracovní prostor) <code>{{regex}}</code>:"}, "codebaseSearch": {"wantsToSearch": "Kilo Code chce hledat v kódové základně <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code chce hledat v kódové základně <code>{{query}}</code> v <code>{{path}}</code>:", "didSearch": "<PERSON><PERSON><PERSON><PERSON> {{count}} v<PERSON><PERSON>dků pro <code>{{query}}</code>:", "resultTooltip": "Skóre podobnosti: {{score}} (klikni pro otevření souboru)"}, "commandOutput": "Výstup příkazu", "response": "Odpověď", "arguments": "Argumenty", "mcp": {"wantsToUseTool": "Kilo Code chce použít nástroj na MCP serveru {{serverName}}:", "wantsToAccessResource": "Kilo Code chce přistupovat k prostředku na MCP serveru {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code chce přepnout do režimu {{mode}}", "wantsToSwitchWithReason": "Kilo Code chce přepnout do režimu {{mode}}, protože: {{reason}}", "didSwitch": "Kilo Code přepnul do režimu {{mode}}", "didSwitchWithReason": "Kilo Code přepnul do režimu {{mode}}, protože: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code chce vytvořit nový podúkol v režimu {{mode}}:", "wantsToFinish": "Kilo Code chce dokončit tento podúkol", "newTaskContent": "Instrukce podúkolu", "completionContent": "Podúkol do<PERSON>č<PERSON>", "resultContent": "Výsledky podúkolu", "defaultResult": "Prosím pokračuj k dalšímu úkolu.", "completionInstructions": "Podúkol dokončen! Můžeš zkontrolovat výsledky a navrhnout jakékoli opravy nebo další kroky. Pokud vše vypadá dobře, potvrď pro vrácení výsledku do nadřazeného úkolu."}, "questions": {"hasQuestion": "Kilo Code má otázku:"}, "taskCompleted": "Úkol dokončen", "error": "Chyba", "diffError": {"title": "Úprava neúspěšná"}, "troubleMessage": "<PERSON>lo Code má potíže...", "powershell": {"issues": "<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> m<PERSON><PERSON> problémy s Windows PowerShell, prosím podívej se na toto"}, "autoApprove": {"title": "<PERSON><PERSON><PERSON>:", "none": "<PERSON><PERSON><PERSON><PERSON>", "description": "Automatické schvalování umožňuje Kilo Code provádět akce bez žádání o povolení. Zapni pouze pro akce, kterým plně důvěřuješ. Podrobnější konfigurace je dostupná v <settingsLink>Nastavení</settingsLink>."}, "modeSelector": {"title": "Re<PERSON><PERSON><PERSON>", "marketplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "settings": "Nastaven<PERSON>", "description": "Specializované persony které p<PERSON>ůsobují chování <PERSON>."}, "announcement": {"title": "🎉 Vydán Roo Code {{version}}", "description": "Roo Code {{version}} přináší hlavní nové funkce a vylepšení na základě tvé zpětné vazby.", "whatsNew": "Co je nového", "feature1": "<bold>Spuštění Roo Marketplace</bold>: Tržiště je nyní živé! Objevuj a instaluj režimy a MCP snadněji než kdy dříve.", "feature2": "<bold>Modely Gemini 2.5</bold>: <PERSON><PERSON><PERSON><PERSON><PERSON> podpora pro nové modely Gemini 2.5 Pro, <PERSON> a <PERSON> Lite.", "feature3": "<bold>Podpora souborů Excel a další</bold>: Přidána podpora souborů Excel (.xlsx) a mnoho oprav chyb a vylepšení!", "hideButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Získej více podrobností a diskutuj na <discordLink>Discord</discordLink> a <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Přemýšlím", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Kopírovat do vstupu (ste<PERSON><PERSON> jako shift + klik)", "autoSelectCountdown": "<PERSON><PERSON><PERSON> v<PERSON>ěr za {{count}}s", "countdownDisplay": "{{count}}s"}, "browser": {"rooWantsToUse": "Kilo Code chce použít prohlížeč:", "consoleLogs": "Záznamy konzole", "noNewLogs": "(Žádné nové záznamy)", "screenshot": "Snímek obrazovky prohlížeče", "cursor": "kurzor", "navigation": {"step": "<PERSON>rok {{current}} z {{total}}", "previous": "Předchozí", "next": "Dalš<PERSON>"}, "sessionStarted": "<PERSON><PERSON>ahá<PERSON>", "actions": {"title": "<PERSON><PERSON><PERSON>: ", "launch": "Spustit prohlížeč na {{url}}", "click": "Kliknout ({{coordinate}})", "type": "Napsat \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON>", "scrollUp": "<PERSON><PERSON><PERSON>", "close": "<PERSON>av<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Rozbalit blok kódu", "collapse": "Sbalit blok kódu", "enable_wrap": "Zapnout zalamování slov", "disable_wrap": "Vypnout zalamování slov", "copy_code": "Kopírovat kód"}}, "systemPromptWarning": "VAROVÁNÍ: Aktivní vlastní přepsání systémového promptu. To může vážně narušit funkčnost a způsobit nepředvídatelné chování.", "profileViolationWarning": "Aktuální profil porušuje nastavení tvé organizace", "shellIntegration": {"title": "Varování provádění příkazu", "description": "Tvůj příkaz se provádí bez integrace shellu terminálu VSCode. Pro potlačení tohoto varování můžeš vypnout integraci shellu v sekci <strong>Terminal</strong> <settingsLink>nastavení Kilo Code</settingsLink> nebo vyřešit integraci terminálu VSCode pomocí odkazu níže.", "troubleshooting": "Klikni zde pro dokumentaci integrace shellu."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Dosažen limit automaticky schválených požadavků", "description": "Kilo Code dosáhl automaticky schváleného limitu {{count}} API požadavků. Chceš resetovat počítadlo a pokračovat v úkolu?", "button": "Resetovat a pokračovat"}}, "indexingStatus": {"ready": "Index připraven", "indexing": "Indexování {{percentage}}%", "indexed": "Indexováno", "error": "Chyba indexu", "status": "Stav indexu"}, "versionIndicator": {"ariaLabel": "Verze {{version}} - Klikni pro zobrazení poznámek k vydání"}}