{"answers": {"yes": "是", "no": "否", "cancel": "取消", "remove": "移除", "keep": "保留"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "反馈", "description": "我们很乐意听取您的反馈或帮助您解决遇到的任何问题。", "githubIssues": "在GitHub上报告问题", "githubDiscussions": "加入GitHub讨论", "discord": "加入我们的Discord社区", "customerSupport": "客户支持"}, "ui": {"search_placeholder": "搜索..."}, "mermaid": {"loading": "生成 Mermaid 图表中...", "render_error": "无法渲染图表", "fixing_syntax": "修复 Mermaid 语法中...", "fix_syntax_button": "使用 AI 修复语法", "original_code": "原始代码：", "errors": {"unknown_syntax": "未知语法错误", "fix_timeout": "AI 修复请求超时", "fix_failed": "AI 修复失败", "fix_attempts": "在 {{attempts}} 次尝试后修复语法失败。最后错误：{{error}}", "no_fix_provided": "AI 未能提供修复", "fix_request_failed": "修复请求失败"}, "buttons": {"zoom": "缩放", "zoomIn": "放大", "zoomOut": "缩小", "copy": "复制", "save": "保存图片", "viewCode": "查看代码", "viewDiagram": "查看图表", "close": "关闭"}, "modal": {"codeTitle": "Mermaid 代码"}, "tabs": {"diagram": "图表", "code": "代码"}, "feedback": {"imageCopied": "图片已复制到剪贴板", "copyError": "复制图片时出错"}}, "file": {"errors": {"invalidDataUri": "无效的数据 URI 格式", "copyingImage": "复制图片时出错: {{error}}", "openingImage": "打开图片时出错: {{error}}", "pathNotExists": "路径不存在: {{path}}", "couldNotOpen": "无法打开文件: {{error}}", "couldNotOpenGeneric": "无法打开文件!"}, "success": {"imageDataUriCopied": "图片数据 URI 已复制到剪贴板"}}}