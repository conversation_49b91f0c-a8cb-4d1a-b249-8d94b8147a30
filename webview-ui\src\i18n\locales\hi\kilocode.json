{"welcome": {"greeting": "<PERSON><PERSON> Code में आपका स्वागत है!", "introText1": "Kilo Code एक मुफ्त, ओपन सोर्स AI कोडिंग एजेंट है।", "introText2": "यह Claude 4 <PERSON><PERSON>, Gemini 2.5 Pro, GPT-4.1, और 450+ अन्य नवीनतम AI मॉडल्स के साथ काम करता है।", "introText3": "एक मुफ्त खाता बनाएं और किसी भी AI मॉडल के साथ उपयोग के लिए $20 के tokens प्राप्त करें।", "ctaButton": "मुफ्त खाता बनाएं", "manualModeButton": "अपनी स्वयं की API कुंजी का उपयोग करें", "alreadySignedUp": "पहले से साइन अप हैं?", "loginText": "यहाँ लॉग इन करें"}, "lowCreditWarning": {"addCredit": "क्रेडिट जोड़ें", "lowBalance": "आपका Kilo Code बैलेंस कम है"}, "notifications": {"toolRequest": "उपकरण अनुरोध अनुमोदन की प्रतीक्षा कर रहा है", "browserAction": "ब्राउज़र कार्रवाई अनुमोदन की प्रतीक्षा कर रही है", "command": "कमांड अनुमोदन की प्रतीक्षा कर रहा है"}, "settings": {"sections": {"mcp": "MCP सर्वर"}, "provider": {"account": "Kilo Code खाता", "apiKey": "Kilo Code API कुंजी", "login": "<PERSON><PERSON> Code में लॉग इन करें", "logout": "<PERSON><PERSON> Code से लॉग आउट करें"}, "contextManagement": {"allowVeryLargeReads": {"label": "बहुत बड़ी फ़ाइल पढ़ने की अनुमति दें", "description": "जब सक्षम हो, तो Kilo Code बहुत बड़ी फ़ाइल या MCP आउटपुट रीड्स करेगा भले ही संदर्भ विंडो के ओवरफ्लो होने की उच्च संभावना हो (सामग्री का आकार संदर्भ विंडो का >80%)।"}}, "systemNotifications": {"label": "सिस्टम नोटिफिकेशन सक्षम करें", "description": "जब सक्षम हो, तो Kilo Code महत्वपूर्ण घटनाओं जैसे कार्य पूर्णता या त्रुटियों के लिए सिस्टम नोटिफिकेशन भेजेगा।", "testButton": "नोटिफिकेशन का परीक्षण करें", "testTitle": "Kilo Code", "testMessage": "यह <PERSON>lo Code से एक परीक्षण नोटिफिकेशन है।"}}, "chat": {"condense": {"wantsToCondense": "<PERSON><PERSON> Code तुम्हारी बातचीत को संक्षिप्त करना चाहता है", "condenseConversation": "बातचीत संक्षिप्त करें"}}, "newTaskPreview": {"task": "कार्य"}, "profile": {"title": "प्रोफ़ाइल", "dashboard": "डैशबोर्ड", "logOut": "लॉग आउट", "currentBalance": "वर्तमान बैलेंस", "loading": "लोड हो रहा है..."}, "docs": "दस्तावेज़", "rules": {"tooltip": "<PERSON>lo Code नियम और वर्कफ़्लो प्रबंधित करें", "ariaLabel": "Kilo Code नियम", "tabs": {"rules": "नियम", "workflows": "वर्कफ़्लो"}, "description": {"rules": "नियम आपको Kilo Code को निर्देश प्रदान करने की अनुमति देते हैं जिनका उसे सभी मोड्स में और सभी प्रॉम्प्ट्स के लिए पालन करना चाहिए। ये आपके वर्कस्पेस में या वैश्विक रूप से सभी बातचीत के लिए संदर्भ और प्राथमिकताओं को शामिल करने का एक स्थायी तरीका हैं।", "workflows": "वर्कफ़्लो एक बातचीत के लिए एक तैयार टेम्प्लेट हैं। वर्कफ़्लो आपको उन प्रॉम्प्ट्स को परिभाषित करने की अनुमति देते हैं जिनका आप अक्सर उपयोग करते हैं, और इसमें Kilo Code को दोहराए जाने वाले कार्यों के माध्यम से मार्गदर्शन करने के लिए चरणों की एक श्रृंखला शामिल हो सकती है, जैसे कि सेवा तैनात करना या PR सबमिट करना। वर्कफ़्लो को आमंत्रित करने के लिए, टाइप करें", "workflowsInChat": "चैट में।"}, "sections": {"globalRules": "वैश्विक नियम", "workspaceRules": "वर्कस्पेस नियम", "globalWorkflows": "वैश्विक वर्कफ़्लो", "workspaceWorkflows": "वर्कस्पेस वर्कफ़्लो"}, "validation": {"invalidFileExtension": "केवल .md, .txt या कोई फ़ाइल एक्सटेंशन की अनुमति है"}, "placeholders": {"workflowName": "वर्कफ़्लो-नाम (.md, .txt या कोई एक्सटेंशन नहीं)", "ruleName": "नियम-नाम (.md, .txt या कोई एक्सटेंशन नहीं)"}, "newFile": {"newWorkflowFile": "नई वर्कफ़्लो फ़ाइल...", "newRuleFile": "नई नियम फ़ाइल..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "{{messageType}} (#{{messageNumber}}) देखें", "messageTypes": {"browser_action_launch": "ब्राउज़र लॉन्च", "browser_action": "ब्राउज़र क्रिया", "checkpoint_saved": "चेकपॉइंट सहेजा गया", "browser_action_result": "ब्राउज़र क्रिया परिणाम", "command": "कमांड एक्जीक्यूशन", "command_output": "कमांड आउटपुट", "completion_result": "पूर्णता परिणाम", "error": "त्रुटि संदेश", "mcp_server_response": "MCP सर्वर प्रतिक्रिया", "condense_context": "संदर्भ संक्षिप्त करें", "followup": "अनुवर्ती प्रश्न", "reasoning": "AI तर्क", "text": "AI प्रतिक्रिया", "unknown": "अज्ञात संदेश प्रकार", "tool": "उपकरण उपयोग", "use_mcp_server": "MCP सर्वर उपयोग", "user": "उपयोगकर्ता प्रतिक्रिया"}}}, "userFeedback": {"editCancel": "रद्<PERSON> करें", "send": "भेजें", "restoreAndSend": "पुनर्स्थापित करें और भेजें"}, "ideaSuggestionsBox": {"newHere": "यहाँ नए हैं?", "suggestionText": "एक बेहतरीन विचार के लिए <suggestionButton>यहाँ क्लिक करें</suggestionButton>, फिर <sendIcon /> पर टैप करें और मुझे जादू करते देखें!", "ideas": {"idea1": "एक घूमता हुआ, चमकता हुआ 3D गोला बनाएं जो माउस की गतिविधियों पर प्रतिक्रिया करे। ऐप को ब्राउज़र में काम करने योग्य बनाएं।", "idea2": "एक Python सॉफ्टवेयर डेवलपर के लिए पोर्टफोलियो वेबसाइट बनाएं", "idea3": "ब्राउज़र में एक वित्तीय ऐप मॉकअप बनाएं। फिर परीक्षण करें कि यह काम करता है या नहीं", "idea4": "एक डायरेक्टरी वेबसाइट बनाएं जिसमें वर्तमान में सर्वश्रेष्ठ AI वीडियो जेनरेशन मॉडल हों", "idea5": "एक CSS ग्रेडिएंट जेनरेटर बनाएं जो कस्टम स्टाइलशीट एक्सपोर्ट करे और लाइव प्रीव्यू दिखाए", "idea6": "ऐसे कार्ड बनाएं जो होवर करने पर गतिशील सामग्री को सुंदर तरीके से प्रकट करें", "idea7": "एक शांत इंटरैक्टिव स्टारफील्ड बनाएं जो माउस की गतिविधियों के साथ चले"}}}