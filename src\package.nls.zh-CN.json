{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "用于规划、构建和修复代码的开源 AI 编码助手。", "command.newTask.title": "新建任务", "command.explainCode.title": "解释代码", "command.fixCode.title": "修复代码", "command.improveCode.title": "改进代码", "command.addToContext.title": "添加到上下文", "command.openInNewTab.title": "在新标签页中打开", "command.focusInput.title": "聚焦输入框", "command.setCustomStoragePath.title": "设置自定义存储路径", "command.importSettings.title": "导入设置", "command.terminal.addToContext.title": "将终端内容添加到上下文", "command.terminal.fixCommand.title": "修复此命令", "command.terminal.explainCommand.title": "解释此命令", "command.acceptInput.title": "接受输入/建议", "command.generateCommitMessage.title": "使用 Kilo 生成提交消息", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "MCP 服务器", "command.prompts.title": "模式", "command.history.title": "历史记录", "command.marketplace.title": "应用市场", "command.openInEditor.title": "在编辑器中打开", "command.settings.title": "设置", "command.documentation.title": "文档", "command.profile.title": "配置文件", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "当启用'始终批准执行操作'时可以自动执行的命令", "settings.vsCodeLmModelSelector.description": "VSCode 语言模型 API 的设置", "settings.vsCodeLmModelSelector.vendor.description": "语言模型的供应商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "语言模型的系列（例如：gpt-4）", "settings.customStoragePath.description": "自定义存储路径。留空以使用默认位置。支持绝对路径（例如：'D:\\KiloCodeStorage'）", "settings.enableCodeActions.description": "启用 Kilo Code 快速修复", "settings.autoImportSettingsPath.description": "Kilo Code 配置文件的路径，用于在扩展启动时自动导入。支持绝对路径和相对于主目录的路径（例如 '~/Documents/kilo-code-settings.json'）。留空以禁用自动导入。", "ghost.input.title": "Kilo Code 幽灵写手", "ghost.input.placeholder": "描述您想要编程的内容...", "ghost.commands.generateSuggestions": "Kilo Code：生成建议编辑", "ghost.commands.displaySuggestions": "显示建议编辑", "ghost.commands.cancelSuggestions": "取消建议编辑", "ghost.commands.applyCurrentSuggestion": "应用当前建议编辑", "ghost.commands.applyAllSuggestions": "应用所有建议编辑", "ghost.commands.promptCodeSuggestion": "快速任务", "ghost.commands.goToNextSuggestion": "转到下一个建议", "ghost.commands.goToPreviousSuggestion": "转到上一个建议"}