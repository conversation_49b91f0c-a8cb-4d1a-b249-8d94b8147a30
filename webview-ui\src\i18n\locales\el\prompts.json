{"title": "Λειτουργ<PERSON>ες", "done": "Τέλος", "modes": {"title": "Λειτουργ<PERSON>ες", "createNewMode": "Δημιουργία νέας λειτουργίας", "importMode": "Εισαγωγ<PERSON> Λειτουργίας", "editModesConfig": "Επεξεργασία ρυθμίσεων λειτουργιών", "editGlobalModes": "Επεξεργασία Global Modes", "editProjectModes": "Επεξεργασία Project Modes (.kilocodemodes)", "createModeHelpText": "Οι λειτουργίες είναι εξειδικευμένες προσωπικότητες που προσαρμόζουν τη συμπεριφορά του Kilo Code. <0>Μάθε για τη Χρήση Λειτουργιών</0> ή <1>την Προσαρμογή Λειτουργιών.</1>", "selectMode": "Αναζήτηση λειτουργιών", "noMatchFound": "Δεν βρέθηκαν λειτουργίες"}, "apiConfiguration": {"title": "Ρύθμιση API", "select": "Επίλεξε ποια ρύθμιση API θα χρησιμοποιηθεί για αυτή τη λειτουργία"}, "tools": {"title": "Διαθέσιμα Εργαλεία", "builtInModesText": "Τα εργαλεία για τις ενσωματωμένες λειτουργίες δεν μπορούν να τροποποιηθούν", "editTools": "Επεξεργασία εργαλείων", "doneEditing": "Τέλος επεξεργασίας", "allowedFiles": "Επιτρεπόμενα αρχεία:", "toolNames": {"read": "Ανάγνωση Αρχείων", "edit": "Επεξεργασία Αρχείων", "browser": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "command": "Εκτέλεση Εντολών", "mcp": "Χρήση MCP"}, "noTools": "Κανένα"}, "roleDefinition": {"title": "Ορισμός Ρόλου", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλογή", "description": "Όρισε την εξειδίκευση και την προσωπικότητα του Kilo Code για αυτή τη λειτουργία. Αυτή η περιγραφή διαμορφώνει πώς το Kilo Code παρουσιάζεται και προσεγγίζει τις εργασίες."}, "description": {"title": "Σύντομη περιγραφή (για ανθρώπους)", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλεγμένη περιγραφή", "description": "Μια σύντομη περιγραφή που εμφανίζεται στο αναπτυσσόμενο μενού του επιλογέα λειτουργιών."}, "whenToUse": {"title": "Πότε να Χρησιμοποιηθεί (προαιρετικό)", "description": "Περίγραψε πότε πρέπει να χρησιμοποιείται αυτή η λειτουργία. Αυτό βοηθά τον Orchestrator να επιλέξει τη σωστή λειτουργία για μια εργασία.", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλεγμένη περιγραφή 'Πότε να Χρησιμοποιηθεί'"}, "customInstructions": {"title": "Προσαρμοσμ<PERSON><PERSON><PERSON>ς Οδηγίες <PERSON>ειτουργίας (προαιρετικό)", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλογή", "description": "Πρόσθεσε οδηγίες συμπεριφοράς ειδικές για τη λειτουργία {{modeName}}.", "loadFromFile": "Οι προσαρμοσμένες οδηγίες ειδικές για τη λειτουργία {{mode}} μπορούν επίσης να φορτωθούν από τον φάκελο <span>.kilocode/rules/</span> στον χώρο εργασίας σου (.kilocoderules-{{slug}} είναι παρωχημένο και θα σταματήσει να λειτουργεί σύντομα)."}, "exportMode": {"title": "Εξαγωγή Λειτουργίας", "description": "Εξήγαγε αυτή τη λειτουργία με κανόνες από τον φάκελο .kilocode/rules-{{slug}}/ συνδυασμένους σε ένα κοινόχρηστο αρχείο YAML. Τα αρχικά αρχεία παραμένουν αμετάβλητα.", "exporting": "Εξαγωγή..."}, "importMode": {"selectLevel": "Επίλεξε πού θα εισαχθεί αυτή η λειτουργία:", "import": "Εισαγωγή", "importing": "Εισαγωγή...", "global": {"label": "Επίπεδο Global", "description": "Διαθέσιμο σε όλα τα έργα. Αν η εξαγόμενη λειτουργία περιείχε αρχεία κανόνων, θα αναδημιουργηθούν στον global φάκελο .kilocode/rules-{slug}/."}, "project": {"label": "Επίπεδο Έργου", "description": "Διαθέσιμο μόνο σε αυτόν τον χώρο εργασίας. Αν η εξαγόμενη λειτουργία περιείχε αρχεία κανόνων, θα αναδημιουργηθούν στον φάκελο .kilocode/rules-{slug}/."}}, "advanced": {"title": "Προχωρημένο: Παράκαμψη System Prompt"}, "globalCustomInstructions": {"title": "Προσαρμοσμένες Οδηγίες για Όλες τις Λειτουργίες", "description": "Αυτές οι οδηγίες ισχύουν για όλες τις λειτουργίες. Παρέχουν ένα βασικό σύνολο συμπεριφορών που μπορούν να ενισχυθούν από τις οδηγίες ειδικές για κάθε λειτουργία παρακάτω. <0>Μάθε περισσότερα</0>", "loadFromFile": "Οι οδηγίες μπορούν επίσης να φορτωθούν από τον φάκελο <span>.kilocode/rules/</span> στον χώρο εργασίας σου (.kilocoderules είναι παρωχημένο και θα σταματήσει να λειτουργεί σύντομα)."}, "systemPrompt": {"preview": "Προεπισκόπηση System Prompt", "copy": "Αντιγραφή system prompt στο πρόχειρο", "title": "System Prompt (λειτουργία {{modeName}})"}, "supportPrompts": {"title": "Υποστηρικτικά Prompts", "resetPrompt": "Επανα<PERSON><PERSON><PERSON><PERSON> {{promptType}} prompt στην προεπιλογή", "prompt": "Prompt", "enhance": {"apiConfiguration": "Ρύθμιση API", "apiConfigDescription": "Μπορείς να επιλέξεις μια ρύθμιση API να χρησιμοποιείται πάντα για τη βελτίωση των prompts, ή απλά να χρησιμοποιείς ό,τι είναι επιλεγμένο αυτή τη στιγμή", "useCurrentConfig": "Χρήση της τρέχουσας επιλεγμένης ρύθμισης API", "testPromptPlaceholder": "Εισήγαγε ένα prompt για να δοκιμάσεις τη βελτίωση", "previewButton": "Προεπισκόπηση Βελτίωσης Prompt", "testEnhancement": "Δοκιμή Βελτίωσης"}, "types": {"ENHANCE": {"label": "Βελτίωση Prompt", "description": "Χρησιμοποίησε τη βελτίωση prompt για να λάβεις προσαρμοσμένες προτάσεις ή βελτιώσεις για τις εισόδους σου. Αυτ<PERSON> διασφαλίζει ότι το Kilo Code κατανοεί την πρόθεσή σου και παρέχει τις καλύτερες δυνατές απαντήσεις. Διαθέσιμο μέσω του εικονιδίου ✨ στη συνομιλία."}, "EXPLAIN": {"label": "Εξήγηση Κώδικα", "description": "Λάβε λεπτομερείς εξηγήσεις για αποσπάσματα κώδικα, συναρτήσεις ή ολόκληρα αρχεία. Χρήσιμο για την κατανόηση πολύπλοκου κώδικα ή την εκμάθηση νέων patterns. Διαθέσιμο στις ενέργειες κώδικα (εικονίδιο λάμπας στον επεξεργαστή) και στο μενού περιβάλλοντος του επεξεργαστή (δεξί κλικ στον επιλεγμένο κώδικα)."}, "FIX": {"label": "Διόρθωση Προβλημάτων", "description": "Λάβε βοήθεια για τον εντοπισμό και την επίλυση σφαλμάτων, λαθών ή προβλημάτων ποιότητας κώδικα. Παρέχει βήμα προς βήμα καθοδήγηση για τη διόρθωση προβλημάτων. Διαθέσιμο στις ενέργειες κώδικα (εικονίδιο λάμπας στον επεξεργαστή) και στο μενού περιβάλλοντος του επεξεργαστή (δεξ<PERSON> κλικ στον επιλεγμένο κώδικα)."}, "IMPROVE": {"label": "Βελτίωση Κώδικα", "description": "Λάβε προτάσεις για βελτιστοπο<PERSON>η<PERSON>η κώδικα, καλύτερες πρακτικές και αρχιτεκτονικές βελτιώσεις διατηρώντας τη λειτουργικότητα. Διαθέσιμο στις ενέργειες κώδικα (εικονίδιο λάμπας στον επεξεργαστή) και στο μενού περιβάλλοντος του επεξεργαστή (δεξί κλικ στον επιλεγμένο κώδικα)."}, "ADD_TO_CONTEXT": {"label": "Προσθήκη στο Context", "description": "Πρόσθεσε context στην τρέχουσα εργασία ή συνομιλία σου. Χρήσιμο για την παροχή πρόσθετων πληροφοριών ή διευκρινίσεων. Διαθέσιμο στις ενέργειες κώδικα (εικονίδιο λάμπας στον επεξεργαστή) και στο μενού περιβάλλοντος του επεξεργαστή (δεξί κλικ στον επιλεγμένο κώδικα)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Προσθήκη Περιεχομένου Terminal στο Context", "description": "Πρόσθεσε την έξοδο του terminal στην τρέχουσα εργασία ή συνομιλία σου. Χρήσιμο για την παροχή εξόδων εντολών ή logs. Διαθέσιμο στο μενού περιβάλλοντος του terminal (δεξί κλικ στο επιλεγμένο περιεχόμενο terminal)."}, "TERMINAL_FIX": {"label": "Διόρθωση Εντολής Terminal", "description": "Λάβε βοήθεια για τη διόρθωση εντολών terminal που απέτυχαν ή χρειάζονται βελτίωση. Διαθέσιμο στο μενού περιβάλλοντος του terminal (δεξί κλικ στο επιλεγμένο περιεχόμενο terminal)."}, "TERMINAL_EXPLAIN": {"label": "Εξήγηση Εντολής Terminal", "description": "Λάβε λεπτομερείς εξηγήσεις για εντολές terminal και τις εξόδους τους. Διαθέσιμο στο μενού περιβάλλοντος του terminal (δεξ<PERSON> κλικ στο επιλεγμένο περιεχόμενο terminal)."}, "NEW_TASK": {"label": "Έναρξη Νέας Εργασίας", "description": "Ξεκίνα μια νέα εργασία με είσοδο χρήστη. Διαθέσιμο στην Παλέτα Εντολών."}, "COMMIT_MESSAGE": {"label": "Δημιουργ<PERSON><PERSON> Μη<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Δημιούργησε περιγρα<PERSON><PERSON><PERSON><PERSON> μηνύματα commit βάσει των staged git αλλαγών σου. Προσάρμοσε το prompt για να δημιουργήσεις μηνύματα που ακολουθούν τις βέλτιστες πρακτικές του repository σου."}}}, "advancedSystemPrompt": {"title": "Προχωρημένο: Παράκαμψη System Prompt", "description": "<2>⚠️ Προειδοποίηση:</2> Αυτ<PERSON> η προχωρημένη λειτουργία παρακάμπτει τις προφυλάξεις. <1>ΔΙΑΒΑΣΕ ΑΥΤΟ ΠΡΙΝ ΤΗ ΧΡΗΣΗ!</1>Παράκαμψε το προεπιλεγμένο system prompt δημιουργώντας ένα αρχείο στο <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Δημιουργ<PERSON><PERSON> Νέας Λειτουργίας", "close": "Κλείσιμο", "name": {"label": "Όνομα", "placeholder": "Εισήγαγε όνομα λειτουργίας"}, "slug": {"label": "Slug", "description": "Το slug χρησιμοποιείται σε URLs και ονόματα αρχείων. Πρέπει να είναι πεζά και να περιέχει μόνο γράμματα, αριθμούς και παύλες."}, "saveLocation": {"label": "Τοποθεσία Αποθήκευσης", "description": "Επίλεξε πού θα αποθηκευτεί αυτή η λειτουργία. Οι λειτουργίες ειδικές για το έργο έχουν προτεραιότητα έναντι των global.", "global": {"label": "Global", "description": "Διαθέσιμο σε όλους τους χώρους εργασίας"}, "project": {"label": "Ειδικό για το έργο (.kilocodemodes)", "description": "Διαθέσι<PERSON><PERSON> μόνο σε αυτόν τον χώρο εργασίας, έχει προτεραιότητα έναντι του global"}}, "roleDefinition": {"label": "Ορισμός Ρόλου", "description": "Όρισε την εξειδίκευση και την προσωπικότητα του Kilo Code για αυτή τη λειτουργία."}, "description": {"label": "Σύντομη περιγραφή (για ανθρώπους)", "description": "Μια σύντομη περιγραφή που εμφανίζεται στο αναπτυσσόμενο μενού επιλογής λειτουργίας."}, "whenToUse": {"label": "Πότε να Χρησιμοποιηθεί (προαιρετικό)", "description": "Δώσε μια σαφή περιγραφή του πότε αυτή η λειτουργία είναι πιο αποτελεσματική και σε ποιους τύπους εργασιών υπερέχει."}, "tools": {"label": "Διαθέσιμα Εργαλεία", "description": "Επίλεξε ποια εργαλεία μπορεί να χρησιμοποιήσει αυτή η λειτουργία."}, "customInstructions": {"label": "Προσαρμοσμ<PERSON><PERSON><PERSON>ς <PERSON>δ<PERSON>γ<PERSON> (προαιρετικό)", "description": "Πρόσθεσε οδηγίες συμπεριφοράς ειδικές για αυτή τη λειτουργία."}, "buttons": {"cancel": "Ακύρωση", "create": "Δημιουργ<PERSON><PERSON> Λειτουργίας"}, "deleteMode": "Διαγρα<PERSON><PERSON> λειτουργίας"}, "allFiles": "όλα τα αρχεία", "deleteMode": {"title": "Διαγρα<PERSON><PERSON> Λειτουργίας", "message": "Είσαι σίγουρος ότι θέλεις να διαγράψεις τη λειτουργία \"{{modeName}}\";", "rulesFolder": "Αυτή η λειτουργ<PERSON>α έχει έναν φάκελο κανόνων στο {{folderPath}} που θα διαγραφεί επίσης.", "descriptionNoRules": "Είσαι σίγουρος ότι θέλεις να διαγράψεις αυτή την προσαρμοσμένη λειτουργία;", "confirm": "Διαγραφή", "cancel": "Ακύρωση"}}