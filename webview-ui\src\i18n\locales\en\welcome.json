{"greeting": "Hi, I'm <PERSON><PERSON>!", "introduction": "<strong>Kilo Code is the premiere autonomous coding agent.</strong> Get ready to architect, code, debug, and boost your productivity like you've never seen before. To continue, Kilo Code requires an API key.", "notice": "To get started, this extension needs an API provider.", "start": "Let's go!", "routers": {"requesty": {"description": "Your optimized LLM router", "incentive": "$1 free credit"}, "openrouter": {"description": "A unified interface for LLMs"}}, "chooseProvider": "To do its magic, Kilo Code needs an API key.", "startRouter": "We recommend using an LLM Router:", "startCustom": "Or you can bring your provider API key:", "telemetry": {"title": "Help Improve Kilo Code", "anonymousTelemetry": "Send error and usage data to help us fix bugs and improve the extension. No code, prompts, or personal information is ever sent.", "changeSettings": "You can always change this at the bottom of the <settingsLink>settings</settingsLink>", "settings": "settings", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "importSettings": "Import Settings"}