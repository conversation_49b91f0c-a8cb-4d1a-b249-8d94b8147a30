{"welcome": {"greeting": "Selamat datang di Kilo Code!", "introText1": "Kilo Code adalah agen coding AI gratis dan open source.", "introText2": "<PERSON><PERSON><PERSON> dengan model AI terbaru seperti Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, dan lebih dari 450 model la<PERSON><PERSON>.", "introText3": "Buat akun gratis dan dapatkan $20 token untuk digunakan dengan model AI apapun.", "ctaButton": "<PERSON><PERSON>t akun gratis", "manualModeButton": "Gunakan API key sendiri", "alreadySignedUp": "Sudah mendaftar?", "loginText": "<PERSON><PERSON><PERSON> di sini"}, "lowCreditWarning": {"addCredit": "Tambah Kredit", "lowBalance": "<PERSON>do <PERSON> kamu rendah"}, "notifications": {"toolRequest": "Permintaan tool menunggu persetujuan", "browserAction": "Aksi browser menunggu persetu<PERSON>an", "command": "<PERSON><PERSON><PERSON>"}, "settings": {"sections": {"mcp": "Server MCP"}, "provider": {"account": "<PERSON><PERSON><PERSON>", "apiKey": "API Key Kilo Code", "login": "Masuk ke Kilo Code", "logout": "<PERSON><PERSON><PERSON> dari <PERSON>"}, "contextManagement": {"allowVeryLargeReads": {"label": "Izinkan pembacaan file yang sangat besar", "description": "<PERSON><PERSON> di<PERSON>, Kilo Code akan melakukan pembacaan file atau output MCP yang sangat besar meskipun ada kemungkinan tinggi meluapnya jendela konteks (ukuran konten >80% dari jendela konteks)."}}, "systemNotifications": {"label": "Aktifkan Notifikasi Sistem", "description": "<PERSON><PERSON>, <PERSON><PERSON> akan mengirim notifikasi sistem untuk kejadian penting seperti penyelesaian tugas atau kesalahan.", "testButton": "<PERSON><PERSON>", "testTitle": "Kilo Code", "testMessage": "Ini adalah notifikasi uji dari <PERSON>."}}, "chat": {"condense": {"wantsToCondense": "Kilo <PERSON> ingin mengonden<PERSON>si percakapan kamu", "condenseConversation": "Kondensasi <PERSON>"}}, "newTaskPreview": {"task": "Tugas"}, "profile": {"title": "Profil", "dashboard": "Dashboard", "logOut": "<PERSON><PERSON><PERSON>", "currentBalance": "SALDO SAAT INI", "loading": "Memuat..."}, "docs": "Dokumentasi", "rules": {"tooltip": "Kelola Aturan & Alur Kerja <PERSON>", "ariaLabel": "Aturan Kilo <PERSON>", "tabs": {"rules": "<PERSON><PERSON><PERSON>", "workflows": "<PERSON><PERSON>"}, "description": {"rules": "Aturan memungkinkan kamu memberikan instruksi kepada Kilo Code yang harus diikuti di semua mode dan untuk semua prompt. <PERSON><PERSON><PERSON> adalah cara persisten untuk menyertakan konteks dan preferensi untuk semua percakapan di workspace kamu atau secara global.", "workflows": "Alur kerja adalah template yang disiapkan untuk percakapan. Alur kerja memungkinkan kamu menentukan prompt yang sering kamu gunakan, dan dapat mencakup serangkaian langkah untuk memandu Kilo Code melalui tugas-tugas berulang, seperti menerapkan layanan atau mengirim PR. Untuk memanggil alur kerja, ketik", "workflowsInChat": "di chat."}, "sections": {"globalRules": "Aturan Global", "workspaceRules": "Aturan Workspace", "globalWorkflows": "Alur <PERSON>rja <PERSON>", "workspaceWorkflows": "Alur Kerja Workspace"}, "validation": {"invalidFileExtension": "<PERSON>ya ekstensi .md, .txt atau tanpa ekstensi yang diizinkan"}, "placeholders": {"workflowName": "nama-alur-kerja (.md, .txt atau tanpa ekstensi)", "ruleName": "nama-aturan (.md, .txt atau tanpa ekstensi)"}, "newFile": {"newWorkflowFile": "File alur kerja baru...", "newRuleFile": "File aturan baru..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Lihat {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action": "aksi peramban", "browser_action_result": "hasil tindakan peramban", "browser_action_launch": "peluncuran browser", "command_output": "hasil perintah", "checkpoint_saved": "pos pemeriks<PERSON> te<PERSON>impan", "completion_result": "hasil <PERSON>", "command": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "condense_context": "ringkas konteks", "error": "pesan error", "followup": "<PERSON><PERSON><PERSON>", "mcp_server_response": "Respon server MCP", "text": "Respons AI", "reasoning": "Penalaran AI", "tool": "pengg<PERSON>an alat", "unknown": "jenis pesan tidak di<PERSON>i", "use_mcp_server": "Penggunaan server MCP", "user": "umpan balik pengguna"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "restoreAndSend": "Pulihkan & Kirim"}, "ideaSuggestionsBox": {"newHere": "Baru di sini?", "suggestionText": "<suggestionButton><PERSON><PERSON> di sini</suggestionButton> untuk ide yang keren, lalu ketuk <sendIcon /> dan lihat saya melakukan keajaiban!", "ideas": {"idea1": "Buat bola 3D yang berputar dan bersinar yang merespons gerakan mouse. Buat aplikasi berfungsi di browser.", "idea2": "Buat website portfolio untuk pengembang perangkat lunak Python", "idea3": "Buat mockup aplikasi keuangan di browser. <PERSON><PERSON> uji a<PERSON> berf<PERSON>", "idea4": "Buat website direktori yang berisi model generasi video AI terbaik saat ini", "idea5": "Buat generator gradien CSS yang mengekspor stylesheet kustom dan menampilkan pratinjau langsung", "idea6": "Buat kartu yang mengungkap konten dinamis dengan indah saat dihover", "idea7": "Buat starfield interaktif yang menenangkan yang bergerak dengan gerakan mouse"}}}