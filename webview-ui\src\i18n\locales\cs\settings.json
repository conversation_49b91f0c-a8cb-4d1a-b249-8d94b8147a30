{"common": {"save": "Uložit", "done": "Hotovo", "cancel": "Zrušit", "reset": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "add": "Přidat záhlaví", "remove": "Odstranit"}, "header": {"title": "Nastavení", "saveButtonTooltip": "Uložit změny", "nothingChangedTooltip": "<PERSON>c se nezměnilo", "doneButtonTooltip": "Zahodit neuložené změny a zavřít panel nastavení"}, "unsavedChangesDialog": {"title": "Neuložené změny", "description": "Chcete zahodit změny a pokračovat?", "cancelButton": "Zrušit", "discardButton": "Zahodit změny"}, "sections": {"providers": "Poskytovatelé", "autoApprove": "<PERSON><PERSON><PERSON>", "browser": "<PERSON><PERSON><PERSON><PERSON><PERSON>č", "checkpoints": "Kontrolní body", "display": "Zobrazení", "notifications": "Oznámení", "contextManagement": "Kontext", "terminal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prompts": "Výzvy", "experimental": "Experimentální", "language": "Jazyk", "about": "O Kilo Code"}, "prompts": {"description": "Nakonfigurujte podpůrné v<PERSON>, kter<PERSON> se používají pro rych<PERSON> akce, jako je vylep<PERSON> v<PERSON>ze<PERSON>, vysvětlení kódu a oprava problémů. Tyto výzvy pomáhají Kilo Code poskytovat lepší pomoc při běžných vývojových úkolech."}, "codeIndex": {"title": "Indexování k<PERSON> b<PERSON>", "description": "Nakonfigurujte nastavení indexování kódové báze pro povolení sémantického vyhledávání vašeho projektu. <0>Dozvědět se více</0>", "statusTitle": "Stav", "enableLabel": "Povolit indexování kódov<PERSON> b<PERSON>", "enableDescription": "Povolit indexování kódu pro lepší vyhledávání a porozumění kontextu", "settingsTitle": "Nastavení indexování", "disabledMessage": "Indexování kódové báze je aktuálně zakázáno. Povolte jej v globálních nastaveních pro konfiguraci možností indexování.", "providerLabel": "Poskytovatel vkládání", "embedderProviderLabel": "Poskytovatel vkládání", "selectProviderPlaceholder": "<PERSON><PERSON><PERSON><PERSON> pos<PERSON>", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Klíč API:", "geminiApiKeyPlaceholder": "Zadejte svůj klíč API Gemini", "openaiCompatibleProvider": "Kompatibilní s OpenAI", "openAiKeyLabel": "Klíč API OpenAI", "openAiKeyPlaceholder": "Zadejte svůj klíč API OpenAI", "openAiCompatibleBaseUrlLabel": "Základní URL", "openAiCompatibleApiKeyLabel": "Klíč API", "openAiCompatibleApiKeyPlaceholder": "Zadejte svůj klíč API", "openAiCompatibleModelDimensionLabel": "Dimenze vkládání:", "modelDimensionLabel": "Dimenze modelu", "openAiCompatibleModelDimensionPlaceholder": "např. 1536", "openAiCompatibleModelDimensionDescription": "Di<PERSON><PERSON> vkládání (velikost výstupu) pro váš model. Zkontrolujte dokumentaci svého poskytovatele pro tuto hodnotu. Běžné hodnoty: 384, 768, 1536, 3072.", "modelLabel": "Model", "modelPlaceholder": "Zadejte název modelu", "selectModel": "Vyberte model", "selectModelPlaceholder": "Vyberte model", "ollamaUrlLabel": "URL Ollama:", "ollamaBaseUrlLabel": "Základní URL Ollama", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON><PERSON><PERSON>:", "qdrantApiKeyLabel": "Klíč API Qdrant", "qdrantApiKeyPlaceholder": "Zadejte svůj klíč API Qdrant (volitelné)", "setupConfigLabel": "Nastavení", "advancedConfigLabel": "Pokročilá konfigurace", "searchMinScoreLabel": "Prahová hodnota skóre vyhledávání", "searchMinScoreDescription": "Minimální skóre podobnosti (0.0-1.0) požadované pro výsledky vyhledávání. Nižší hodnoty vrátí více výsledk<PERSON>, ale mohou být méně <PERSON>í. <PERSON><PERSON>š<PERSON><PERSON> hodnoty vrátí méně, ale <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> výsledky.", "searchMinScoreResetTooltip": "Obnovit na výchozí hodnotu (0.4)", "searchMaxResultsLabel": "Maximální počet výsledků vyhledávání", "searchMaxResultsDescription": "Maximální počet výsledk<PERSON> v<PERSON>, kter<PERSON> se vrátí při dotazování indexu kódové báze. Vyšší hodnoty poskytují více kontextu, ale mohou zahrnovat méně relevantní v<PERSON>.", "resetToDefault": "Obnovit na výchozí", "startIndexingButton": "Spustit indexování", "clearIndexDataButton": "Vymazat data indexu", "unsavedSettingsMessage": "<PERSON><PERSON><PERSON> s<PERSON>štěním procesu indexování prosím uložte nastavení.", "clearDataDialog": {"title": "Jste si jisti?", "description": "<PERSON>to a<PERSON> ne<PERSON> vrátit zpět. Tímto trvale smažete data indexu vaší kódové b<PERSON>.", "cancelButton": "Zrušit", "confirmButton": "Vymazat data"}, "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Nepodařilo se uložit nastavení", "modelDimensions": "({{dimension}} dimenzí)", "saveSuccess": "Nastavení bylo <PERSON>", "saving": "Ukládání...", "saveSettings": "Uložit", "indexingStatuses": {"standby": "Pohotovost", "indexing": "Indexování", "indexed": "Indexováno", "error": "Chyba"}, "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validation": {"qdrantUrlRequired": "URL Qdrant je povinné", "invalidQdrantUrl": "Neplatné URL Qdrant", "invalidOllamaUrl": "Neplatné URL Ollama", "invalidBaseUrl": "Neplatné základní URL", "openaiApiKeyRequired": "Klíč API OpenAI je povinný", "modelSelectionRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> modelu je povinný", "apiKeyRequired": "<PERSON><PERSON><PERSON><PERSON> je povinný", "modelIdRequired": "ID modelu je povinné", "modelDimensionRequired": "Dimenze modelu je povinná", "geminiApiKeyRequired": "Klíč API Gemini je povinný", "ollamaBaseUrlRequired": "Základní URL Ollama je povinné", "baseUrlRequired": "Základní URL je povinné", "modelDimensionMinValue": "Dimenze modelu musí být větší než 0"}}, "autoApprove": {"description": "Povolit Kilo Code automaticky provádět operace bez nutnosti schválení. Povolte tato nastavení pouze v případě, že plně důvěřujete AI a rozumíte souvisejícím bezpečnostním rizikům.", "readOnly": {"label": "Číst", "description": "<PERSON>kud je povoleno, Kilo <PERSON> automaticky zobrazí obsah adresáře a přečte soubory, an<PERSON><PERSON> byste museli klikat na tlačítko Schválit.", "outsideWorkspace": {"label": "<PERSON><PERSON><PERSON><PERSON> soubory mimo pracovní prostor", "description": "Povolit Kilo Code číst soubory mimo aktuální pracovní prostor bez nutnosti schválení."}}, "write": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Automaticky vytvářet a upravovat soubory bez nutnosti schválení", "delayLabel": "Zpožděn<PERSON> po <PERSON>, aby diagnostika mohla dete<PERSON>t potenciální problémy", "outsideWorkspace": {"label": "<PERSON><PERSON><PERSON><PERSON> soubory mimo pracovní prostor", "description": "Povolit Kilo Code vytvářet a upravovat soubory mimo aktuální pracovní prostor bez nutnosti schválení."}, "protected": {"label": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Povolit Kilo Code vytvářet a upravovat chráně<PERSON>é soubory (jako .kilocodeignore a konfigurační soubory .kilocode/) bez nutnosti schválení."}}, "browser": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>č", "description": "Automaticky provádět akce prohlížeče bez nutnosti schválení. Poznámka: Platí pouze v případě, že model podporuje používání počítače"}, "retry": {"label": "Zkusit znovu", "description": "Automaticky opakovat neúspěšné požadavky API, když server vrátí chybovou odpověď", "delayLabel": "Zpoždění před opakováním požadavku"}, "mcp": {"label": "MCP", "description": "Povolit automatické schvalování jednotlivých nástrojů MCP v zobrazení MCP Servers (vyžaduje toto nastavení i individuální zaškrtávací políčko nástroje „Vždy povolit“)"}, "modeSwitch": {"label": "<PERSON><PERSON><PERSON>", "description": "Automaticky přepínat mezi různými režimy bez nutnosti schválení"}, "subtasks": {"label": "Podúkoly", "description": "Povolit vytváření a dokončování podúkolů bez nutnosti schválení"}, "followupQuestions": {"label": "Otáz<PERSON>", "description": "Automaticky vybrat první navrhovanou odpověď pro následné otázky po uplynutí nastaveného časového limitu", "timeoutLabel": "Doba čekání před automatickým výběrem první odpovědi"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Automaticky spouštět povolené příkazy terminálu bez nutnosti schválení", "allowedCommands": "Povolené příkazy pro automatické spuštění", "allowedCommandsDescription": "Předpony příkazů, k<PERSON><PERSON> mohou být automaticky spuštěny, k<PERSON><PERSON> je povoleno „Vždy schvalovat operace spuštění“. Přidejte * pro povolení všech příkazů (používejte s opatrností).", "commandPlaceholder": "Zadejte předponu př<PERSON> (např. 'git ')", "addButton": "<PERSON><PERSON><PERSON><PERSON>"}, "showMenu": {"label": "Zobrazit nabídku automatického schválení v zobrazení chatu", "description": "Pokud je povoleno, nabídka automatického schválení se zobrazí v dolní části zobrazení chatu, což umožní rychlý přístup k nastavení automatického schválení"}, "updateTodoList": {"label": "Todo", "description": "Automaticky aktualizovat seznam úkolů bez nutnosti schválení"}, "apiRequestLimit": {"title": "Maximální počet žádostí", "description": "Automaticky provést tento počet žádostí API předtím, než požádá o schválení k pokračování v úkolu.", "unlimited": "Neomezeně"}}, "providers": {"providerDocumentation": "Dokumentace {{provider}}", "configProfile": "Konfigurační profil", "description": "Uložte si různé konfigurace API pro rychlé přepínání mezi poskytovateli a nastaveními.", "apiProvider": "Poskytovatel API", "model": "Model", "nameEmpty": "Jméno nemůže být prázdné", "nameExists": "Profil s tímto názvem již existuje", "deleteProfile": "<PERSON><PERSON><PERSON><PERSON> profil", "invalidArnFormat": "Neplatný formát ARN. Zkontrolujte prosím výše uvedené příklady.", "enterNewName": "Zadejte nové <PERSON>", "addProfile": "<PERSON><PERSON><PERSON><PERSON> profil", "renameProfile": "Přejmenovat profil", "newProfile": "Nový konfigurační profil", "enterProfileName": "Zadejte název profilu", "createProfile": "Vytvořit profil", "cannotDeleteOnlyProfile": "<PERSON><PERSON><PERSON> s<PERSON>t jediný profil", "searchPlaceholder": "Hledat profily", "searchProviderPlaceholder": "Hledat poskytovatele", "noProviderMatchFound": "Nebyli nalezeni žádní poskytovatelé", "noMatchFound": "Nebyly nalezeny žádné odpovídající profily", "vscodeLmDescription": "API jazykového modelu VS Code vám umožňuje spouštět modely poskytované jinými rozšířeními VS Code (včetně, ale nejen, GitHub Copilot). Nejjedn<PERSON><PERSON><PERSON><PERSON><PERSON>, jak <PERSON>, je nainstalovat rozšíření Copilot a Copilot Chat z VS Code Marketplace.", "awsCustomArnUse": "Zadejte platný Amazon Bedrock ARN pro model, k<PERSON><PERSON> chcete použít. Příklady formátu:", "awsCustomArnDesc": "Ujistěte se, že region v ARN odpovídá výše vybranému regionu AWS.", "openRouterApiKey": "Klíč API OpenRouter", "getOpenRouterApiKey": "Získat klíč API OpenRouter", "apiKeyStorageNotice": "Klíče API jsou bezpečně uloženy v tajném úložišti VSCode", "glamaApiKey": "Klíč API Glama", "getGlamaApiKey": "Získat klíč API Glama", "useCustomBaseUrl": "Použít vlastní základní URL", "useReasoning": "Povolit uvažování", "useHostHeader": "Použít vlastní hlavičku Host", "useLegacyFormat": "Použít starší formát API OpenAI", "customHeaders": "<PERSON><PERSON><PERSON><PERSON>", "headerName": "<PERSON><PERSON><PERSON><PERSON>", "headerValue": "Hodnota hlavičky", "noCustomHeaders": "Nejsou definovány žádné vlastní hlavičky. Kliknutím na tlačítko + přid<PERSON>te jednu.", "requestyApiKey": "Klíč API Requesty", "refreshModels": {"label": "Obnovit modely", "hint": "Znovu otevřete nastavení, abyste vidě<PERSON> modely.", "loading": "Obnovování sez<PERSON>u modelů...", "success": "Seznam modelů byl úspěšně obnoven!", "error": "Nepodařilo se obnovit seznam modelů<PERSON> Z<PERSON> to prosím znovu."}, "getRequestyApiKey": "Získat klíč API Requesty", "openRouterTransformsText": "Komprimovat výzvy a řetězce zpráv na velikost kontextu (<a>Transformace OpenRouter</a>)", "anthropicApiKey": "Klíč API Anthropic", "getAnthropicApiKey": "Získat klíč API Anthropic", "anthropicUseAuthToken": "Předávat klíč API Anthropic jako hlavičku Authorization místo X-Api-Key", "chutesApiKey": "Klíč API Chutes", "getChutesApiKey": "Získat klíč API Chutes", "deepSeekApiKey": "Klíč API DeepSeek", "getDeepSeekApiKey": "Získat klíč API DeepSeek", "geminiApiKey": "Klíč API Gemini", "getGroqApiKey": "Získat klíč API Groq", "groqApiKey": "Klíč API Groq", "getGeminiApiKey": "Získat klíč API Gemini", "openAiApiKey": "Klíč API OpenAI", "apiKey": "Klíč API", "openAiBaseUrl": "Základní URL", "getOpenAiApiKey": "Získat klíč API OpenAI", "mistralApiKey": "Klíč API Mistral", "getMistralApiKey": "Získat klíč API Mistral / Codestral", "codestralBaseUrl": "Základní URL Codestral (volitelné)", "codestralBaseUrlDesc": "Nastavte alternativní URL pro model Codestral.", "xaiApiKey": "Klíč API xAI", "getXaiApiKey": "Získat klíč API xAI", "litellmApiKey": "Klíč API LiteLLM", "litellmBaseUrl": "Základní URL LiteLLM", "awsCredentials": "Přihlašovací údaje AWS", "awsProfile": "Profil AWS", "awsProfileName": "Název profilu AWS", "awsAccessKey": "Klíč přístupu AWS", "awsSecretKey": "Tajný klíč AWS", "awsSessionToken": "Token relace AWS", "awsRegion": "Region AWS", "awsCrossRegion": "Použít meziregionální odvozování", "awsBedrockVpc": {"useCustomVpcEndpoint": "Použít vlastní koncový bod VPC", "vpcEndpointUrlPlaceholder": "Zadejte URL koncového bodu VPC (volitelné)", "examples": "Příklady:"}, "enablePromptCaching": "Povolit ukládání výzev do mezipaměti", "enablePromptCachingTitle": "Povolte ukládání výzev do mezipaměti, abyste zlepšili výkon a snížili náklady na podporované modely.", "cacheUsageNote": "Poznámka: <PERSON><PERSON><PERSON> nevid<PERSON> v<PERSON>žití <PERSON>, zkuste vybrat jiný model a poté znovu vybrat p<PERSON> model.", "vscodeLmModel": "J<PERSON><PERSON><PERSON><PERSON> model", "vscodeLmWarning": "Poznámka: <PERSON><PERSON> se o velmi experimentální integraci a podpora poskytovatele se bude lišit. Pokud se zobrazí chyba, že model ne<PERSON><PERSON>, jedná se o problém na straně poskytovatele.", "googleCloudSetup": {"title": "Chcete-li používat Google Cloud Vertex AI, musíte:", "step1": "1. Vytvořit účet Google Cloud, povolit rozhraní Vertex AI API a povolit požadované modely Claude.", "step2": "2. Nainstalovat rozhraní Google Cloud CLI a nakonfigurovat výchozí přihlašovací údaje aplikace.", "step3": "3. Nebo vytvořit účet služby s přihlašovacími údaji."}, "googleCloudCredentials": "Přihlašovací údaje Google Cloud", "googleCloudKeyFile": "Cesta k souboru klíče Google Cloud", "googleCloudProjectId": "ID projektu Google Cloud", "googleCloudRegion": "Region Google Cloud", "lmStudio": {"baseUrl": "Základní URL (volitelné)", "modelId": "ID modelu", "speculativeDecoding": "Povolit spekulativní dekódování", "draftModelId": "ID modelu konceptu", "draftModelDesc": "Model konceptu musí být ze stejné rodiny modelů, aby spekulat<PERSON><PERSON><PERSON> de<PERSON><PERSON> fungo<PERSON> správně.", "selectDraftModel": "Vybrat model konceptu", "noModelsFound": "Nebyly nalezeny žádné modely konceptu. Ujistěte se, že LM Studio běží s povoleným režimem serveru.", "description": "LM Studio vám umožňuje spouštět modely lokálně na vašem počítači. Pokyny k zahájení práce naleznete v jejich <a>průvodci rychlým startem</a>. Budete také muset spustit funkci <b>lokálního serveru</b> LM Studio, abyste jej mohli používat s tímto rozšířením. <span>Poznámka:</span> Kilo Code používá složité výzvy a nejlépe funguje s modely Claude. Méně schopné modely nemusí fungovat podle očekávání."}, "ollama": {"baseUrl": "Základní URL (volitelné)", "modelId": "ID modelu", "description": "Ollama vám umožňuje spouštět modely lokálně na vašem počítači. Pokyny k zahájení práce naleznete v jejich průvodci rychlým startem.", "warning": "Poznámka: Kilo Code používá složité výzvy a nejlépe funguje s modely Claude. <PERSON><PERSON><PERSON> schopné modely nemusí fungovat podle očekávání."}, "unboundApiKey": "Klíč API Unbound", "getUnboundApiKey": "Získat klíč API Unbound", "unboundRefreshModelsSuccess": "Seznam modelů byl aktualizován! Nyní si můžete vybrat z nejnovějších modelů.", "unboundInvalidApiKey": "Neplatný klíč API. Zkontrolujte prosím svůj klíč API a zkuste to znovu.", "humanRelay": {"description": "Není vyžadován žádný klíč API, ale uživatel musí pomoci zkopírovat a vložit informace do webového chatu AI.", "instructions": "Během používání se zobrazí dialogové okno a aktuální zpráva se automaticky zkopíruje do schránky. Musíte je vložit do webových verzí AI (například ChatGPT nebo Claude), poté zkopírovat odpověď AI zpět do dialogového okna a kliknout na tlačítko potvrdit."}, "openRouter": {"providerRouting": {"title": "Směrován<PERSON> poskytovatele OpenRouter", "description": "OpenRouter směruje požadavky na nejlepší dostupné poskytovatele pro váš model. Ve výchozím nastavení jsou požadavky vyrovnávány zátěží mezi nejlepš<PERSON> poskytovatele, aby se maximalizovala doba provozu. Můžete si však vybrat konkrétního poskytovatele, kterého chcete pro tento model pou<PERSON><PERSON><PERSON>.", "learnMore": "Zjistěte více o směrování poskytovatelů"}}, "cerebras": {"apiKey": "Klíč API Cerebras", "getApiKey": "Získat klíč API Cerebras"}, "customModel": {"capabilities": "Nakonfigurujte schopnosti a ceny pro v<PERSON>š vlastní model kompatibilní s OpenAI. Při zad<PERSON>í schopností modelu buďte opatrní, protože mohou ovlivnit výkon Kilo Code.", "maxTokens": {"label": "Maximální počet výstupních tokenů", "description": "Maximální počet tokenů, <PERSON><PERSON><PERSON> model vygenerovat v odpovědi. (Zadejte -1, aby server mohl nastavit maximální počet tokenů.)"}, "contextWindow": {"label": "Velikost kontextového okna", "description": "<PERSON><PERSON><PERSON><PERSON> počet <PERSON> (vstup + výstup), <PERSON><PERSON><PERSON> model zpracovat."}, "imageSupport": {"label": "Podpora obrázků", "description": "Je tento model scho<PERSON>v<PERSON> a rozumět obrázkům?"}, "computerUse": {"label": "Použití počítače", "description": "Je tento model scho<PERSON> interagovat s prohl<PERSON>em? (<PERSON><PERSON><PERSON> 3.7 Sonnet)."}, "promptCache": {"label": "Ukládání výzev do mezipaměti", "description": "Je tento model scho<PERSON> ukládat výzvy do mezipaměti?"}, "pricing": {"input": {"label": "<PERSON><PERSON> vs<PERSON><PERSON>", "description": "Cena za milion tokenů ve vstupu/výzvě. To ovlivňuje náklady na odeslání kontextu a pokynů do modelu."}, "output": {"label": "Cena výstupu", "description": "Cena za milion tokenů v odpovědi modelu. To ovlivňuje náklady na generovaný obsah a dokončení."}, "cacheReads": {"label": "Cena čtení z mezipaměti", "description": "Cena za milion tokenů za čtení z mezipaměti. Toto je cena <PERSON>á při načtení odpovědi z mezipaměti."}, "cacheWrites": {"label": "Cena zápisu do mezipaměti", "description": "Cena za milion tokenů za zápis do mezipaměti. Toto je cena <PERSON> při prvním uložení výzvy do mezipaměti."}}, "resetDefaults": "Obnovit výchozí"}, "rateLimitSeconds": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Minimální čas mezi požadavky API."}, "reasoningEffort": {"label": "Úsilí modelu při uvažování", "high": "Vysoké", "medium": "Střední", "low": "Nízké"}, "setReasoningLevel": "Povolit úsilí při uvažování", "claudeCode": {"pathLabel": "Cesta ke Claude Code", "description": "Volitelná cesta k vašemu CLI Claude Code. Ve výchozím nastavení 'claude', pokud není nastaveno.", "placeholder": "Výchozí: claude"}, "geminiCli": {"description": "Tento poskytovatel používá OAuth autentizaci z nástroje Gemini CLI a nevyžaduje klíče API.", "oauthPath": "Cesta k OAuth přihlašovacím údajům (volitelné)", "oauthPathDescription": "Cesta k souboru přihlašovacích údajů OAuth. Ponechte prázdné pro použití výchozího umístění (~/.gemini/oauth_creds.json).", "instructions": "Pokud jste se je<PERSON><PERSON><PERSON><PERSON>, spusťte prosím", "instructionsContinued": "ve vašem terminálu nejdříve.", "setupLink": "Pokyny k nastavení Gemini CLI", "requirementsTitle": "Důležité požadavky", "requirement1": "Nejprve musíte nainstalovat nástroj Gemini CLI", "requirement2": "Poté s<PERSON>te gemini ve vašem terminálu a ujistěte se, že se přihlásíte pomocí Google", "requirement3": "Funguje pouze s osobními účty Google (ne s účty Google Workspace)", "requirement4": "Nepoužívá klíče API - ověřování je řešeno přes OAuth", "requirement5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby byl nástroj Gemini CLI nejdříve nainstalován a ověřen", "freeAccess": "Přístup k bezplatné úrovni prostřednictvím OAuth autentizace"}}, "browser": {"enable": {"label": "Povolit nástroj prohlížeče", "description": "<PERSON>kud je povoleno, <PERSON><PERSON> může používat prohlížeč k interakci s webovými stránkami při používání modelů, které podporují používání počítače. <0>Dozvědět se více</0>"}, "viewport": {"label": "Velikost zobrazení", "description": "Vyberte velikost zobrazení pro interakce s prohlížečem. To ovlivňuje, jak se <PERSON>ové stránky zobrazují a jak s nimi interagujete.", "options": {"largeDesktop": "Velký desktop (1280x800)", "smallDesktop": "Malý desktop (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobilní (360x640)"}}, "screenshotQuality": {"label": "Kvalita snímku obrazovky", "description": "Upravte kvalitu snímků obrazovky prohlížeče ve formátu WebP. Vyšší hodnoty poskytují jasnější snímky obrazovky, ale zvyšují spotřeb<PERSON>ů."}, "remote": {"label": "Použít vzdálené připojení <PERSON>če", "description": "Připojte se k prohlížeči Chrome, k<PERSON><PERSON> běž<PERSON> s povoleným vzdáleným laděním (--remote-debugging-port=9222).", "urlPlaceholder": "Vlastní URL (např. http://localhost:9222)", "testButton": "Testovat př<PERSON>í", "testingButton": "Testování...", "instructions": "Zadejte adresu hostitele protokolu DevTools nebo ponechte prázdné pro automatické zjištění místních instancí Chrome. Tlačítko Testovat připojení se pokusí o vlastní URL, pokud je zadáno, nebo o automatické zjištění, pokud je pole prázdné."}}, "checkpoints": {"enable": {"label": "Povolit automatické kontrolní body", "description": "<PERSON>ku<PERSON> je povoleno, Kilo <PERSON>ky vytvoří kontrolní body během provádění <PERSON>, což usnadní kontrolu změn nebo návrat do předchozích stavů. <0>Dozvědět se více</0>"}}, "display": {"taskTimeline": {"label": "Zobrazit časovou osu úkolu", "description": "Zobrazit vizuální časovou osu zpráv úkolu, bare<PERSON><PERSON><PERSON> odlišenou podle typu, což vám umožní rychle zobrazit průběh úkolu a posunout se zpět na konkrétní body v historii úkolu."}}, "notifications": {"sound": {"label": "Povolit zvukové efekty", "description": "<PERSON><PERSON><PERSON> je povoleno, <PERSON><PERSON> bude přehrávat zvukové efekty pro oznámení a události.", "volumeLabel": "Hlasitost"}, "tts": {"label": "Povolit převod textu na řeč", "description": "<PERSON><PERSON>d je povoleno, <PERSON><PERSON> bude nahlas číst své odpovědi pomocí převodu textu na řeč.", "speedLabel": "Rychlost"}}, "contextManagement": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jak<PERSON> informace jsou zahrnuty v kontextovém okně AI, což ovlivňuje využití tokenů a kvalitu odpovědi", "autoCondenseContextPercent": {"label": "Prahová hodnota pro spuštění inteligentního zhušťování kontextu", "description": "<PERSON><PERSON><PERSON> k<PERSON>é okno dosáhne této prahové hodnot<PERSON>, <PERSON><PERSON> j<PERSON> zhušťí."}, "condensingApiConfiguration": {"label": "Konfigurace API pro zhušťování kontextu", "description": "<PERSON><PERSON><PERSON><PERSON>, jakou konfiguraci API použít pro operace zhušťování kontextu. Ponechte nevybrané, chcete-li použít aktuálně aktivní konfiguraci.", "useCurrentConfig": "Výchozí"}, "customCondensingPrompt": {"label": "Vlastní prompt pro z<PERSON>šťování kontextu", "description": "Přizpůsobte systémový prompt používaný pro zhušťování kontextu. Ponechte prázdné, chcete-li použ<PERSON>t výchozí prompt.", "placeholder": "Sem zadejte svůj vlastní prompt pro zhušťování...\n\nMůžete použít stejnou strukturu jako výchozí prompt:\n- Předchozí konverzace\n- Aktuální práce\n- Klíčové technické pojmy\n- Relevantní soubory a kód\n- Řešení problémů\n- Čekající úkoly a dalš<PERSON> kroky", "reset": "Obnovit výchozí", "hint": "Prázdné = použ<PERSON>t výchozí prompt"}, "autoCondenseContext": {"name": "Automaticky spouštět inteligentní zhušťování kontextu", "description": "Pokud je povoleno, <PERSON>lo <PERSON>ky zhušťí kontext, k<PERSON><PERSON> je dosaženo prahové hodnoty. Pokud je z<PERSON>, m<PERSON><PERSON><PERSON> st<PERSON><PERSON> ruč<PERSON>ě spouštět zhušťování kontextu."}, "openTabs": {"label": "<PERSON>it kontextu otevř<PERSON><PERSON>ch karet", "description": "Maximální počet otevřený<PERSON> karet VSCode, kter<PERSON> se mají zahrnout do kontextu. Vyšší hodnoty poskytují více kontextu, ale zvyšují využití <PERSON>ů."}, "workspaceFiles": {"label": "Limit kontextu souborů pracovního prostoru", "description": "Maximální po<PERSON> so<PERSON>, kter<PERSON> se mají zahrnout do podrobností aktuálního pracovního adresáře. Vyšší hodnoty poskytují více kontextu, ale zvyšují využití <PERSON>ů."}, "rooignore": {"label": "Zobrazit soubory ignorované .kilocodeignore v seznamech a vyhledáváních", "description": "Pokud je povoleno, soubory odpovídající vzorům v .kilocodeignore se zobrazí v seznamech se symbolem zámku. Pokud je z<PERSON>, tyto soubory budou zcela skryty ze seznamů souborů a vyhledávání."}, "maxConcurrentFileReads": {"label": "<PERSON>it so<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "description": "Maximální počet soub<PERSON>, k<PERSON><PERSON> m<PERSON> n<PERSON>troj 'read_file' zpracovávat souběžně. Vyšší hodnoty mohou urychlit čtení více malých soubor<PERSON>, ale zvyšují využití pam<PERSON>ti."}, "maxReadFile": {"label": "Prahová hodnota automatického zkrácení při čtení souboru", "description": "Kilo Code načte tento počet řád<PERSON>, <PERSON><PERSON><PERSON> <PERSON> vynech<PERSON> počáteční/konco<PERSON><PERSON> hodnoty. Pokud je toto číslo menší než celkový počet řádků souboru, Kilo Code vygeneruje index čísel řádků definic kódu. Speciální případy: -1 instruuje Kilo Code, aby přečetl celý soubor (bez indexování), a 0 ho instruuje, aby nenačítal žádné řádky a poskytoval pouze indexy řádků pro minimální kontext. Nižší hodnoty minimalizují počáteční využití kontextu, což umožňuje přesné následné čtení rozsahu řádků. Explicitní požadavky na začátek/konec nejsou tímto nastavením omezeny.", "lines": "<PERSON><PERSON><PERSON><PERSON>", "always_full_read": "<PERSON><PERSON><PERSON> celý soubor"}, "condensingThreshold": {"label": "Prahová hodnota spouštění zhušťování", "selectProfile": "Nakonfigurovat prahovou hodnotu pro profil", "defaultProfile": "Globální výchozí (všechny profily)", "defaultDescription": "<PERSON><PERSON><PERSON> konte<PERSON>t <PERSON>hne tohoto procenta, bude automaticky zhuštěn pro všechny profily, pokud nemají vlastní nastavení", "profileDescription": "Vlastní prahová hodnota pouze pro tento profil (přepisuje globální výchozí)", "inheritDescription": "Tento profil děd<PERSON> glo<PERSON>ální výchozí prahovou hodnotu ({{threshold}}%)", "usesGlobal": "(používá globální {{threshold}}%)"}}, "terminal": {"basic": {"label": "Nastavení terminálu: Základní", "description": "Základní nastavení termin<PERSON>"}, "advanced": {"label": "Nastavení terminálu: Pokročilé", "description": "Následující možnosti mohou vyžadovat restart terminálu, aby se nastavení projevilo."}, "outputLineLimit": {"label": "Limit výst<PERSON><PERSON> term<PERSON>", "description": "Maximální počet <PERSON>, které se mají zahrnout do výstupu terminálu při provádění příkazů. Při překročení budou řádky odstraněny ze středu, č<PERSON><PERSON>ž se ušetří tokeny. <0>Dozvědět se více</0>"}, "shellIntegrationTimeout": {"label": "Časový limit integrace shellu terminálu", "description": "Maximální doba čekání na inicializaci integrace shellu před provedením příkazů. Pro uživatele s dlouhou dobou spouštění shellu může být nutné tuto hodnotu zvýšit, pokud se v terminálu zobrazí chyby „Integrace shellu není k dispozici“. <0>Dozvědět se více</0>"}, "shellIntegrationDisabled": {"label": "Zakázat integraci shellu terminálu", "description": "Povolte tuto mož<PERSON>, pokud příkazy terminálu nefungují správně nebo se zobrazují chyby „Integrace shellu není k dispozici“. Tímto se použije jednodušší metoda spouštění přík<PERSON>ů, která obejde některé pokročilé funkce terminálu. <0>Dozvědět se více</0>"}, "commandDelay": {"label": "Zpoždění příkazu terminálu", "description": "Zpoždění v milisekundách, kter<PERSON> se přidá po provedení příkazu. Výchozí nastavení 0 zpoždění zcela zakáže. To může pomoci zajistit, že výstup příkazu bude plně zachycen v terminálech s problémy s časováním. Ve většině terminálů se to implementuje nastavením `PROMPT_COMMAND='sleep N'` a Powershell připojí `start-sleep` na konec každého příkazu. <PERSON><PERSON><PERSON><PERSON><PERSON> to bylo řešení chyby VSCode#237208 a nemusí být již potřeba. <0>Dozvědět se více</0>"}, "compressProgressBar": {"label": "Komprimovat výstup průběhové lišty", "description": "Pokud je povoleno, zpracovává výstup terminálu s návratem vozíku (\\r), aby se simulovalo, jak by sku<PERSON><PERSON><PERSON><PERSON> terminál zobrazoval obsah. Tím se odstraní mezistavy průběhové lišty a zachová se pouze konečný stav, což šetří kontextový prostor pro relevantnější informace. <0>Dozvědět se více</0>"}, "powershellCounter": {"label": "Povolit řešení problému s počítadlem PowerShell", "description": "Pokud je povoleno, přid<PERSON> se k příkazům PowerShell počítadlo, aby se zaj<PERSON><PERSON> správ<PERSON><PERSON> proved<PERSON>í příkazu. To pomáhá s termin<PERSON>ly PowerShell, kter<PERSON> mohou mít problémy se zachycením výstupu příkazu. <0>Dozvědět se více</0>"}, "zshClearEolMark": {"label": "Vymazat značku EOL ZSH", "description": "Pokud je povoleno, vymaže značku konce řádku ZSH nastavením PROMPT_EOL_MARK=''. Tím se předejde problémům s interpretací výstupu příkazu, k<PERSON><PERSON> výstup končí speciálními znaky, jako je '%'. <0>Dozvědět se více</0>"}, "zshOhMy": {"label": "Povolit integraci Oh My Zsh", "description": "Pokud je povoleno, nastaví ITERM_SHELL_INTEGRATION_INSTALLED=Yes pro povolení funkcí integrace shellu Oh My Zsh. Použití tohoto nastavení může vyžadovat restartování IDE. <0>Dozvědět se více</0>"}, "zshP10k": {"label": "Povolit integraci Powerlevel10k", "description": "Pokud je povoleno, nastaví POWERLEVEL9K_TERM_SHELL_INTEGRATION=true pro povolení funkcí integrace shellu Powerlevel10k. <0>Dozvědět se více</0>"}, "zdotdir": {"label": "Povolit zpracování ZDOTDIR", "description": "Pokud je povoleno, vyt<PERSON><PERSON><PERSON> dočasný adresář pro ZDOTDIR pro správné zpracování integrace shellu zsh. Tím se zajistí správná funkce integrace shellu VSCode s zsh při zachování vaší konfigurace zsh. <0>Dozvědět se více</0>"}, "inheritEnv": {"label": "<PERSON><PERSON><PERSON> promě<PERSON><PERSON> pro<PERSON>", "description": "Pokud je povoleno, termin<PERSON>l bude dědit proměnné prostředí z rodičovského procesu VSCode, jako jsou nastavení integrace shellu definovaná v profilu uživatele. Tím se přímo přepíná globální nastavení VSCode `terminal.integrated.inheritEnv`. <0>Dozvědět se více</0>"}}, "advanced": {"diff": {"label": "Povolit úpravy prostřednictvím diffů", "description": "<PERSON>kud je povoleno, <PERSON><PERSON> bude moci rychleji upravovat soubory a automaticky odmítne zkrácené zápisy celých souborů. Nejlépe funguje s nejnovějším modelem Claude 4 Sonnet.", "strategy": {"label": "Strategie diff", "options": {"standard": "Standardní (jeden blok)", "multiBlock": "Experimentální: diff s více bloky", "unified": "Experimentální: s<PERSON><PERSON><PERSON><PERSON><PERSON> diff"}, "descriptions": {"standard": "Standardní strategie diff aplikuje změny na jeden blok kódu najednou.", "unified": "Sjednocená strategie diff používá více přístupů k aplikaci diffů a vybírá ten nejlepší.", "multiBlock": "Strategie diff s více bloky umožňuje aktualizovat více bloků kódu v souboru v jednom požadavku."}}, "matchPrecision": {"label": "Přesnost shody", "description": "<PERSON>to posuvník řídí, jak př<PERSON>ně musí sekce kódu odpovídat při aplikaci diffů. <PERSON>ž<PERSON><PERSON> hodnoty umožňují flexibilněj<PERSON><PERSON> shodu, ale zvyšují riziko nesprávných nahrazení. Používejte hodnoty pod 100% s extrémní opatrností."}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Použ<PERSON>t experiment<PERSON>lní sjednocenou strategii diff", "description": "Povolit experimentální sjednocenou strategii diff. Tato strategie může snížit počet opakování způsobených chybami modelu, ale může způsobit neočekávané chování nebo nesprávné úpra<PERSON>. Povolte pouze pokud rozumíte rizikům a jste ochotni pečlivě zkontrolovat všechny změny."}, "SEARCH_AND_REPLACE": {"name": "Použít experimentální nástroj hledání a nahrazování", "description": "Povolit experimentální nástroj hledání a nahrazování, který umožňuje Kilo Code nahradit více výskytů hledaného výrazu v jednom požadavku."}, "INSERT_BLOCK": {"name": "Použít experimentální nástroj vkládání obsahu", "description": "Povolit experimentální nástroj vkl<PERSON><PERSON><PERSON><PERSON> obsahu, k<PERSON><PERSON> umožňuje Kilo Code vkládat obsah na konkrétní čísla řádků bez nutnosti vytvářet diff."}, "POWER_STEERING": {"name": "Použít experimentální režim \"posilovače řízení\"", "description": "<PERSON>kud je povoleno, <PERSON><PERSON> bude modelu častěji připomínat podrobnosti jeho aktuální definice režimu. To povede k silnějšímu dodržování definic rolí a vlastních instrukcí, ale použije více tokenů na zprávu."}, "AUTOCOMPLETE": {"name": "Použít experimentální funkci \"automatického doplňování\"", "description": "<PERSON><PERSON><PERSON> je povoleno, Kilo <PERSON> bude poskytovat návrhy kódu přímo při psaní. Vyžaduje Kilo Code API Provider."}, "CONCURRENT_FILE_READS": {"name": "Povolit souběžné čtení souborů", "description": "Pokud je povoleno, Kilo Code může číst více souborů v jednom požadavku. Pokud je z<PERSON>, Kilo Code musí číst soubory jeden po druhém. Zakázání může pomoci při práci s méně schopnými modely nebo když chcete mít větší kontrolu nad přístupem k souborům."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Použít experimentální nástroj diff s více bloky", "description": "Pokud je povoleno, <PERSON><PERSON> bude používat nástroj diff s více bloky. To se pokusí aktualizovat více bloků kódu v souboru v jednom požadavku."}, "MARKETPLACE": {"name": "Povolit Marketplace", "description": "Pokud je povoleno, můžete instalovat MCP a vlastní režimy z Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Povolit souběžné úpravy souborů", "description": "Pokud je povoleno, Kilo Code může upravovat více souborů v jednom požadavku. Pokud je z<PERSON>, Kilo Code musí upravovat soubory jeden po druhém. Zakázání může pomoci při práci s méně schopnými modely nebo když chcete mít větší kontrolu nad úpravami souborů."}}, "promptCaching": {"label": "Zakázat ukládání výzev do mezipaměti", "description": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON><PERSON>, Kilo Code nebude používat ukládání výzev do mezipaměti pro tento model."}, "temperature": {"useCustom": "Použít vlastní teplotu", "description": "Řídí náhodnost v odpovědích modelu.", "rangeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnoty činí výstup náhodnějším, ni<PERSON><PERSON><PERSON> hodnoty jej činí determinističtějším."}, "modelInfo": {"supportsImages": "Podporuje obrázky", "noImages": "Nepodporuje obrázky", "supportsComputerUse": "Podporuje používání počítače", "noComputerUse": "Nepodporuje používání počítače", "supportsPromptCache": "Podporuje ukládání výzev do mezipaměti", "noPromptCache": "Nepodporuje ukládání výzev do mezipaměti", "maxOutput": "Maximální výstup", "inputPrice": "<PERSON><PERSON> vs<PERSON><PERSON>", "outputPrice": "Cena výstupu", "cacheReadsPrice": "Cena čtení z mezipaměti", "cacheWritesPrice": "Cena zápisu do mezipaměti", "enableStreaming": "Povolit streamování", "enableR1Format": "Povolit parametry modelu R1", "enableR1FormatTips": "<PERSON><PERSON><PERSON> b<PERSON>t povoleno při p<PERSON>ž<PERSON>vání modelů R1, jako je <PERSON>, aby se p<PERSON><PERSON><PERSON><PERSON> ch<PERSON> 400", "useAzure": "Použít Azure", "azureApiVersion": "Nastavit verzi Azure API", "gemini": {"freeRequests": "* Zdarma až {{count}} požadavků za minutu. Poté závisí fakturace na velikosti výzvy.", "pricingDetails": "Další informace najdete v podrobnostech o cenách.", "billingEstimate": "* Fakturace je odhad - přesná cena závisí na velikosti výzvy."}}, "modelPicker": {"automaticFetch": "Rozšíření automaticky načítá nejnovější seznam modelů dostupných na <serviceLink>{{serviceName}}</serviceLink>. Pokud si nejste jisti, k<PERSON><PERSON> model vybrat, Kilo Code funguje nejlépe s <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Můžete také zkusit vyhledat \"free\" pro aktuálně dostupné bezplatné možnosti.", "label": "Model", "searchPlaceholder": "Hledat", "noMatchFound": "Nebyla nalezena žádná shoda", "useCustomModel": "<PERSON><PERSON><PERSON><PERSON>t vlastní: {{modelId}}"}, "footer": {"feedback": "Pokud máte nějaké dotazy nebo zpětnou vazbu, nev<PERSON>hejte otevřít problém na <githubLink>github.com/Kilo-Org/kilocode</githubLink> nebo se připojte k <redditLink>reddit.com/r/kilocode</redditLink> nebo <discordLink>kilocode.ai/discord</discordLink>.", "support": "Pro finanční dotazy kontaktujte prosím zákaznickou podporu na <supportLink>https://kilocode.ai/support</supportLink>", "telemetry": {"label": "Povolit hlášení chyb a používání", "description": "Pomozte vylepšit Kilo Code odesíláním dat o používání a hlášení chyb. <PERSON><PERSON> se neodesílá žádný kód, výzvy ani osobní informace. Další podrobnosti najdete v našich zásadách ochrany osobních údajů."}, "settings": {"import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "export": "Exportovat", "reset": "<PERSON><PERSON><PERSON><PERSON>"}}, "thinkingBudget": {"maxTokens": "Maximální poč<PERSON>", "maxThinkingTokens": "Maximální počet tokenů pro přemýšlení"}, "validation": {"apiKey": "Musíte zadat platný klíč API.", "awsRegion": "Musíte vybrat region pro použití s Amazon Bedrock.", "googleCloud": "Musíte zadat platné ID projektu Google Cloud a region.", "modelId": "Musíte zadat platné ID modelu.", "modelSelector": "Musíte zadat platný selektor modelu.", "openAi": "Musíte zadat platnou základní URL, klíč API a ID modelu.", "arn": {"invalidFormat": "Neplatný formát ARN. Zkontrolujte prosím požadavky na formát.", "regionMismatch": "Upozornění: Region ve vašem ARN ({{arnRegion}}) neodpovídá vybranému regionu ({{region}}). To může způsobit problémy s přístupem. Poskytovatel použije region z ARN."}, "modelAvailability": "ID modelu ({{modelId}}), <PERSON><PERSON><PERSON>, nen<PERSON> k dispozici. Vyberte prosím jiný model.", "providerNotAllowed": "Poskytovatel '{{provider}}' není povolen vaší organizací", "modelNotAllowed": "Model '{{model}}' není povolen pro poskytovatele '{{provider}}' vaší organizací", "profileInvalid": "Tento profil obs<PERSON><PERSON> poskytovatele nebo model, k<PERSON><PERSON> není povolen vaší organizací"}, "placeholders": {"apiKey": "Zadejte klíč API...", "profileName": "Zadejte název profilu", "accessKey": "Zadejte přístupový klíč...", "secretKey": "Zadejte tajný klíč...", "sessionToken": "<PERSON><PERSON><PERSON><PERSON> token relace...", "credentialsJson": "Zadejte JSON přihlašovacích údajů...", "keyFilePath": "Zadejte cestu k souboru klíče...", "projectId": "Zadejte ID projektu...", "customArn": "Zadejte ARN (např. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Zadejte základní URL...", "modelId": {"lmStudio": "např. meta-llama-3.1-8b-instruct", "lmStudioDraft": "např. lmstudio-community/llama-3.2-1b-instruct", "ollama": "např. llama3.1"}, "numbers": {"maxTokens": "např. 4096", "contextWindow": "např. 128000", "inputPrice": "např. 0.0001", "outputPrice": "např. 0.0002", "cacheWritePrice": "např. 0.00005"}}, "defaults": {"ollamaUrl": "Výchozí: http://localhost:11434", "lmStudioUrl": "Výchozí: http://localhost:1234", "geminiUrl": "Výchozí: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Vlastní ARN", "useCustomArn": "Použít vlastní ARN..."}, "includeMaxOutputTokens": "Zahrnout maximální počet výstupních tokenů", "includeMaxOutputTokensDescription": "Odeslat parametr maximálního počtu výstupních tokenů v požadavcích API. Někteří poskytovatelé to nemusí podporovat."}