{"common": {"save": "Αποθήκευση", "done": "Τέλος", "cancel": "Ακύρωση", "reset": "Επαναφορά", "select": "Επιλογή", "add": "Προσθήκη Κεφαλίδας", "remove": "Αφαίρεση"}, "header": {"title": "Ρυθμίσεις", "saveButtonTooltip": "Αποθήκευση αλλαγών", "nothingChangedTooltip": "Δεν άλλαξε τίποタ", "doneButtonTooltip": "Απόρριψη μη αποθηκευμένων αλλαγών και κλείσιμο του πίνακα ρυθμίσεων"}, "unsavedChangesDialog": {"title": "Μη αποθηκευμένες αλλαγές", "description": "Θέλετε να απορρίψετε τις αλλαγές και να συνεχίσετε;", "cancelButton": "Ακύρωση", "discardButton": "Απόρριψη αλλαγών"}, "sections": {"providers": "Πάροχ<PERSON>ι", "autoApprove": "Αυτόματη Έγκριση", "browser": "Περιηγητής", "checkpoints": "Σημεία Ελέγχου", "display": "Οθόνη", "notifications": "Ειδοποιήσεις", "contextManagement": "Πλαίσιο", "terminal": "Τερματικό", "prompts": "Prompts", "experimental": "Πειραματικό", "language": "Γλώσσα", "about": "Σχετικά με το Kilo Code"}, "prompts": {"description": "Διαμορφώστε τα support prompts που χρησιμοποιούνται για γρήγορες ενέργειες όπως η βελτίωση των prompts, η εξήγηση κώδικα και η διόρθωση προβλημάτων. Αυτά τα prompts βοηθούν το Kilo Code να παρέχει καλύτερη βοήθεια για κοινές εργασίες ανάπτυξης."}, "codeIndex": {"title": "Ευρετηρίαση Codebase", "description": "Διαμορφώστε τις ρυθμίσεις ευρετηρίασης codebase για να ενεργοποιήσετε τη σημασιολογική αναζήτηση του έργου σας. <0>Μάθετε περισσότερα</0>", "statusTitle": "Κατάσταση", "enableLabel": "Ενεργοποίηση Ευρετηρίασης Codebase", "enableDescription": "Ενεργοποίηση ευρετηρία<PERSON>ης κώδικα για βελτιωμένη αναζήτηση και κατανόηση πλαισίου", "settingsTitle": "Ρυθμίσεις Ευρετηρίασης", "disabledMessage": "Η ευρετηρίαση codebase είναι προς το παρόν απενεργοποιημένη. Ενεργοποιήστε την στις καθολικές ρυθμίσεις για να διαμορφώσετε τις επιλογές ευρετηρίασης.", "providerLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Embeddings", "embedderProviderLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectProviderPlaceholder": "Επιλογή παρόχου", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Κλειδί API:", "geminiApiKeyPlaceholder": "Εισαγάγετε το κλειδί API Gemini", "openaiCompatibleProvider": "Συμβατό με OpenAI", "openAiKeyLabel": "Κλειδί API OpenAI", "openAiKeyPlaceholder": "Εισαγάγετε το κλειδί API OpenAI", "openaiCompatibleBaseUrlLabel": "Βασικό URL:", "openAiCompatibleBaseUrlLabel": "Βασικό URL", "openaiCompatibleApiKeyLabel": "Κλειδί API:", "openAiCompatibleApiKeyLabel": "Κλειδί API", "openAiCompatibleApiKeyPlaceholder": "Εισαγάγετε το κλειδί API", "openaiCompatibleModelDimensionLabel": "Διάσταση Embedding:", "openAiCompatibleModelDimensionLabel": "Διάσταση Embedding:", "modelDimensionLabel": "Διάστα<PERSON>η Μοντέλου", "openaiCompatibleModelDimensionPlaceholder": "π.χ., 1536", "openAiCompatibleModelDimensionPlaceholder": "π.χ., 1536", "openaiCompatibleModelDimensionDescription": "Η διάσταση embedding (μέγ<PERSON><PERSON><PERSON> εξόδου) για το μοντέλο σας. Ελέγξτε την τεκμηρίωση του παρόχου σας για αυτήν την τιμή. Κοινές τιμές: 384, 768, 1536, 3072.", "openAiCompatibleModelDimensionDescription": "Η διάσταση embedding (μέγ<PERSON><PERSON><PERSON> εξόδου) για το μοντέλο σας. Ελέγξτε την τεκμηρίωση του παρόχου σας για αυτήν την τιμή. Κοινές τιμές: 384, 768, 1536, 3072.", "modelLabel": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelPlaceholder": "Εισαγάγ<PERSON>τε όνομα μοντέλου", "selectModel": "Επιλέξτε μοντέλο", "selectModelPlaceholder": "Επιλογή μοντέλου", "ollamaUrlLabel": "URL Ollama:", "ollamaBaseUrlLabel": "Βασικό URL Ollama", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "Κλειδί Qdrant:", "qdrantApiKeyLabel": "Κλειδί API Qdrant", "qdrantApiKeyPlaceholder": "Εισαγάγετε το κλειδ<PERSON>nt (προαιρετικό)", "setupConfigLabel": "Εγκατάσταση", "advancedConfigLabel": "Προηγμένες Ρυθμίσεις", "searchMinScoreLabel": "Όριο Βαθμολογίας Αναζήτησης", "searchMinScoreDescription": "Ελάχιστη βαθμολογία ομοιότητας (0.0-1.0) που απαιτείται για τα αποτελέσματα αναζήτησης. Οι χαμηλότερες τιμές επιστρέφουν περισσότερα αποτελέσματα αλλά μπορεί να είναι λιγότερο σχετικά. Οι υψηλότερες τιμές επιστρέφουν λιγότερα αλλά πιο σχετικά αποτελέσματα.", "searchMinScoreResetTooltip": "Επαν<PERSON><PERSON><PERSON><PERSON><PERSON> στην προεπιλεγμένη τιμή (0.4)", "searchMaxResultsLabel": "Μέγιστα Αποτελέσματα Αναζήτησης", "searchMaxResultsDescription": "Μέγιστος αριθμός αποτελεσμάτων αναζήτησης που θα επιστραφούν κατά την αναζήτηση στο ευρετήριο codebase. Οι υψηλότερες τιμές παρέχουν περισσότερο περιεχόμενο αλλά μπορεί να περιλαμβάνουν λιγότερο σχετικά αποτελέσματα.", "resetToDefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλογή", "startIndexingButton": "Έναρξη Ευρετηρίασης", "clearIndexDataButton": "Εκκαθάριση Δεδομένων Ευρετηρίου", "unsavedSettingsMessage": "Παρα<PERSON><PERSON><PERSON><PERSON> αποθηκεύστε τις ρυθμίσεις σας πριν ξεκινήσετε τη διαδικασία ευρετηρίασης.", "clearDataDialog": {"title": "Είστε σίγουροι;", "description": "Αυτή η ενέργεια δεν μπορεί να αναιρεθεί. Θα διαγράψει οριστικά τα δεδομένα του ευρετηρίου codebase σας.", "cancelButton": "Ακύρωση", "confirmButton": "Εκκαθάριση Δεδομένων"}, "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Αποτυχία αποθήκευσης ρυθμίσεων", "modelDimensions": "({{dimension}} διαστάσεις)", "saveSuccess": "Οι ρυθμίσεις αποθηκεύτηκαν με επιτυχία", "saving": "Αποθήκευση...", "saveSettings": "Αποθήκευση", "indexingStatuses": {"standby": "Αναμονή", "indexing": "Ευρετηρίαση", "indexed": "Ευρετηριασμένο", "error": "Σφάλμα"}, "close": "Κλείσιμο", "validation": {"qdrantUrlRequired": "Απαιτείται URL Qdrant", "invalidQdrantUrl": "Μη έγκυρο URL Qdrant", "invalidOllamaUrl": "Μη έγκυρο URL Ollama", "invalidBaseUrl": "Μη έγκυρο βασικό URL", "openaiApiKeyRequired": "Απαιτείται κλειδί API OpenAI", "modelSelectionRequired": "Απαιτείτ<PERSON>ι επιλογή μοντέλου", "apiKeyRequired": "Απαιτείται κλειδί API", "modelIdRequired": "Απαιτε<PERSON><PERSON><PERSON><PERSON> αναγνωριστικό μοντέλου", "modelDimensionRequired": "Απαιτείτ<PERSON>ι διάσταση μοντέλου", "geminiApiKeyRequired": "Απαιτείται κλειδί API Gemini", "ollamaBaseUrlRequired": "Απαιτείται βασικό URL Ollama", "baseUrlRequired": "Απαιτείτα<PERSON> βασικό URL", "modelDimensionMinValue": "Η διάσταση μοντέλου πρέπει να είναι μεγαλύτερη από 0"}}, "autoApprove": {"description": "Επιτρέψτε στο Kilo Code να εκτελεί αυτόματα λειτουργίες χωρίς να απαιτείται έγκριση. Ενεργοποιήστε αυτές τις ρυθμίσεις μόνο εάν εμπιστεύεστε πλήρως την AI και κατανοείτε τους σχετικούς κινδύνους ασφαλείας.", "readOnly": {"label": "Ανάγνωση", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code θα προβάλλει αυτόματα τα περιεχόμενα του καταλόγου και θα διαβάζει αρχεία χωρίς να χρειάζεται να κάνετε κλικ στο κουμπί Έγκριση.", "outsideWorkspace": {"label": "Συμπερίληψη αρχείων εκτός χώρου εργασίας", "description": "Επιτρέψτε στο Kilo Code να διαβάζει αρχεία εκτός του τρέχοντος χώρου εργασίας χωρίς να απαιτείται έγκριση."}}, "write": {"label": "Εγγραφή", "description": "Αυτόματη δημιουργία και επεξεργασία αρχείων χωρίς να απαιτείται έγκριση", "delayLabel": "Καθυστέρηση μετά τις εγγραφές για να επιτραπεί στα διαγνωστικά να εντοπίσουν πιθανά προβλήματα", "outsideWorkspace": {"label": "Συμπερίληψη αρχείων εκτός χώρου εργασίας", "description": "Επιτρέψτε στο Kilo Code να δημιουργεί και να επεξεργάζεται αρχεία εκτός του τρέχοντος χώρου εργασίας χωρίς να απαιτείται έγκριση."}, "protected": {"label": "Συμπερίληψη προστατευμένων αρχείων", "description": "Επιτρέψτε στο Kilo Code να δημιουργεί και να επεξεργάζεται προστατευμένα αρχεία (όπως .kilocodeignore και αρχεία διαμόρφωσης .kilocode/) χωρίς να απαιτείται έγκριση."}}, "browser": {"label": "Περιηγητής", "description": "Αυτόματη εκτέλεση ενεργειών προγράμματος περιήγησης χωρίς να απαιτείται έγκριση. Σημείωση: Ισχύ<PERSON>ι μόνο όταν το μοντέλο υποστηρίζει τη χρήση υπολογιστή"}, "retry": {"label": "Επανάληψη", "description": "Αυτόματη επανάληψη αποτυχημένων αιτημάτων API όταν ο διακομιστής επιστρέφει απάντηση σφάλματος", "delayLabel": "Καθυστέρηση πριν την επανάληψη του αιτήματος"}, "mcp": {"label": "MCP", "description": "Ενεργοποίηση αυτόματης έγκρισης μεμονωμένων εργαλείων MCP στην προβολή MCP Servers (απαιτείται τόσο αυτή η ρύθμιση όσο και το ατομικό πλαίσιο ελέγχου \"Να επιτρέπεται πάντα\" του εργαλείου)"}, "modeSwitch": {"label": "Λειτουργία", "description": "Αυτόματη εναλλαγή μεταξύ διαφορετικών λειτουργιών χωρίς να απαιτείται έγκριση"}, "subtasks": {"label": "Δευτερεύοντα καθήκοντα", "description": "Να επιτρέπεται η δημιουργία και η ολοκλήρωση δευτερευόντων εργασιών χωρίς να απαιτείται έγκριση"}, "followupQuestions": {"label": "Ερώτηση", "description": "Αυτόματη επιλογή της πρώτης προτεινόμενης απάντησης για ερωτήσεις παρακολούθησης μετά το διαμορφωμένο χρονικό όριο", "timeoutLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> αναμονής πριν την αυτόματη επιλογή της πρώτης απάντησης"}, "execute": {"label": "Εκτέλεση", "description": "Αυτόματη εκτέλεση επιτρεπόμενων εντολών τερματικού χωρίς να απαιτείται έγκριση", "allowedCommands": "Επιτρεπόμενες εντολές αυτόματης εκτέλεσης", "allowedCommandsDescription": "Τα προθέματα εντολών που μπορούν να εκτελεστούν αυτόματα όταν είναι ενεργοποιημένη η επιλογή \"Να εγκρίνονται πάντα οι λειτουργίες εκτέλεσης\". Προσθέστε * για να επιτρέπονται όλες οι εντολές (χρησιμοποιήστε με προσοχή).", "commandPlaceholder": "Εισαγάγετε πρόθεμα εντολής (π.χ. 'git ')", "addButton": "Προσθήκη"}, "showMenu": {"label": "Εμφάνιση μενού αυτόματης έγκρισης στην προβολή συνομιλίας", "description": "Όταν είναι ενεργοποιημένο, το μενού αυτόματης έγκρισης θα εμφανίζεται στο κάτω μέρος της προβολής συνομιλίας, επιτρέποντας τη γρήγορη πρόσβαση στις ρυθμίσεις αυτόματης έγκρισης"}, "updateTodoList": {"label": "Todo", "description": "Αυτόματη ενημέρωση της λίστας εργασιών χωρίς να απαιτείται έγκριση"}, "apiRequestLimit": {"title": "Μέγιστος αριθμός αιτήσεων", "description": "Αυτόματη υποβολή αυτού του αριθμού αιτήσεων API πριν ζητηθεί έγκριση για να συνεχιστεί η εργασία.", "unlimited": "Απεριόριστα"}}, "providers": {"providerDocumentation": "Τεκμηρίωση {{provider}}", "configProfile": "Προφίλ διαμόρφωσης", "description": "Αποθηκεύστε διαφορετικές διαμορφώσεις API για γρήγορη εναλλαγή μεταξύ παρόχων και ρυθμίσεων.", "apiProvider": "Π<PERSON><PERSON><PERSON><PERSON><PERSON> API", "model": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "nameEmpty": "Το όνομα δεν μπορεί να είναι κενό", "nameExists": "Ένα προφίλ με αυτό το όνομα υπάρχει ήδη", "deleteProfile": "Διαγραφή προφίλ", "invalidArnFormat": "Μη έγκυρη μορφή ARN. Ελέγξτε τα παραπάνω παραδείγματα.", "enterNewName": "Εισαγάγετε νέο όνομα", "addProfile": "Προσθήκη προφίλ", "renameProfile": "Μετονομασία προφίλ", "newProfile": "Νέο προφίλ διαμόρφωσης", "enterProfileName": "Εισαγάγετε όνομα προφίλ", "createProfile": "Δημιουργία προφίλ", "cannotDeleteOnlyProfile": "Δεν είναι δυνατή η διαγραφή του μοναδικού προφίλ", "searchPlaceholder": "Αναζήτηση προφίλ", "searchProviderPlaceholder": "Αναζήτηση παρόχων", "noProviderMatchFound": "Δεν βρέθηκαν πάροχοι", "noMatchFound": "Δεν βρέθηκαν προφίλ που ταιριάζουν", "vscodeLmDescription": "Το VS Code Language Model API σάς επιτρέπει να εκτελείτε μοντέλα που παρέχονται από άλλες επεκτάσεις του VS Code (συμπεριλαμβαν<PERSON><PERSON>νων, ενδ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, του GitHub Copilot). Ο ευκολότερος τρόπος για να ξεκινήσετε είναι να εγκαταστήσετε τις επεκτάσεις Copilot και Copilot Chat από το VS Code Marketplace.", "awsCustomArnUse": "Εισαγάγετε ένα έγκυρο Amazon Bedrock ARN για το μοντέλο που θέλετε να χρησιμοποιήσετε. Παραδείγματα μορφής:", "awsCustomArnDesc": "Βεβαιωθείτε ότι η περιοχή στο ARN ταιριάζει με την επιλεγμένη περιοχή AWS παραπάνω.", "openRouterApiKey": "Κλειδί API OpenRouter", "getOpenRouterApiKey": "Λήψη κλειδιού API OpenRouter", "apiKeyStorageNotice": "Τα κλειδιά API αποθηκεύονται με ασφάλεια στο Secret Storage του VSCode", "glamaApiKey": "Κλειδί API Glama", "getGlamaApiKey": "Λήψη κλειδιού API Glama", "useCustomBaseUrl": "Χρήση προσαρμοσμένου βασικού URL", "useReasoning": "Ενεργοποίηση συλλογισμού", "useHostHeader": "Χρήση προσαρμοσμένης κεφαλίδας Host", "useLegacyFormat": "Χρήση παλαιού μορφότυπου API OpenAI", "customHeaders": "Προσαρμοσμένες κεφαλίδες", "headerName": "Όνομα κεφαλίδας", "headerValue": "Τιμή κεφαλίδας", "noCustomHeaders": "Δεν έχουν οριστεί προσαρμοσμένες κεφαλίδες. Κάντε κλικ στο κουμπί + για να προσθέσετε μία.", "requestyApiKey": "Κλειδί API Requesty", "refreshModels": {"label": "Ανανέωση μοντέλων", "hint": "Ανοίξτε ξανά τις ρυθμίσεις για να δείτε τα πιο πρόσφατα μοντέλα.", "loading": "Ανανέωση λίστας μοντέλων...", "success": "Η λίστα μοντέλων ανανεώθηκε με επιτυχία!", "error": "Αποτυχ<PERSON><PERSON> ανανέωσης της λίστας μοντέλων. Δοκιμάστε ξανά."}, "getRequestyApiKey": "Λήψη κλειδιού API Requesty", "openRouterTransformsText": "Συμπίεση μηνυμάτων και αλυσίδων μηνυμάτων στο μέγεθος του πλαισίου (<a>Μετασχηματισμοί OpenRouter</a>)", "anthropicApiKey": "Κλειδί API Anthropic", "getAnthropicApiKey": "Λήψη κλειδιού API Anthropic", "anthropicUseAuthToken": "Να γίνεται αποστολή του κλειδιού API Anthropic ως κεφαλίδα Authorization αντί για X-Api-Key", "chutesApiKey": "Κλειδί API Chutes", "getChutesApiKey": "Λήψη κλειδιού API Chutes", "deepSeekApiKey": "Κλειδί API DeepSeek", "getDeepSeekApiKey": "Λήψη κλειδιού API DeepSeek", "geminiApiKey": "Κλειδί API Gemini", "getGroqApiKey": "Λήψη κλειδιού API Groq", "groqApiKey": "Κλειδί API Groq", "getGeminiApiKey": "Λήψη κλειδιού API Gemini", "openAiApiKey": "Κλειδί API OpenAI", "apiKey": "Κλειδί API", "openAiBaseUrl": "Βασικό URL", "getOpenAiApiKey": "Λήψη κλειδιού API OpenAI", "mistralApiKey": "Κλειδί API Mistral", "getMistralApiKey": "Λήψη κλειδιού API Mistral / Codestral", "codestralBaseUrl": "Βασικό URL Codestral (Προαιρετικό)", "codestralBaseUrlDesc": "Ορίστε μια εναλλακτική διεύθυνση URL για το μοντέλο Codestral.", "xaiApiKey": "Κλειδί API xAI", "getXaiApiKey": "Λήψη κλειδιού API xAI", "litellmApiKey": "Κλειδί API LiteLLM", "litellmBaseUrl": "Βασικό URL LiteLLM", "awsCredentials": "Διαπιστευτήρια AWS", "awsProfile": "Προ<PERSON><PERSON><PERSON>", "awsProfileName": "Όνομα προφίλ AWS", "awsAccessKey": "Κλειδί πρόσβασης AWS", "awsSecretKey": "Μυστικό κλειδί AWS", "awsSessionToken": "Διακριτικό συνεδρίας AWS", "awsRegion": "Περιοχή AWS", "awsCrossRegion": "Χρήση διαπεριφερεια<PERSON><PERSON>ς εξαγωγής συμπερασμάτων", "awsBedrockVpc": {"useCustomVpcEndpoint": "Χρήση προσαρμοσμένου τελικού σημείου VPC", "vpcEndpointUrlPlaceholder": "Εισαγάγ<PERSON><PERSON><PERSON> URL τελικού σημείου VPC (προαιρετικό)", "examples": "Παραδείγματα:"}, "enablePromptCaching": "Ενεργοποίηση προσωρινής αποθήκευσης προτροπών", "enablePromptCachingTitle": "Ενεργοποιήστε την προσωρινή αποθήκευση προτροπών για να βελτιώσετε την απόδοση και να μειώσετε το κόστος για τα υποστηριζόμενα μοντέλα.", "cacheUsageNote": "Σημείωση: <PERSON><PERSON><PERSON> δεν βλέπετε χρήση της προσωρινής αποθήκευσης, δοκιμάστε να επιλέξετε ένα διαφορετικό μοντέλο και, στη συνέχεια, επιλέξτε ξανά το επιθυμητό μοντέλο.", "vscodeLmModel": "Γλωσ<PERSON>ι<PERSON><PERSON> μοντέλο", "vscodeLmWarning": "Σημείωση: Πρόκειται για μια πολύ πειραματική ενοποίηση και η υποστήριξη του παρόχου θα διαφέρει. <PERSON><PERSON><PERSON> λάβετε ένα σφάλμα σχετικά με ένα μοντέλο που δεν υποστηρίζεται, αυτό είναι πρόβλημα από την πλευρά του παρόχου.", "googleCloudSetup": {"title": "Για να χρησιμοποιήσετε το Google Cloud Vertex AI, πρέπει:", "step1": "1. Να δημιουργήσετε έναν λογαρ<PERSON><PERSON><PERSON>μ<PERSON>, να ενεργοποιήσετε το Vertex AI API και να ενεργοποιήσετε τα επιθυμητά μοντέλα Claude.", "step2": "2. Να εγκαταστήσετε το Google Cloud CLI και να διαμορφώσετε τα προεπιλεγμένα διαπιστευτήρια εφαρμογής.", "step3": "3. Ή να δημιουργήσετε έναν λογαριασμό υπηρεσίας με διαπιστευτήρια."}, "googleCloudCredentials": "Διαπιστευτήρια Google Cloud", "googleCloudKeyFile": "Διαδρομή αρχείου κλειδιού Google Cloud", "googleCloudProjectId": "Αναγνωριστικό έργου Google Cloud", "googleCloudRegion": "Περιοχή Google Cloud", "lmStudio": {"baseUrl": "Βασική διεύθυνση URL (προαιρετικό)", "modelId": "Αναγνω<PERSON>ιστικ<PERSON> μοντέλου", "speculativeDecoding": "Ενεργοποίηση κερδοσκοπικής αποκωδικοποίησης", "draftModelId": "Αναγνω<PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON> μοντέλου πρόχειρου", "draftModelDesc": "Το μοντέλο πρόχειρου πρέπει να προέρχεται από την ίδια οικογένεια μοντέλων για να λειτουργεί σωστά η κερδοσκοπική αποκωδικοποίηση.", "selectDraftModel": "Επιλογή μοντέλου πρόχειρου", "noModelsFound": "Δεν βρέθηκαν μοντέλα πρόχειρου. Βεβαιωθείτε ότι το LM Studio εκτελείται με ενεργοποιημένη τη λειτουργία διακομιστή.", "description": "Το LM Studio σάς επιτρέπει να εκτελείτε μοντέλα τοπικά στον υπολογιστή σας. Για οδηγίες σχετικά με το πώς να ξεκινήσετε, ανατρέξτε στον <a>οδηγό γρήγορης εκκίνησης</a>. Θα χρειαστεί επίσης να ξεκινήσετε τη λειτουργία <b>τοπικού διακομιστή</b> του LM Studio για να το χρησιμοποιήσετε με αυτήν την επέκταση. <span>Σημείωση:</span> Το <PERSON>lo Code χρησιμοποιεί σύνθετες προτροπές και λειτουργεί καλύτερα με μοντέλα Claude. Λιγότερο ικανά μοντέλα ενδέχεται να μην λειτουργούν όπως αναμένεται."}, "ollama": {"baseUrl": "Βασική διεύθυνση URL (προαιρετικό)", "modelId": "Αναγνω<PERSON>ιστικ<PERSON> μοντέλου", "description": "Το <PERSON>ma σάς επιτρέπει να εκτελείτε μοντέλα τοπικά στον υπολογιστή σας. Για οδηγίες σχετικά με το πώς να ξεκινήσετε, ανατρέξτε στον οδηγό γρήγορης εκκίνησης.", "warning": "Σημείωση: Το <PERSON>lo Code χρησιμοποιεί σύνθετες προτροπές και λειτουργεί καλύτερα με μοντέλα Claude. Λιγότερο ικανά μοντέλα ενδέχεται να μην λειτουργούν όπως αναμένεται."}, "unboundApiKey": "Κλειδί API Unbound", "getUnboundApiKey": "Λήψη κλειδιού API Unbound", "unboundRefreshModelsSuccess": "Η λίστα μοντέλων ενημερώθηκε! Τώρα μπορείτε να επιλέξετε από τα πιο πρόσφατα μοντέλα.", "unboundInvalidApiKey": "Μη έγκυρο κλειδί API. Ελέγξτε το κλειδί API και προσπαθήστε ξανά.", "humanRelay": {"description": "Δεν απαιτε<PERSON><PERSON><PERSON><PERSON> κλ<PERSON><PERSON><PERSON><PERSON>, α<PERSON><PERSON><PERSON> ο χρήστης πρέπει να βοηθήσει στην αντιγραφή και επικόλληση των πληροφοριών στο web chat AI.", "instructions": "Κατά τη χρήση, θα εμφανιστε<PERSON> ένα παράθυρο διαλόγου και το τρέχον μήνυμα θα αντιγραφεί αυτόματα στο πρόχειρο. Πρέπει να τα επικολλήσετε σε εκδόσεις web του AI (όπως το ChatGPT ή το Claude), στη συνέχεια να αντιγράψετε την απάντηση του AI πίσω στο παράθυρο διαλόγου και να κάνετε κλικ στο κουμπί επιβεβαίωσης."}, "openRouter": {"providerRouting": {"title": "δρομολόγηση παρόχου OpenRouter", "description": "Το OpenRouter δρομολογεί τα αιτήματα στους καλύτερους διαθέσιμους παρόχους για το μοντέλο σας. Απ<PERSON> προεπιλογή, τα αιτήματα εξισορροπούνται ως προς το φορτίο στους κορυφαίους παρόχους για τη μεγιστοποίηση του χρόνου λειτουργίας. Ωστ<PERSON>σ<PERSON>, μπορείτε να επιλέξετε έναν συγκεκριμένο πάροχο για να χρησιμοποιήσετε για αυτό το μοντέλο.", "learnMore": "Μάθετε περισσότερα για τη δρομολόγηση παρόχων"}}, "cerebras": {"apiKey": "Κλειδί API Cerebras", "getApiKey": "Λήψη κλειδιού API Cerebras"}, "customModel": {"capabilities": "Διαμορφώστε τις δυνατότητες και την τιμολόγηση για το προσαρμοσμένο μοντέλο σας που είναι συμβατό με το OpenAI. Να είστε προσεκτικοί κατά τον καθορισμό των δυνατοτήτων του μοντέλου, καθώς μπορούν να επηρεάσουν την απόδοση του Kilo Code.", "maxTokens": {"label": "Μέγιστος αριθμός διακριτικών εξόδου", "description": "Μέγιστος αριθμός διακριτικών που μπορεί να δημιουργήσει το μοντέλο σε μια απόκριση. (Καθορίστε -1 για να επιτρέψετε στον διακομιστή να ορίσει τα μέγιστα διακριτικά.)"}, "contextWindow": {"label": "Μέγ<PERSON><PERSON>ος παραθύρου πλαισίου", "description": "Συνολικ<PERSON> διακριτικ<PERSON> (ε<PERSON><PERSON><PERSON><PERSON><PERSON> + έξοδος) που μπορεί να επεξεργαστεί το μοντέλο."}, "imageSupport": {"label": "Υποστήριξη εικόνας", "description": "Είναι αυτό το μοντέλο ικανό να επεξεργάζεται και να κατανοεί εικόνες;"}, "computerUse": {"label": "Χρήση υπολογιστή", "description": "Είναι αυτό το μοντέλο ικανό να αλληλεπιδρά με ένα πρόγραμμα περιήγησης; (π.χ. <PERSON> 3.7 Sonnet)."}, "promptCache": {"label": "Προσωρινή αποθήκευση προτροπών", "description": "Είναι αυτό το μοντέλο ικανό να αποθηκεύει προσωρινά προτροπές;"}, "pricing": {"input": {"label": "Τιμ<PERSON> εισόδου", "description": "Κ<PERSON><PERSON>τ<PERSON> αν<PERSON> εκατομμύριο διακριτι<PERSON><PERSON> στην είσοδο/προτροπή. Αυτό επηρεάζει το κόστος αποστολής πλαισίου και οδηγιών στο μοντέλο."}, "output": {"label": "Τιμ<PERSON> εξόδου", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> αν<PERSON> εκατομμύριο διακρι<PERSON>ι<PERSON><PERSON> στην απόκριση του μοντέλου. Αυτό επηρεάζει το κόστος του παραγόμενου περιεχομένου και των ολοκληρώσεων."}, "cacheReads": {"label": "Τιμή ανάγνωσης προσωρινής μνήμης", "description": "Κ<PERSON>στ<PERSON> αν<PERSON> εκατομμύριο διακριτικά για την ανάγνωση από την προσωρινή μνήμη. Αυτή είναι η τιμή που χρεώνετα<PERSON> όταν ανακτάται μια αποθηκευμένη απόκριση."}, "cacheWrites": {"label": "Τιμή εγγραφής στην προσωρινή μνήμη", "description": "Κ<PERSON>στ<PERSON> αν<PERSON> εκατομμύριο διακριτικά για την εγγραφή στην προσωρινή μνήμη. Αυτή είναι η τιμή που χρεώνεται όταν μια προτροπή αποθηκεύεται για πρώτη φορά."}}, "resetDefaults": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλογές"}, "rateLimitSeconds": {"label": "Όριο ρυθμού", "description": "Ελάχιστος χρόνος μεταξύ αιτημάτων API."}, "reasoningEffort": {"label": "Προσπάθεια συλλογισμού μοντέλου", "high": "Υψηλή", "medium": "Μεσαία", "low": "Χαμηλή"}, "setReasoningLevel": "Ενεργοποίηση προσπάθειας συλλογισμού", "claudeCode": {"pathLabel": "Διαδρομή Claude Code", "description": "Προαιρετική διαδρομή προς το Claude Code CLI. Προεπιλογή 'claude' εάν δεν οριστεί.", "placeholder": "Προεπιλογή: claude"}, "geminiCli": {"description": "Αυτ<PERSON>ς ο πάροχος χρησιμοποιεί την αυθεντικοποίηση OAuth του εργαλείου Gemini CLI και δεν απαιτεί κλειδιά API.", "oauthPath": "Διαδρομή διαπιστευτη<PERSON><PERSON><PERSON><PERSON> (προαιρετικό)", "oauthPathDescription": "Διαδρομή προς το αρχείο διαπιστευτηρίων OAuth. Αφήστε κενό για χρήση της προεπιλεγμένης τοποθεσίας (~/.gemini/oauth_creds.json).", "instructions": "Αν δεν έχεις αυθεντικοποιηθεί ακόμα, εκτέλεσε", "instructionsContinued": "στο τερματικό σου πρώτα.", "setupLink": "Οδηγ<PERSON><PERSON>ς εγκατάστασης Gemini CLI", "requirementsTitle": "Σημαντικ<PERSON>ς απαιτήσεις", "requirement1": "Πρώτ<PERSON>, πρέπει να εγκαταστήσεις το εργαλείο Gemini CLI", "requirement2": "Έπειτα, εκτέλεσε gemini στο τερματικό σου και βεβαιώσου ότι συνδέεσαι με Google", "requirement3": "Λειτουργ<PERSON><PERSON> μόνο με προσωπικούς λογαριασμούς Google (όχι λογαριασμούς Google Workspace)", "requirement4": "Δεν χρησιμοποιεί κλειδιά API - η αυθεντικοποίηση διαχειρίζεται μέσω OAuth", "requirement5": "Απαιτεί το εργαλείο Gemini CLI να είναι εγκατεστημένο και αυθεντικοποιημένο πρώτα", "freeAccess": "Δω<PERSON><PERSON><PERSON>ν πρόσβαση μέσω αυθεντικοποίησης OAuth"}}, "browser": {"enable": {"label": "Ενεργοποίηση εργαλείου περιηγητή", "description": "Όταν είναι ενεργοποιημένο, το <PERSON>lo Code μπορεί να χρησιμοποιήσει ένα πρόγραμμα περιήγησης για να αλληλεπιδράσει με ιστότοπους όταν χρησιμοποιούνται μοντέλα που υποστηρίζουν τη χρήση υπολογιστή. <0>Μάθετε περισσότερα</0>"}, "viewport": {"label": "Μέγ<PERSON><PERSON>ος παραθύρου προβολής", "description": "Επιλέξτε το μέγεθος του παραθύρου προβολής για αλληλεπιδράσεις με το πρόγραμμα περιήγησης. Αυτό επηρεάζει τον τρόπο εμφάνισης και αλληλεπίδρασης με τους ιστότοπους.", "options": {"largeDesktop": "Μεγάλη επιφάνεια εργασίας (1280x800)", "smallDesktop": "Μικρή επιφάνεια εργασίας (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Κινητό (360x640)"}}, "screenshotQuality": {"label": "Ποιότητα στιγμιότυπου οθόνης", "description": "Προσαρμόστε την ποιότητα WebP των στιγμιότυπων οθόνης του προγράμματος περιήγησης. Οι υψηλότερες τιμές παρέχουν καθαρότερα στιγμιότυπα οθόνης αλλά αυξάνουν τη χρήση διακριτικών."}, "remote": {"label": "Χρήση απομακρυσμένης σύνδεσης προγράμματος περιήγησης", "description": "Σύνδεση σε πρόγραμμα περιήγησης Chrome που εκτελείται με ενεργοποιημένο τον απομακρυσμένο εντοπισμό σφαλμάτων (--remote-debugging-port=9222).", "urlPlaceholder": "Προσαρμοσμένη διεύθυνση URL (π.χ., http://localhost:9222)", "testButton": "Δοκιμή σύνδεσης", "testingButton": "Δοκιμή...", "instructions": "Εισαγάγετε τη διεύθυνση κεντρικού υπολογιστή του πρωτοκόλλου DevTools ή αφήστε το κενό για αυτόματη ανακάλυψη τοπικών παρουσιών του Chrome. Το κουμπί Δοκιμή σύνδεσης θα δοκιμάσει την προσαρμοσμένη διεύθυνση URL εάν παρέχεται ή θα πραγματοποιήσει αυτόματη ανακάλυψη εάν το πεδίο είναι κενό."}}, "checkpoints": {"enable": {"label": "Ενεργοποίηση αυτόματων σημείων ελέγχου", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code θα δημιουργεί αυτόματα σημεία ελέγχου κατά την εκτέλεση της εργασίας, διευκολύνοντας την αναθεώρηση αλλαγών ή την επαναφορά σε προηγούμενες καταστάσεις. <0>Μάθετε περισσότερα</0>"}}, "display": {"taskTimeline": {"label": "Εμφάνιση χρονοδιαγράμματος εργασίας", "description": "Εμφάνιση οπτικού χρονοδιαγράμματος των μηνυμάτων εργασίας, χρωματισμένο ανά τύπο, επιτρέ<PERSON><PERSON>ν<PERSON><PERSON>ς σας να βλέπετε γρήγορα την πρόοδο της εργασίας και να επιστρέφετε σε συγκεκριμένα σημεία στο ιστορικό της εργασίας."}}, "notifications": {"sound": {"label": "Ενεργοποίηση ηχητικών εφέ", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> θα αναπαράγει ηχητικά εφέ για ειδοποιήσεις και συμβάντα.", "volumeLabel": "Ένταση"}, "tts": {"label": "Ενεργοποίηση μετατροπής κειμένου σε ομιλία", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> θα διαβάζει δυνατά τις απαντήσεις του χρησιμοποιώντας μετατροπή κειμένου σε ομιλία.", "speedLabel": "Ταχύτητα"}}, "contextManagement": {"description": "Ελέγξτε ποιες πληροφορίες περιλαμβάνονται στο παράθυρο περιβάλλοντος της AI, επηρε<PERSON><PERSON><PERSON>ντας τη χρήση διακριτικών και την ποιότητα της απόκρισης", "autoCondenseContextPercent": {"label": "Όριο για την ενεργοποίηση της έξυπνης συμπύκνωσης περιβάλλοντος", "description": "Όταν το παράθυρο περιβάλλοντος φτάσει σε αυτό το όριο, το <PERSON>lo Code θα το συμπυκνώσει αυτόματα."}, "condensingApiConfiguration": {"label": "Διαμόρφωση API για συμπύκνωση περιβάλλοντος", "description": "Επιλέξτε ποια διαμόρφωση API θα χρησιμοποιηθεί για τις λειτουργίες συμπύκνωσης περιβάλλοντος. Αφήστε το κενό για να χρησιμοποιήσετε την τρέχουσα ενεργή διαμόρφωση.", "useCurrentConfig": "Προεπιλογή"}, "customCondensingPrompt": {"label": "Προσαρμοσμένη προτροπή συμπύκνωσης περιβάλλοντος", "description": "Προσαρμόστε την προτροπή συστήματος που χρησιμοποιείται για τη συμπύκνωση περιβάλλοντος. Αφήστε το κενό για να χρησιμοποιήσετε την προεπιλεγμένη προτροπή.", "placeholder": "Εισαγάγετε εδώ την προσαρμοσμένη προτροπή συμπύκνωσης...\n\nΜπορείτε να χρησιμοποιήσετε την ίδια δομή με την προεπιλεγμένη προτροπή:\n- Προηγούμενη συνομιλία\n- Τρέχουσα εργασία\n- Β<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> τεχνικές έννοιες\n- Σχετικά αρχεία και κώδικας\n- Επίλυση προβλημάτων\n- Εκκρεμείς εργασίες και επόμενα βήματα", "reset": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλογή", "hint": "Κενό = χρήση προεπιλεγμένης προτροπής"}, "autoCondenseContext": {"name": "Αυτόματη ενεργοποίηση της έξυπνης συμπύκνωσης περιβάλλοντος", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code θα συμπυκνώσει αυτόματα το περιβάλλον όταν επιτευχθεί το όριο. Όταν είναι απενεργοποιημένο, μπορείτε ακόμα να ενεργοποιήσετε χειροκίνητα τη συμπύκνωση περιβάλλοντος."}, "openTabs": {"label": "Όριο περιβάλλοντος ανοιχτών καρτελών", "description": "Μέγιστος αριθμός ανοιχτών καρτελών VSCode που θα συμπεριληφθούν στο περιβάλλον. Οι υψηλότερες τιμές παρέχουν περισσότερο περιβάλλον αλλά αυξάνουν τη χρήση διακριτικών."}, "workspaceFiles": {"label": "Όριο περιβάλλοντος αρχείων χώρου εργασίας", "description": "Μέγιστος αριθμός αρχείων που θα συμπεριληφθούν στις λεπτομέρειες του τρέχοντος καταλόγου εργασίας. Οι υψηλότερες τιμές παρέχουν περισσότερο περιβάλλον αλλά αυξάνουν τη χρήση διακριτικών."}, "rooignore": {"label": "Εμφάνιση αρχείων .kilocodeign<PERSON> σε λίστες και αναζητήσεις", "description": "Όταν είναι ενεργοποιημένο, τα αρχεία που ταιριάζουν με μοτίβα στο .kilocodeignore θα εμφανίζονται σε λίστες με σύμβολο κλειδαριάς. Όταν είναι απενεργοποιημένο, αυτά τα αρχεία θα είναι εντελώς κρυμμένα από τις λίστες αρχείων και τις αναζητήσεις."}, "maxConcurrentFileReads": {"label": "Όριο ταυτόχρονων αναγνώσεων αρχείων", "description": "Μέγιστος αριθμός αρχείων που μπορεί να επεξεργαστεί ταυτόχρονα το εργαλείο 'read_file'. Οι υψηλότερες τιμές μπορεί να επιταχύνουν την ανάγνωση πολλών μικρών αρχείων αλλά αυξάνουν τη χρήση μνήμης."}, "maxReadFile": {"label": "Όριο αυτόματης περικοπής ανάγνωσης αρχείου", "description": "Το Kilo Code διαβάζει αυτόν τον αριθμό γραμμών όταν το μοντέλο παραλείπει τις τιμές έναρξης/λήξης. Εάν αυτός ο αριθμός είναι μικρότερος από το σύνολο του αρχείου, το Kilo Code δημιουργεί έναν ευρετήριο αριθμών γραμμών των ορισμών κώδικα. Ειδικές περιπτώσεις: -1 δίνει εντολή στο Kilo Code να διαβάσει ολόκληρο το αρχείο (χωρίς ευρετηρίαση) και 0 του δίνει εντολή να μην διαβάσει καμία γραμμή και παρέχει μόνο ευρετήρια γραμμών για ελάχιστο περιβάλλον. Οι χαμηλότερες τιμές ελαχιστοποιούν την αρχική χρήση περιβάλλοντος, επιτρέποντας ακριβείς επακόλουθες αναγνώσεις εύρους γραμμών. Οι ρητές αιτήσεις έναρξης/λήξης δεν περιορίζονται από αυτήν τη ρύθμιση.", "lines": "γραμμές", "always_full_read": "Να διαβά<PERSON><PERSON><PERSON><PERSON><PERSON> πάντα ολόκληρο το αρχείο"}, "condensingThreshold": {"label": "Όριο ενεργοποίησης συμπύκνωσης", "selectProfile": "Διαμόρφωση ορίου για προφίλ", "defaultProfile": "Παγκόσμια προεπιλογή (όλα τα προφίλ)", "defaultDescription": "Όταν το περιβάλλον φτάσει σε αυτό το ποσοστό, θα συμπυκνωθεί αυτόματα για όλα τα προφίλ, εκτ<PERSON>ς εάν έχουν προσαρμοσμένες ρυθμίσεις", "profileDescription": "Προσαρμοσμέν<PERSON> όριο μόνο για αυτό το προφίλ (αντικαθιστά την παγκόσμια προεπιλογή)", "inheritDescription": "Αυτό το προφίλ κληρονομεί το παγκόσμιο προεπιλεγμένο όριο ({{threshold}}%)", "usesGlobal": "(χρησιμοποιεί παγκόσμιο {{threshold}}%)"}}, "terminal": {"basic": {"label": "Ρυθμίσεις τερματικού: Βασικές", "description": "Βασικές ρυθμίσεις τερματικού"}, "advanced": {"label": "Ρυθμίσεις τερματικού: Προηγμένες", "description": "Οι ακόλουθες επιλογές ενδέχεται να απαιτούν επανεκκίνηση του τερματικού για την εφαρμογή της ρύθμισης."}, "outputLineLimit": {"label": "Όριο εξόδου τερματικού", "description": "Μέγιστος αριθμός γραμμών που θα συμπεριληφθούν στην έξοδο του τερματικού κατά την εκτέλεση εντολών. Όταν ξεπεραστεί, οι γραμμές θα αφαιρεθούν από τη μέση, εξοικονομώντας διακριτικά. <0>Μάθετε περισσότερα</0>"}, "shellIntegrationTimeout": {"label": "Χρονικ<PERSON> όριο ενοποίησης κελύφους τερματικού", "description": "Μέγιστος χρόνος αναμονής για την προετοιμασία της ενοποίησης κελύφους πριν από την εκτέλεση εντολών. Για χρήστες με μεγάλους χρόνους εκκίνησης κελύφους, αυτή η τιμή μπορεί να χρειαστεί να αυξηθεί εάν βλέπετε σφάλματα \"Μη διαθέσιμη ενοποίηση κελύφους\" στο τερματικό. <0>Μ<PERSON>θ<PERSON>τ<PERSON> περισσότερα</0>"}, "shellIntegrationDisabled": {"label": "Απενεργοποίηση ενοποίησης κελύφους τερματικού", "description": "Ενεργοποιήστε αυτό εάν οι εντολές του τερματικού δεν λειτουργούν σωστά ή εάν βλέπετε σφάλματα 'Μη διαθέσιμη ενοποίηση κελύφους'. Αυτό χρησιμοποιεί μια απλούστερη μέθοδο για την εκτέλεση εντολών, παρακάμπτοντας ορισμένες προηγμένες δυνατότητες του τερματικού. <0><PERSON><PERSON><PERSON><PERSON>τ<PERSON> περισσότερα</0>"}, "commandDelay": {"label": "Καθυστέρηση εντολής τερματικού", "description": "Καθυστέρηση σε χιλιοστά του δευτερολέπτου που προστίθεται μετά την εκτέλεση της εντολής. Η προεπιλεγμένη ρύθμιση 0 απενεργοποιεί πλήρως την καθυστέρηση. Αυτό μπορεί να βοηθήσει να διασφαλιστεί ότι η έξοδος της εντολής καταγράφεται πλήρως σε τερματικά με προβλήματα συγχρονισμού. Στα περισσότερα τερματικά υλοποιείται με τον ορισμό `PROMPT_COMMAND='sleep N'` και το Powershell προσθέτει το `start-sleep` στο τέλος κάθε εντολής. <PERSON>ρ<PERSON><PERSON><PERSON><PERSON> ήταν λύση για το σφάλμα VSCode#237208 και μπορεί να μην είναι πλέον απαραίτητο. <0>Μάθετε περισσότερα</0>"}, "compressProgressBar": {"label": "Συμπίεση εξόδου γραμμής προόδου", "description": "Όταν είναι ενεργοποιημένο, επεξ<PERSON><PERSON><PERSON><PERSON><PERSON>εται την έξοδο του τερματικού με επιστροφές μεταφοράς (\\r) για να προσομοιώσει τον τρόπο με τον οποίο ένα πραγματικό τερματικό θα εμφάνιζε το περιεχόμενο. Αυτό αφαιρεί τις ενδιάμεσες καταστάσεις της γραμμής προόδου, διατηρώντας μόνο την τελική κατάσταση, η οποία εξοικονομεί χώρο περιβάλλοντος για πιο σχετικές πληροφορίες. <0>Μάθετε περισσότερα</0>"}, "powershellCounter": {"label": "Ενεργοποίηση λύσης μετρητή PowerShell", "description": "Όταν είναι ενεργοποιημένο, προσθέτει έναν μετρητή στις εντολές PowerShell για να διασφαλίσει τη σωστή εκτέλεση της εντολής. Αυτό βοηθά με τερματικά PowerShell που ενδέχεται να έχουν προβλήματα με τη λήψη εξόδου εντολών. <0>Μάθετε περισσότερα</0>"}, "zshClearEolMark": {"label": "Εκκαθάριση σήματος EOL ZSH", "description": "Όταν είναι ενεργοποιημένο, εκκαθαρίζει το σήμα τέλους γραμμής ZSH ορίζοντας PROMPT_EOL_MARK=''. Αυτό αποτρέπει προβλήματα με την ερμηνεία της εξόδου εντολών όταν η έξοδος τελειώνει με ειδικούς χαρακτήρες όπως '%'. <0>Μάθετε περισσότερα</0>"}, "zshOhMy": {"label": "Ενεργοποίηση ενοποίησης Oh My Zsh", "description": "Όταν είναι ενεργοποιημένο, ορίζει το ITERM_SHELL_INTEGRATION_INSTALLED=Yes για να ενεργοποιήσει τις δυνατότητες ενοποίησης κελύφους Oh My Zsh. Η εφαρμογή αυτής της ρύθμισης ενδέχεται να απαιτεί επανεκκίνηση του IDE. <0>Μάθετε περισσότερα</0>"}, "zshP10k": {"label": "Ενεργοποίηση ενοποίησης Powerlevel10k", "description": "Όταν είναι ενεργοποιημένο, ορίζει το POWERLEVEL9K_TERM_SHELL_INTEGRATION=true για να ενεργοποιήσει τις δυνατότητες ενοποίησης κελύφους Powerlevel10k. <0>Μάθετε περισσότερα</0>"}, "zdotdir": {"label": "Ενεργοποίηση διαχείρισης ZDOTDIR", "description": "Όταν είναι ενεργοποιημένο, δημιου<PERSON><PERSON><PERSON><PERSON> έναν προσωρινό κατάλογο για το ZDOTDIR για τη σωστή διαχείριση της ενοποίησης κελύφους zsh. Αυτό διασφαλίζει ότι η ενοποίηση κελύφους VSCode λειτουργεί σωστά με το zsh διατηρώντας παράλληλα τη διαμόρφωση του zsh. <0><PERSON>άθετε περισσότερα</0>"}, "inheritEnv": {"label": "Κληρονο<PERSON>ικότητα μεταβλητών περιβάλλοντος", "description": "Όταν είναι ενεργοποιημένο, το τερματικό θα κληρονομήσει μεταβλητές περιβάλλοντος από τη γονική διαδικασία του VSCode, όπως οι ρυθμίσεις ενοποίησης κελύφους που ορίζονται από το προφίλ χρήστη. Αυτό αλλάζει απευθείας την καθολική ρύθμιση του VSCode `terminal.integrated.inheritEnv`. <0>Μάθετε περισσότερα</0>"}}, "advanced": {"diff": {"label": "Ενεργοποίησηแก้ไข μέσω διαφορών", "description": "Όταν είναι ενεργοποιημένο, το <PERSON>lo Code θα μπορεί να επεξεργάζεται αρχεία πιο γρήγορα και θα απορρίπτει αυτόματα τις περικομμένες εγγραφές ολόκληρων αρχείων. Λειτουργεί καλύτερα με το πιο πρόσφατο μοντέλο Claude 4 Sonnet.", "strategy": {"label": "Στρατη<PERSON><PERSON><PERSON><PERSON>ff", "options": {"standard": "Τυπική (Μεμονωμένο μπλοκ)", "multiBlock": "Πειραματικό: Diff πολλαπλών μπλοκ", "unified": "Πειραματικό: Ενοποιημένο diff"}, "descriptions": {"standard": "Η τυπική στρατηγική diff εφαρμόζει αλλαγές σε ένα μόνο μπλοκ κώδικα κάθε φορά.", "unified": "Η ενοποιημένη στρατηγική diff υιοθετεί πολλαπλές προσεγγίσεις για την εφαρμογή διαφορών και επιλέγει την καλύτερη προσέγγιση.", "multiBlock": "Η στρατηγική diff πολλαπλών μπλοκ επιτρέπει την ενημέρωση πολλαπλών μπλοκ κώδικα σε ένα αρχείο με μία μόνο αίτηση."}}, "matchPrecision": {"label": "Ακρίβεια αντιστοίχισης", "description": "Αυτό το ρυθμιστικό ελέγχει πόσο ακριβώς πρέπει να ταιριάζουν τα τμήματα κώδικα κατά την εφαρμογή διαφορών. Οι χαμηλότερες τιμές επιτρέπουν πιο ευέλικτη αντιστοίχιση αλλά αυξάνουν τον κίνδυνο λανθασμένων αντικαταστάσεων. Χρησιμοποιήστε τιμές κάτω από 100% με εξαιρετική προσοχή."}}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Χρήση πειραματικής ενοποιημένης στρατηγικής diff", "description": "Ενεργοποιήστε την πειραματική ενοποιημένη στρατηγική diff. Αυτή η στρατηγική μπορεί να μειώσει τον αριθμό των επαναλήψεων που προκαλούνται από σφάλματα μοντέλου, αλλά μπορεί να προκαλέσει απροσδόκητη συμπεριφορά ή λανθασμένες επεξεργασίες. Ενεργοποιήστε μόνο εάν κατανοείτε τους κινδύνους και είστε πρόθυμοι να ελέγξετε προσεκτικά όλες τις αλλαγές."}, "SEARCH_AND_REPLACE": {"name": "Χρήση πειραματικού εργαλείου αναζήτησης και αντικατάστασης", "description": "Ενεργοποιήστε το πειραματικ<PERSON> εργαλείο αναζήτησης και αντικατάστασης, επιτρέποντας στο Kilo Code να αντικαταστήσει πολλαπλές εμφανίσεις ενός όρου αναζήτησης σε ένα αίτημα."}, "INSERT_BLOCK": {"name": "Χρήση πειραματικού εργαλείου εισαγωγής περιεχομένου", "description": "Ενεργοποιήστε το πειραματικ<PERSON> εργαλείο εισαγωγής περιεχομένου, επιτρέποντας στο Kilo Code να εισάγει περιεχόμενο σε συγκεκριμένους αριθμούς γραμμών χωρίς να χρειάζεται να δημιουργήσει diff."}, "POWER_STEERING": {"name": "Χρήση πειραματικής λειτουργίας \"power steering\"", "description": "Όταν είναι ενεργοποιημένο, το <PERSON>lo Code θα υπενθυμίζει στο μοντέλο τις λεπτομέρειες του τρέχοντος ορισμού λειτουργίας του πιο συχνά. Αυτό θα οδηγήσει σε ισχυρότερη τήρηση των ορισμών ρόλων και των προσαρμοσμένων οδηγιών, αλλά θα χρησιμοποιήσει περισσότερα διακριτικά ανά μήνυμα."}, "AUTOCOMPLETE": {"name": "Χρήση πειραματικής λειτουργίας \"αυτόματης συμπλήρωσης\"", "description": "Όταν είναι ενεργοποιημένο, το <PERSON>lo Code θα παρέχει ενσωματωμένες προτάσεις κώδικα καθώς πληκτρολογείτε. Απαιτεί Kilo Code API Provider."}, "CONCURRENT_FILE_READS": {"name": "Ενεργοποίηση ταυτόχρονων αναγνώσεων αρχείων", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code μπορεί να διαβάσει πολλαπλά αρχεία σε ένα μόνο αίτημα. Όταν είναι απενεργοποιημένο, το Kilo Code πρέπει να διαβάζει αρχεία ένα κάθε φορά. Η απενεργοποίηση αυτού μπορεί να βοηθήσει όταν εργάζεστε με λιγότερο ικανά μοντέλα ή όταν θέλετε περισσότερο έλεγχο στην πρόσβαση αρχείων."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Χρήση πειραματικού εργαλείου diff πολλαπλών μπλοκ", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code θα χρησιμοποιήσει το εργαλείο diff πολλαπλών μπλοκ. Αυτό θα προσπαθήσει να ενημερώσει πολλαπλά μπλοκ κώδικα στο αρχείο σε ένα αίτημα."}, "MARKETPLACE": {"name": "Ενεργοποίηση Marketplace", "description": "Όταν είναι ενεργοποιημένο, μπορείτε να εγκαταστήσετε MCPs και προσαρμοσμένες λειτουργίες από το Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Ενεργοποίηση ταυτόχρονων επεξεργασιών αρχείων", "description": "Όταν είναι ενεργοποιημένο, το <PERSON>lo Code μπορεί να επεξεργαστεί πολλαπλά αρχεία σε ένα μόνο αίτημα. Όταν είναι απενεργοποιημένο, το Kilo Code πρέπει να επεξεργάζεται αρχεία ένα κάθε φορά. Η απενεργοποίηση αυτού μπορεί να βοηθήσει όταν εργάζεστε με λιγότερο ικανά μοντέλα ή όταν θέλετε περισσότερο έλεγχο στις τροποποιήσεις αρχείων."}}, "promptCaching": {"label": "Απενεργοποίηση προσωρινής αποθήκευσης προτροπών", "description": "Όταν είναι επιλεγμένο, το <PERSON>lo <PERSON> δεν θα χρησιμοποιεί προσωρινή αποθήκευση προτροπών για αυτό το μοντέλο."}, "temperature": {"useCustom": "Χρήση προσαρμοσμένης θερμοκρασίας", "description": "Ελέγχει την τυχαιότητα στις απαντήσεις του μοντέλου.", "rangeDescription": "Οι υψηλότερες τιμές κάνουν την έξοδο πιο τυχαία, οι χαμηλότερες τιμές την κάνουν πιο ντετερμινιστική."}, "modelInfo": {"supportsImages": "Υποστηρίζει εικόνες", "noImages": "Δεν υποστηρίζει εικόνες", "supportsComputerUse": "Υποστηρίζει χρήση υπολογιστή", "noComputerUse": "Δεν υποστηρίζει χρήση υπολογιστή", "supportsPromptCache": "Υποστηρίζει προσωρινή αποθήκευση προτροπών", "noPromptCache": "Δεν υποστηρίζει προσωρινή αποθήκευση προτροπών", "maxOutput": "Μέγιστη έξοδος", "inputPrice": "Τιμ<PERSON> εισόδου", "outputPrice": "Τιμ<PERSON> εξόδου", "cacheReadsPrice": "Τιμή ανάγνωσης προσωρινής μνήμης", "cacheWritesPrice": "Τιμή εγγραφής προσωρινής μνήμης", "enableStreaming": "Ενεργοποίηση ροής", "enableR1Format": "Ενεργοποίηση παραμέτρων μοντέλου R1", "enableR1FormatTips": "Πρέπει να είναι ενεργοποιημένο κατά τη χρήση μοντέλων R1 όπως το QWQ για την αποφυγή σφαλμάτων 400", "useAzure": "Χρή<PERSON><PERSON> Azure", "azureApiVersion": "Ορισμός έκδοσης Azure API", "gemini": {"freeRequests": "* Δωρεάν έως {{count}} αιτήματα ανά λεπτό. Μετά από αυτό, η χρέωση εξαρτάται από το μέγεθος της προτροπής.", "pricingDetails": "Για περισσότερες πληροφορίες, δείτε τις λεπτομέρειες τιμολόγησης.", "billingEstimate": "* Η χρέωση είναι εκτίμηση - το ακριβές κόστος εξαρτάται από το μέγεθος της προτροπής."}}, "modelPicker": {"automaticFetch": "Η επέκταση ανακτά αυτόματα την πιο πρόσφατη λίστα μοντέλων που είναι διαθέσιμα στο <serviceLink>{{serviceName}}</serviceLink>. Εάν δεν είστε σίγουροι ποιο μοντέλο να επιλέξετε, το Kilo Code λειτουργεί καλύτερα με το <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Μπορείτε επίσης να δοκιμάσετε να αναζητήσετε \"free\" για επιλογές χωρίς κόστος που είναι διαθέσιμες αυτήν τη στιγμή.", "label": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "Αναζήτηση", "noMatchFound": "Δεν βρέθηκε αντιστοιχία", "useCustomModel": "Χρήση προσαρμοσμένου: {{modelId}}"}, "footer": {"feedback": "Εάν έχετε ερωτήσεις ή σχόλια, μη διστάσετε να ανοίξετε ένα θέμα στο <githubLink>github.com/Kilo-Org/kilocode</githubLink> ή να συμμετάσχετε στο <redditLink>reddit.com/r/kilocode</redditLink> ή στο <discordLink>kilocode.ai/discord</discordLink>.", "support": "Για οικονομικά ερωτήματα, επικοινωνήστε με την Υποστήριξη Πελατών στο <supportLink>https://kilocode.ai/support</supportLink>", "telemetry": {"label": "Επιτρέψτε ανώνυμη αναφορ<PERSON> σφαλμάτων και χρήσης", "description": "Βοηθήστε στη βελτίωση του <PERSON>lo Code στέλνοντας ανώνυμα δεδομένα χρήσης και αναφορές σφαλμάτων. Δεν αποστέλλεται ποτέ κώδικας, προτροπές ή προσωπικές πληροφορίες. Δείτε την πολιτική απορρήτου μας για περισσότερες λεπτομέρειες."}, "settings": {"import": "Εισαγωγή", "export": "Εξαγωγή", "reset": "Επαναφορά"}}, "thinkingBudget": {"maxTokens": "Μέγιστα Διακριτικά", "maxThinkingTokens": "Μέγιστα Διακριτικά Σκέψης"}, "validation": {"apiKey": "Πρέπει να παρέχετε ένα έγκυρο κλειδί API.", "awsRegion": "Πρέπει να επιλέξετε μια περιοχή για χρήση με το Amazon Bedrock.", "googleCloud": "Πρέπει να παρέχετε ένα έγκυρο αναγνωριστικό έργου και περιοχή Google Cloud.", "modelId": "Πρέπει να παρέχετε ένα έγκυρο αναγνωριστικό μοντέλου.", "modelSelector": "Πρέπει να παρέχετε έναν έγκυρο επιλογέα μοντέλου.", "openAi": "Πρέπει να παρέχετε μια έγκυρη βασική διεύθυνση URL, κλειδί API και αναγνωριστικό μοντέλου.", "arn": {"invalidFormat": "Μη έγκυρη μορφή ARN. Ελέγξτε τις απαιτήσεις μορφής.", "regionMismatch": "Προειδοποίηση: Η περιοχή στο ARN σας ({{arnRegion}}) δεν ταιριάζει με την επιλεγμένη περιοχή σας ({{region}}). Αυτ<PERSON> μπορεί να προκαλέσει προβλήματα πρόσβασης. Ο πάροχος θα χρησιμοποιήσει την περιοχή από το ARN."}, "modelAvailability": "Το αναγνωριστικό μοντέλου ({{modelId}}) που παρείχατε δεν είναι διαθέσιμο. Επιλέξτε ένα διαφορετικό μοντέλο.", "providerNotAllowed": "Ο πάροχος '{{provider}}' δεν επιτρέπεται από τον οργανισμό σας", "modelNotAllowed": "Το μοντέλο '{{model}}' δεν επιτρέπεται για τον πάροχο '{{provider}}' από τον οργανισμό σας", "profileInvalid": "Αυτό το προφίλ περιέχει έναν πάροχο ή μοντέλο που δεν επιτρέπεται από τον οργανισμό σας"}, "placeholders": {"apiKey": "Εισαγάγετε κλειδί API...", "profileName": "Εισαγάγετε όνομα προφίλ", "accessKey": "Εισαγάγετε κλειδί πρόσβασης...", "secretKey": "Εισαγάγετε μυστικό κλειδί...", "sessionToken": "Εισαγάγετε διακριτικό συνεδρίας...", "credentialsJson": "Εισαγάγετε JSON διαπιστευτηρίων...", "keyFilePath": "Εισαγάγετε διαδρομή αρχείου κλειδιού...", "projectId": "Εισαγάγετε αναγνωριστικό έργου...", "customArn": "Εισαγάγετε ARN (π.χ. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Εισαγάγετε βασική διεύθυνση URL...", "modelId": {"lmStudio": "π.χ. meta-llama-3.1-8b-instruct", "lmStudioDraft": "π.χ. lmstudio-community/llama-3.2-1b-instruct", "ollama": "π.χ. llama3.1"}, "numbers": {"maxTokens": "π.χ. 4096", "contextWindow": "π.χ. 128000", "inputPrice": "π.χ. 0.0001", "outputPrice": "π.χ. 0.0002", "cacheWritePrice": "π.χ. 0.00005"}}, "defaults": {"ollamaUrl": "Προεπιλογή: http://localhost:11434", "lmStudioUrl": "Προεπιλογή: http://localhost:1234", "geminiUrl": "Προεπιλογή: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Προσαρμοσμένο ARN", "useCustomArn": "Χρήση προσαρμοσμένου ARN..."}, "includeMaxOutputTokens": "Συμπερίληψη μέγιστων διακριτικών εξόδου", "includeMaxOutputTokensDescription": "Αποστολή παραμέτρου μέγιστων διακριτικών εξόδου στα αιτήματα API. Ορισμένοι πάροχοι μπορεί να μην υποστηρίζουν αυτό."}