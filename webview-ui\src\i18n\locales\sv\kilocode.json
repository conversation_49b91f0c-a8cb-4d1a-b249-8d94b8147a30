{"welcome": {"greeting": "Välkommen till Kilo Code!", "introText1": "Kilo Code är en gratis, öppen källkods AI-kodningsagent.", "introText2": "<PERSON> fungerar med de senaste AI-modellerna som Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, och <PERSON><PERSON> 450 andra.", "introText3": "Skapa ett gratis konto och få tokens värda $20 att använda med vilken AI-modell som helst.", "ctaButton": "<PERSON><PERSON><PERSON> gratis konto", "manualModeButton": "Använd din egen API-nyckel", "alreadySignedUp": "<PERSON>an registrerad?", "loginText": "<PERSON>gga in här"}, "lowCreditWarning": {"addCredit": "Lägg till kredit", "lowBalance": "<PERSON><PERSON>-sa<PERSON> l<PERSON>"}, "notifications": {"toolRequest": "Verktygsbegäran väntar på godkännande", "browserAction": "Webbläsaråtgärd väntar på godkännande", "command": "Kommando väntar på godkännande"}, "settings": {"sections": {"mcp": "MCP-servrar"}, "provider": {"account": "Kilo Code-konto", "apiKey": "Kilo Code API-nyckel", "login": "<PERSON><PERSON><PERSON> in på Kilo Code", "logout": "<PERSON><PERSON>a ut fr<PERSON><PERSON>"}, "contextManagement": {"allowVeryLargeReads": {"label": "<PERSON><PERSON><PERSON> mycket stora fill<PERSON>ar", "description": "När aktiverat kommer Kilo Code att utföra mycket stora fil- eller MCP-utdataläsningar även om det finns en hög sannolikhet att överskrida kontextfönstret (innehållsstorlek >80% av kontextfönstret)."}}, "systemNotifications": {"label": "Aktivera systemnotifikationer", "description": "När aktiverat kommer Kilo Code att skicka systemnotifikationer för viktiga händelser som slutförda uppgifter eller fel.", "testButton": "Testa notifikation", "testTitle": "Kilo Code", "testMessage": "Detta är en testnotifikation från <PERSON>."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code vill komprimera din konversation", "condenseConversation": "Komprimera konversation"}}, "newTaskPreview": {"task": "Uppgift"}, "profile": {"title": "Profil", "dashboard": "Instrumentpanel", "logOut": "Logga ut", "currentBalance": "AKTUELLT SALDO", "loading": "Laddar..."}, "docs": "Dokumentation", "rules": {"tooltip": "Hantera Kilo Code Rules och Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "<PERSON><PERSON>", "workflows": "Arbetsflöden"}, "description": {"rules": "Regler låter dig ge Kilo Code instruktioner som den ska följa i alla lägen och för alla prompts. Det är ett beständigt sätt att inkludera kontext och preferenser för alla konversationer i din arbetsyta eller globalt.", "workflows": "Arbetsflöden är en förberedd mall för en konversation. Arbetsflöden kan låta dig definiera prompts du använder ofta, och kan inkludera en serie steg för att guida Kilo Code genom repetitiva uppgifter, som att distribuera en tjänst eller skicka in en PR. För att anropa ett arbetsflöde, skriv", "workflowsInChat": "i chatten."}, "sections": {"globalRules": "<PERSON><PERSON>", "workspaceRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalWorkflows": "Globala arbetsflöden", "workspaceWorkflows": "Arbetsytearbetsflöden"}, "validation": {"invalidFileExtension": "Endast .md, .txt eller ingen filändelse till<PERSON>"}, "placeholders": {"workflowName": "workflow-name (.md, .txt eller ingen ändelse)", "ruleName": "rule-name (.md, .txt eller ingen ändelse)"}, "newFile": {"newWorkflowFile": "Ny arbetsflödesfil...", "newRuleFile": "<PERSON>y regelfil..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Visa {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "webbläsarstart", "browser_action_result": "webbläsaråtgärdsresultat", "browser_action": "webblä<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkpoint_saved": "kontrollpunkt sparad", "command": "kommandokörning", "command_output": "kommandoutdata", "completion_result": "slutföranderesultat", "condense_context": "komprimera kontext", "error": "felmeddela<PERSON>", "followup": "uppföljningsfråga", "mcp_server_response": "MCP-serversvar", "reasoning": "AI-resonemang", "text": "AI-svar", "tool": "verktygsanvändning", "unknown": "okänd meddelandetyp", "use_mcp_server": "MCP-serveranvändning", "user": "användarfeedback"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "restoreAndSend": "Återställ & Skicka"}, "ideaSuggestionsBox": {"newHere": "<PERSON><PERSON> här?", "suggestionText": "<suggestionButton><PERSON><PERSON><PERSON> hä<PERSON></suggestionButton> för en cool idé, tryck sedan på <sendIcon /> och titta på mig göra magi!", "ideas": {"idea1": "Skapa en roterande, glödande 3D-sfär som reagerar på musrörelser. Få appen att fungera i webbläsaren.", "idea2": "Skapa en portföljwebbplats för en Python-mjukvaruutvecklare", "idea3": "Skapa en finansiell app-mockup i webbläsaren. Testa sedan om den fungerar", "idea4": "Skapa en katalogwebbplats som innehåller de bästa AI-videogenereringsmodellerna just nu", "idea5": "Skapa en CSS-gradientgenerator som exporterar anpassade stilmallar och visar en liveförhandsvisning", "idea6": "Generera kort som vackert avslöjar dynamiskt innehåll när du hovrar över dem", "idea7": "Skapa ett lugnande interaktivt stjärnfält som rör sig med musgester"}}}