{"title": "Kilo Code Marketplace", "tabs": {"installed": "Geïnstalleerd", "settings": "Instellingen", "browse": "<PERSON><PERSON>"}, "done": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "filters": {"search": {"placeholder": "Marketplace-items zoeken...", "placeholderMcp": "MCP's zoeken...", "placeholderMode": "<PERSON><PERSON>..."}, "type": {"label": "Filteren op type:", "all": "Alle types", "mode": "Modus", "mcpServer": "MCP-server"}, "sort": {"label": "Sorteren op:", "name": "<PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON>", "lastUpdated": "Laatst bijgewerkt"}, "tags": {"label": "Filteren op tags:", "clear": "Tags wissen", "placeholder": "Type om tags te zoeken en selecteren...", "noResults": "<PERSON><PERSON> gevonden", "selected": "Items tonen met een van de g<PERSON> tags", "clickToFilter": "Klik op tags om items te filteren"}, "none": "<PERSON><PERSON>"}, "type-group": {"modes": "<PERSON><PERSON>", "mcps": "MCP-servers"}, "items": {"empty": {"noItems": "Geen Marketplace-items gevonden", "withFilters": "<PERSON><PERSON><PERSON> je filters aan te passen", "noSources": "<PERSON><PERSON>r een bron toe te voegen in het Bronnen-tabblad", "adjustFilters": "<PERSON><PERSON>r je filters of zoektermen aan te passen", "clearAllFilters": "Alle filters wissen"}, "count": "{{count}} items gevonden", "components": "{{count}} componenten", "matched": "{{count}} g<PERSON><PERSON>", "refresh": {"button": "<PERSON><PERSON><PERSON><PERSON>", "refreshing": "V<PERSON><PERSON><PERSON>...", "mayTakeMoment": "Dit kan even duren."}, "card": {"by": "door {{author}}", "from": "van {{source}}", "install": "Installeren", "installProject": "Installeren", "installGlobal": "Installeren (Globaal)", "remove": "Verwijderen", "removeProject": "Verwijderen", "removeGlobal": "Verwijderen (Globaal)", "viewSource": "Bekijken", "viewOnSource": "Bekijken op {{source}}", "noWorkspaceTooltip": "Open een werkruimte om Marketplace-items te installeren", "installed": "Geïnstalleerd", "removeProjectTooltip": "Verwijderen uit huidig project", "removeGlobalTooltip": "Verwijderen uit globale configuratie", "actionsMenuLabel": "Meer acties"}}, "install": {"title": "{{name}} installeren", "titleMode": "{{name}} modus installeren", "titleMcp": "{{name}} MCP installeren", "scope": "Installatiebereik", "project": "Project (huidige werkruimte)", "global": "Globaal (alle werkruimtes)", "method": "Installatiemethode", "configuration": "Configuratie", "configurationDescription": "Configureer de vereiste parameters voor deze MCP-server", "button": "Installeren", "successTitle": "{{name}} g<PERSON>ï<PERSON>alleerd", "successDescription": "Installatie succesvol voltooid", "installed": "Succesvol geïnstalleerd!", "whatNextMcp": "Je kunt deze MCP-server nu configureren en gebruiken. Klik op het MCP-pictogram in de zijbalk om van tabblad te wisselen.", "whatNextMode": "Je kunt deze modus nu gebruiken. <PERSON><PERSON> op het modi-pictogram in de zijbalk om van tabblad te wisselen.", "done": "<PERSON><PERSON><PERSON>", "goToMcp": "Ga naar MCP-tabblad", "goToModes": "Ga naar Modi-instellingen", "moreInfoMcp": "{{name}} MCP-document<PERSON><PERSON> be<PERSON>en", "validationRequired": "<PERSON>f een waarde op voor {{paramName}}", "prerequisites": "Vereisten"}, "sources": {"title": "Marketplace-bronnen configureren", "description": "Voeg Git-repositories toe die Marketplace-items bevatten. Deze repositories worden opgehaald bij het bladeren door de Marketplace.", "add": {"title": "Nieuwe bron toe<PERSON>n", "urlPlaceholder": "Git-repository URL (bijv. https://github.com/username/repo)", "urlFormats": "Ondersteunde formaten: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git) of Git-protocol (git://github.com/username/repo.git)", "namePlaceholder": "Weergavenaam (max 20 tekens)", "button": "Bron toe<PERSON>n"}, "current": {"title": "<PERSON><PERSON><PERSON> bronnen", "empty": "<PERSON><PERSON> bronnen geconfigureerd. Voeg een bron toe om te beginnen.", "refresh": "<PERSON><PERSON> bron vern<PERSON>wen", "remove": "<PERSON><PERSON> verwijderen"}, "errors": {"emptyUrl": "URL mag niet leeg zijn", "invalidUrl": "Ongeldig URL-formaat", "nonVisibleChars": "URL bevat onzichtbare tekens anders dan spaties", "invalidGitUrl": "URL moet een geldige Git-repository URL zijn (bijv. https://github.com/username/repo)", "duplicateUrl": "Deze URL staat al in de lijst (hoofdletters en spaties genegeerd)", "nameTooLong": "<PERSON><PERSON> moet 20 tekens of minder zijn", "nonVisibleCharsName": "<PERSON>am bevat onzichtbare tekens anders dan spaties", "duplicateName": "Deze naam is al in gebruik (hoofdletters en spaties genegeerd)", "emojiName": "Emoji-tekens kunnen weergaveproblemen veroorzaken", "maxSources": "Maximaal {{max}} bronnen toe<PERSON>an"}}, "footer": {"issueText": "Heb je een probleem gevonden met een marketplace-item of heb je suggesties voor nieuwe items? <0>Open een GitHub issue</0> om het ons te laten weten!"}}