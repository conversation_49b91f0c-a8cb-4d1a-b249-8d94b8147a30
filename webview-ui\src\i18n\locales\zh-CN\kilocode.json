{"welcome": {"greeting": "欢迎使用 Kilo Code！", "introText1": "Kilo Code 是一个免费的开源 AI 编程代理。", "introText2": "它适用于最新的 AI 模型，如 Claude 4 Sonnet、Gemini 2.5 Pro、GPT-4.1 以及 450 多个其他模型。", "introText3": "创建免费账户，获得价值 20 美元的 token，可用于任何 AI 模型。", "ctaButton": "创建免费账户", "manualModeButton": "使用你自己的 API 密钥", "alreadySignedUp": "已经注册了？", "loginText": "在此登录"}, "lowCreditWarning": {"addCredit": "添加额度", "lowBalance": "你的 Kilo Code 余额不足"}, "notifications": {"toolRequest": "工具请求等待批准", "browserAction": "浏览器操作等待批准", "command": "命令等待批准"}, "settings": {"sections": {"mcp": "MCP 服务器"}, "contextManagement": {"allowVeryLargeReads": {"label": "允许超大文件读取", "description": "启用后，Kilo Code 将执行超大文件或 MCP 输出读取，即使存在高概率溢出上下文窗口（内容大小 >80% 的上下文窗口）。"}}, "systemNotifications": {"label": "启用系统通知", "description": "启用后 Kilo Code 将发送系统通知，提醒任务完成或错误等重要事件。", "testButton": "测试通知", "testTitle": "Kilo Code", "testMessage": "这是来自 Kilo Code 的测试通知。"}, "provider": {"account": "Kilo Code 账号", "apiKey": "Kilo Code API 密钥", "login": "登录 Kilo Code", "logout": "退出 Kilo Code"}}, "chat": {"condense": {"wantsToCondense": "Kilo Code 想要压缩你的对话", "condenseConversation": "压缩对话"}}, "newTaskPreview": {"task": "任务"}, "profile": {"title": "个人资料", "dashboard": "仪表板", "logOut": "退出登录", "currentBalance": "当前余额", "loading": "加载中..."}, "docs": "文档", "rules": {"tooltip": "管理 Kilo Code 规则和工作流", "ariaLabel": "Kilo Code 规则", "tabs": {"rules": "规则", "workflows": "工作流"}, "description": {"rules": "规则允许你为 Kilo Code 提供在所有模式和所有提示中都应遵循的指令。它们是在你的工作空间或全局范围内为所有对话包含上下文和偏好的持久方式。", "workflows": "工作流是为对话准备的模板。工作流允许你定义经常使用的提示，并可以包含一系列步骤来指导 Kilo Code 完成重复性任务，如部署服务或提交 PR。要调用工作流，请输入", "workflowsInChat": "在聊天中。"}, "sections": {"globalRules": "全局规则", "workspaceRules": "工作区规则", "globalWorkflows": "全局工作流", "workspaceWorkflows": "工作区工作流"}, "validation": {"invalidFileExtension": "仅允许 .md、.txt 或无文件扩展名"}, "placeholders": {"workflowName": "工作流名称（.md、.txt 或无扩展名）", "ruleName": "规则名称（.md、.txt 或无扩展名）"}, "newFile": {"newWorkflowFile": "新建工作流文件...", "newRuleFile": "新建规则文件..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action": "浏览器操作", "browser_action_result": "浏览器操作结果", "browser_action_launch": "浏览器启动", "command": "命令执行", "checkpoint_saved": "检查点已保存", "completion_result": "完成结果", "command_output": "命令输出", "condense_context": "简化上下文", "followup": "跟进问题", "error": "错误信息", "mcp_server_response": "MCP 服务器响应", "reasoning": "AI 推理", "unknown": "未知消息类型", "text": "AI回复", "tool": "工具使用", "use_mcp_server": "MCP 服务器使用情况", "user": "用户反馈"}, "clickToScroll": "查看{{messageType}} (#{{messageNumber}})"}}, "userFeedback": {"editCancel": "取消", "send": "发送", "restoreAndSend": "恢复并发送"}, "ideaSuggestionsBox": {"newHere": "新用户？", "suggestionText": "<suggestionButton>点击这里</suggestionButton>获取有趣的想法，然后点击 <sendIcon /> 看我施展魔法！", "ideas": {"idea1": "创建一个旋转发光的 3D 球体，能够响应鼠标移动。让应用在浏览器中运行。", "idea2": "为 Python 软件开发者创建作品集网站", "idea3": "在浏览器中创建金融应用模型。然后测试是否可以正常工作", "idea4": "创建一个目录网站，包含目前最好的 AI 视频生成模型", "idea5": "创建一个 CSS 渐变生成器，导出自定义样式表并显示实时预览", "idea6": "生成在鼠标悬停时优雅展示动态内容的卡片", "idea7": "创建一个宁静的交互式星空，跟随鼠标手势移动"}}}