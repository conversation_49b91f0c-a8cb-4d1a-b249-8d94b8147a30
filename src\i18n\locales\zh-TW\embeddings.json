{"unknownError": "未知錯誤", "authenticationFailed": "建立內嵌失敗：驗證失敗。請檢查您的 API 金鑰。", "failedWithStatus": "嘗試 {{attempts}} 次後建立內嵌失敗：HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "嘗試 {{attempts}} 次後建立內嵌失敗：{{errorMessage}}", "failedMaxAttempts": "嘗試 {{attempts}} 次後建立內嵌失敗", "textExceedsTokenLimit": "索引 {{index}} 處的文字超過最大權杖限制 ({{itemTokens}} > {{maxTokens}})。正在略過。", "rateLimitRetry": "已達到速率限制，將在 {{delayMs}} 毫秒後重試（嘗試次數 {{attempt}}/{{maxRetries}}）", "ollama": {"couldNotReadErrorBody": "無法讀取錯誤內容", "requestFailed": "Ollama API 請求失敗，狀態碼 {{status}} {{statusText}}：{{errorBody}}", "invalidResponseStructure": "Ollama API 回應結構無效：未找到 \"embeddings\" 陣列或不是陣列。", "embeddingFailed": "Ollama 內嵌失敗：{{message}}", "serviceNotRunning": "Ollama 服務未在 {{baseUrl}} 執行", "serviceUnavailable": "Ollama 服務不可用（狀態：{{status}}）", "modelNotFound": "找不到 Ollama 模型：{{modelId}}", "modelNotEmbeddingCapable": "Ollama 模型不具備內嵌能力：{{modelId}}", "hostNotFound": "找不到 Ollama 主機：{{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "處理檔案 {{filePath}} 時發生未知錯誤", "unknownErrorDeletingPoints": "刪除 {{filePath}} 的資料點時發生未知錯誤", "failedToProcessBatchWithError": "嘗試 {{maxRetries}} 次後批次處理失敗：{{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "連接 Qdrant 向量資料庫失敗。請確保 Qdrant 正在執行並可在 {{qdrantUrl}} 存取。錯誤：{{errorMessage}}"}, "validation": {"authenticationFailed": "驗證失敗。請在設定中檢查您的 API 金鑰。", "connectionFailed": "連線至內嵌服務失敗。請檢查您的連線設定並確保服務正在執行。", "modelNotAvailable": "指定的模型不可用。請檢查您的模型組態。", "configurationError": "無效的內嵌程式組態。請檢閱您的設定。", "serviceUnavailable": "內嵌服務不可用。請確保它正在執行且可存取。", "invalidEndpoint": "無效的 API 端點。請檢查您的 URL 組態。", "invalidEmbedderConfig": "無效的內嵌程式組態。請檢查您的設定。", "invalidApiKey": "無效的 API 金鑰。請檢查您的 API 金鑰組態。", "invalidBaseUrl": "無效的基礎 URL。請檢查您的 URL 組態。", "invalidModel": "無效的模型。請檢查您的模型組態。", "invalidResponse": "內嵌服務回應無效。請檢查您的組態。"}, "serviceFactory": {"openAiConfigMissing": "建立嵌入器缺少 OpenAI 設定", "ollamaConfigMissing": "建立嵌入器缺少 Ollama 設定", "openAiCompatibleConfigMissing": "建立嵌入器缺少 OpenAI 相容設定", "geminiConfigMissing": "建立嵌入器缺少 Gemini 設定", "invalidEmbedderType": "設定的嵌入器類型無效：{{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "無法確定提供商 '{{provider}}' 的模型 '{{modelId}}' 的向量維度。請確保在 OpenAI 相容提供商設定中正確設定了「嵌入維度」。", "vectorDimensionNotDetermined": "無法確定提供商 '{{provider}}' 的模型 '{{modelId}}' 的向量維度。請檢查模型設定檔或設定。", "qdrantUrlMissing": "建立向量儲存缺少 Qdrant URL", "codeIndexingNotConfigured": "無法建立服務：程式碼索引未正確設定"}}