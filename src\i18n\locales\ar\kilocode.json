{"info": {"settings_imported": "تم استيراد الإعدادات بنجاح."}, "userFeedback": {"message_update_failed": "فشل تحديث الرسالة", "no_checkpoint_found": "ما فيه نقطة حفظ قبل هذي الرسالة", "message_updated": "تم تحديث الرسالة بنجاح"}, "lowCreditWarning": {"title": "تنبيه: الرصيد منخفض!", "message": "تحقق إذا تقدر تشحن برصيد مجاني أو تشتري رصيد إضافي!"}, "notLoggedInError": "لا يمكن إكمال الطلب، تأكد من أنك متصل ومسجل الدخول باستخدام المزود المحدد.\n\n{{error}}", "rules": {"actions": {"delete": "ا<PERSON><PERSON><PERSON>", "confirmDelete": "متأكد إنك تبي تحذف {{filename}}؟", "deleted": "تم حذف {{filename}}"}, "errors": {"noWorkspaceFound": "ما تم العثور على مجلد مشروع", "fileAlreadyExists": "الملف {{filename}} موجود مسبقًا", "failedToCreateRuleFile": "فشل إنشاء ملف القاعدة.", "failedToDeleteRuleFile": "فشل حذف ملف القاعدة."}, "templates": {"workflow": {"description": "وصف سير العمل هنا...", "stepsHeader": "## الخطوات", "step1": "الخطوة 1", "step2": "الخطوة 2"}, "rule": {"description": "وصف القاعدة هنا...", "guidelinesHeader": "## الإرشادات", "guideline1": "إرشاد 1", "guideline2": "إرشاد 2"}}}, "commitMessage": {"activated": "تم تفعيل منشئ رسائل التزام Kilo Code", "gitNotFound": "⚠️ مستودع Git غير موجود أو Git غير متاح", "gitInitError": "⚠️ خطأ في تهيئة Git: {{error}}", "generating": "Kilo: جارٍ إنشاء رسالة الالتزام...", "noChanges": "Kilo: لم يتم العثور على تغييرات للتحليل", "generated": "Kilo: تم إنشاء رسالة الالتزام!", "generationFailed": "Kilo: فشل في إنشاء رسالة الالتزام: {{errorMessage}}", "generatingFromUnstaged": "Kilo: إنشاء رسالة باستخدام التغييرات غير المرحلة", "analyzingChanges": "Kilo: جاري تحليل التعديلات الجاهزة...", "noStagedChangesRepo": "Kilo: ما فيه تعديلات مضافة في مستودع git", "noStagedChanges": "Kilo: ما فيه تعديلات مضافة لتحليلها", "tokenRequired": "يتطلب توليد الرسالة رمز Kilo Code", "activationFailed": "Kilo: فشل تفعيل مولّد الرسائل: {{error}}", "providerRegistered": "Kilo: تم تسجيل موفر رسائل الاعتماد"}, "autocomplete": {"statusBar": {"tooltip": {"basic": "Kilo Code الإكمال التلقائي", "disabled": "إكمال تلقائي لـ Kilo Code (معطل)", "sessionTotal": "التكلفة الإجمالية للجلسة:", "tokenError": "يجب تعيين رمز صالح لاستخدام الإكمال التلقائي", "lastCompletion": "الإكمال الأخير:", "model": "النموذج:"}, "disabled": "$(circle-slash) Kilo إكمال تلقائي", "warning": "$(warning) Kilo إكمال تلقائي", "enabled": "$(sparkle) Kilo إكمال تلقائي", "cost": {"zero": "0.00 دولار", "lessThanCent": "<$0.01"}}, "toggleMessage": "<PERSON><PERSON> <PERSON>كمال تلقائي {{status}}"}, "ghost": {"progress": {"analyzing": "جارِ تحليل الكود الخاص بك...", "showing": "عرض التعديلات المقترحة...", "generating": "جاري توليد التعديلات المقترحة...", "processing": "جارِ معالجة التعديلات المقترحة...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: مهمة سريعة", "placeholder": "مثلاً، 'أعد هيكلة هذه الدالة لتكون أكثر كفاءة'"}, "commands": {"displaySuggestions": "عرض التعديلات المقترحة", "generateSuggestions": "Kilo Code: توليد تعديلات مقترحة", "applyCurrentSuggestion": "تطبيق التعديل المُقترح الحالي", "cancelSuggestions": "إلغاء التعديلات المقترحة", "promptCodeSuggestion": "مهمة سريعة", "applyAllSuggestions": "تطبيق كافة التعديلات المقترحة", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: التعديلات المقترحة"}, "chatParticipant": {"fullName": "Kilo Code وكيل", "description": "يمكنني مساعدتك بالمهام السريعة والتعديلات المقترحة.", "name": "وكيل"}, "messages": {"provideCodeSuggestions": "جارٍ تقديم اقتراحات الرمز..."}}}