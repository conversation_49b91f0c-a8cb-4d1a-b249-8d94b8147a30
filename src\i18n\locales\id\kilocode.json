{"info": {"settings_imported": "Pengat<PERSON><PERSON> berhasil diimpor."}, "userFeedback": {"message_update_failed": "<PERSON><PERSON> me<PERSON> pesan", "no_checkpoint_found": "Tidak ditemukan checkpoint sebelum pesan ini", "message_updated": "<PERSON><PERSON> be<PERSON>"}, "lowCreditWarning": {"title": "Peringatan Kredit Rendah!", "message": "<PERSON><PERSON><PERSON> apakah kamu bisa mengisi ulang dengan kredit gratis atau beli lebih banyak!"}, "notLoggedInError": "Tidak dapat menye<PERSON><PERSON><PERSON> permin<PERSON>, pastikan kamu terhubung dan masuk dengan penyedia yang dipilih.\n\n{{error}}", "rules": {"actions": {"delete": "Hapus", "confirmDelete": "<PERSON><PERSON><PERSON>h kamu yakin ingin menghapus {{filename}}?", "deleted": "{{filename}} te<PERSON> di<PERSON><PERSON>"}, "errors": {"noWorkspaceFound": "Folder workspace tidak ditemukan", "fileAlreadyExists": "File {{filename}} sudah ada", "failedToCreateRuleFile": "<PERSON>l membuat file aturan.", "failedToDeleteRuleFile": "<PERSON><PERSON> file aturan."}, "templates": {"workflow": {"description": "Desk<PERSON><PERSON> alur kerja di sini...", "stepsHeader": "## <PERSON><PERSON><PERSON>-lang<PERSON>h", "step1": "Langkah 1", "step2": "Langkah 2"}, "rule": {"description": "Deskripsi aturan di sini...", "guidelinesHeader": "## Pedoman", "guideline1": "Pedoman 1", "guideline2": "Pedoman 2"}}}, "commitMessage": {"activated": "Generator pesan commit Kilo <PERSON> diaktifkan", "gitNotFound": "⚠️ Repositori Git tidak ditemukan atau git tidak tersedia", "gitInitError": "⚠️ Kesalahan inisialisasi Git: {{error}}", "generating": "<PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON><PERSON> pesan commit...", "noChanges": "Kilo: Tidak ada perubahan yang ditemukan untuk dianalisis", "generated": "<PERSON><PERSON>: <PERSON><PERSON> commit telah dibuat!", "generationFailed": "Kilo: <PERSON><PERSON> pesan commit: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON><PERSON><PERSON><PERSON> pesan menggunakan perubahan yang belum di-stage", "activationFailed": "Kilo: Gagal mengaktifkan pembangkit pesan: {{error}}", "providerRegistered": "Kilo: <PERSON><PERSON><PERSON> pesan commit telah terdaftar"}, "autocomplete": {"statusBar": {"disabled": "$(circle-slash) Kilo Autocomplete", "enabled": "$(sparkle) Kilo Autocomplete", "tooltip": {"basic": "Kilo Code Autocomplete", "disabled": "Kilo Code Autocomplete (dinonaktifkan)", "tokenError": "Token yang valid harus ditetapkan untuk menggunakan autocomplete", "lastCompletion": "Penyelesaian terakhir:", "sessionTotal": "Biaya total sesi:", "model": "Model:"}, "warning": "$(warning) Kilo Autocomplete", "cost": {"zero": "Rp0,00", "lessThanCent": "<Rp0,01"}}, "toggleMessage": "Kilo Autocomplete {{status}}"}, "ghost": {"progress": {"analyzing": "Mengan<PERSON><PERSON> kode <PERSON>...", "generating": "Membuat saran pengeditan...", "processing": "Memproses saran pengeditan...", "showing": "Menampilkan saran pengeditan...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Tugas <PERSON>", "placeholder": "misalnya, 'refaktor fungsi ini agar lebih efisien'"}, "commands": {"cancelSuggestions": "Batalkan Saran Pengeditan", "applyCurrentSuggestion": "Terapkan Saran Pengeditan Saat Ini", "generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON> Penyunting<PERSON>", "applyAllSuggestions": "Te<PERSON><PERSON> yang <PERSON>", "displaySuggestions": "<PERSON><PERSON><PERSON><PERSON> Pengedita<PERSON>", "promptCodeSuggestion": "Tugas Cepat Prompt", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: <PERSON><PERSON>"}, "chatParticipant": {"fullName": "Kilo Code Agen", "name": "Agen", "description": "<PERSON>a dapat membantu Anda dengan tugas cepat dan saran pengeditan."}, "messages": {"provideCodeSuggestions": "Memberikan saran kode..."}}}