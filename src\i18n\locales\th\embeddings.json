{"unknownError": "ข้อผิดพลาดที่ไม่รู้จัก", "authenticationFailed": "ล้มเหลวในการสร้าง embeddings: การยืนยันตัวตนล้มเหลว กรุณาตรวจสอบ API key ของคุณ", "failedWithStatus": "ล้มเหลวในการสร้าง embeddings หลังจาก {{attempts}} ครั้ง: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "ล้มเหลวในการสร้าง embeddings หลังจาก {{attempts}} ครั้ง: {{errorMessage}}", "failedMaxAttempts": "ล้มเหลวในการสร้าง embeddings หลังจาก {{attempts}} ครั้ง", "textExceedsTokenLimit": "ข้อความที่ดัชนี {{index}} เกินขีดจำกัด token สูงสุด ({{itemTokens}} > {{maxTokens}}) ข้าม", "rateLimitRetry": "ถึงขีดจำกัดอัตรา จะลองใหม่ใน {{delayMs}}ms (ความพยายาม {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "ไม่สามารถอ่าน error body", "requestFailed": "คำขอ Ollama API ล้มเหลวด้วยสถานะ {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "โครงสร้างการตอบสนองไม่ถูกต้องจาก Ollama API: ไม่พบ \"embeddings\" array หรือไม่ใช่ array", "embeddingFailed": "Ollama embedding ล้มเหลว: {{message}}", "serviceNotRunning": "บริการ Ollama ไม่ทำงานที่ {{baseUrl}}", "serviceUnavailable": "บริการ Ollama ไม่พร้อมใช้งาน (สถานะ: {{status}})", "modelNotFound": "ไม่พบโมเดล Ollama: {{modelId}}", "modelNotEmbeddingCapable": "โมเดล Ollama ไม่สามารถทำ embedding ได้: {{modelId}}", "hostNotFound": "ไม่พบโฮสต์ Ollama: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "ข้อผิดพลาดที่ไม่รู้จักในการประมวลผลไฟล์ {{filePath}}", "unknownErrorDeletingPoints": "ข้อผิดพลาดที่ไม่รู้จักในการลบจุดสำหรับ {{filePath}}", "failedToProcessBatchWithError": "ล้มเหลวในการประมวลผลแบทช์หลังจาก {{maxRetries}} ครั้ง: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "ล้มเหลวในการเชื่อมต่อกับฐานข้อมูล Qdrant vector กรุณาตรวจสอบให้แน่ใจว่า Qdrant กำลังทำงานและสามารถเข้าถึงได้ที่ {{qdrantUrl}} ข้อผิดพลาด: {{errorMessage}}"}, "validation": {"authenticationFailed": "การยืนยันตัวตนล้มเหลว กรุณาตรวจสอบ API key ของคุณในการตั้งค่า", "connectionFailed": "ล้มเหลวในการเชื่อมต่อกับบริการ embedder กรุณาตรวจสอบการตั้งค่าการเชื่อมต่อและตรวจสอบให้แน่ใจว่าบริการกำลังทำงาน", "modelNotAvailable": "โมเดลที่ระบุไม่พร้อมใช้งาน กรุณาตรวจสอบการกำหนดค่าโมเดลของคุณ", "configurationError": "การกำหนดค่า embedder ไม่ถูกต้อง กรุณาตรวจสอบการตั้งค่าของคุณ", "serviceUnavailable": "บริการ embedder ไม่พร้อมใช้งาน กรุณาตรวจสอบให้แน่ใจว่ากำลังทำงานและสามารถเข้าถึงได้", "invalidEndpoint": "API endpoint ไม่ถูกต้อง กรุณาตรวจสอบการกำหนดค่า URL ของคุณ", "invalidEmbedderConfig": "การกำหนดค่า embedder ไม่ถูกต้อง กรุณาตรวจสอบการตั้งค่าของคุณ", "invalidApiKey": "API key ไม่ถูกต้อง กรุณาตรวจสอบการกำหนดค่า API key ของคุณ", "invalidBaseUrl": "Base URL ไม่ถูกต้อง กรุณาตรวจสอบการกำหนดค่า URL ของคุณ", "invalidModel": "โมเดลไม่ถูกต้อง กรุณาตรวจสอบการกำหนดค่าโมเดลของคุณ", "invalidResponse": "การตอบสนองไม่ถูกต้องจากบริการ embedder กรุณาตรวจสอบการกำหนดค่าของคุณ"}, "serviceFactory": {"openAiConfigMissing": "การกำหนดค่า OpenAI หายไปสำหรับการสร้าง embedder", "ollamaConfigMissing": "การกำหนดค่า Ollama หายไปสำหรับการสร้าง embedder", "openAiCompatibleConfigMissing": "การกำหนดค่า OpenAI Compatible หายไปสำหรับการสร้าง embedder", "geminiConfigMissing": "การกำหนดค่า Gemini หายไปสำหรับการสร้าง embedder", "invalidEmbedderType": "ประเภท embedder ที่กำหนดค่าไม่ถูกต้อง: {{embedder<PERSON><PERSON>ider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "ไม่สามารถกำหนดมิติเวกเตอร์สำหรับโมเดล '{{modelId}}' กับผู้ให้บริการ '{{provider}}' กรุณาตรวจสอบให้แน่ใจว่า 'Embedding Dimension' ถูกตั้งค่าอย่างถูกต้องในการตั้งค่าผู้ให้บริการ OpenAI-Compatible", "vectorDimensionNotDetermined": "ไม่สามารถกำหนดมิติเวกเตอร์สำหรับโมเดล '{{modelId}}' กับผู้ให้บริการ '{{provider}}' ตรวจสอบโปรไฟล์โมเดลหรือการกำหนดค่า", "qdrantUrlMissing": "Qdrant URL หายไปสำหรับการสร้าง vector store", "codeIndexingNotConfigured": "ไม่สามารถสร้างบริการ: การจัดทำดัชนีโค้ดไม่ได้กำหนดค่าอย่างถูกต้อง"}}