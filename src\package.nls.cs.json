{"extension.displayName": "Kilo Code AI Agent (k<PERSON><PERSON><PERSON><PERSON> / Roo)", "extension.description": "Open Source AI asistent pro kódování pro plánování, vytváření a opravy kódu.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "Nový úkol", "command.mcpServers.title": "MCP servery", "command.prompts.title": "Re<PERSON><PERSON><PERSON>", "command.history.title": "Historie", "command.marketplace.title": "Tržiště", "command.openInEditor.title": "Otevřít v editoru", "command.settings.title": "Nastavení", "command.documentation.title": "Dokumentace", "command.openInNewTab.title": "Otevřít v nové kartě", "command.explainCode.title": "Vysvětlit kód", "command.fixCode.title": "<PERSON>ra<PERSON><PERSON> k<PERSON>", "command.improveCode.title": "<PERSON>ylep<PERSON><PERSON> kód", "command.addToContext.title": "Přidat do kontextu", "command.focusInput.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> vs<PERSON><PERSON><PERSON><PERSON> pole", "command.setCustomStoragePath.title": "Nastavit vlastní cestu <PERSON>tě", "command.terminal.addToContext.title": "Přidat obsah terminálu do kontextu", "command.terminal.fixCommand.title": "Opravit tento příkaz", "command.terminal.explainCommand.title": "Vysvětlit tento příkaz", "command.acceptInput.title": "Přijmout vstup/návrh", "command.generateCommitMessage.title": "Vygenerovat zprávu commitu pomocí <PERSON>", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> mohou být <PERSON>ky spuštěny, k<PERSON><PERSON> je povoleno 'Always approve execute operations'", "settings.vsCodeLmModelSelector.description": "Nastavení pro VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Dodavatel jazykového modelu (např. copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON> (např. gpt-4)", "settings.customStoragePath.description": "Vlastní cesta úložiště. Ponechte prázdné pro použití výchozího umístění. Podporuje absolutní cesty (např. 'D:\\KiloCodeStorage')", "command.importSettings.title": "Importovat nastavení", "settings.enableCodeActions.description": "Povolit rychlé opravy Kilo Code", "settings.autoImportSettingsPath.description": "Cesta k konfiguračnímu souboru Kilo Code pro automatický import při spuštění rozšíření. Podporuje absolutní cesty a cesty relativní k domovskému adresáři (např. '~/Documents/kilo-code-settings.json'). Ponechte prázdné pro zakázání automatického importu.", "ghost.input.title": "Stiskněte 'Enter' pro potvrzení nebo 'Escape' pro zrušení", "ghost.input.placeholder": "<PERSON><PERSON><PERSON><PERSON>, co chcete udělat...", "ghost.commands.generateSuggestions": "Kilo Code: Generovat Navrhovan<PERSON>", "ghost.commands.displaySuggestions": "Zobrazit <PERSON><PERSON>", "ghost.commands.cancelSuggestions": "<PERSON><PERSON><PERSON><PERSON>", "ghost.commands.applyCurrentSuggestion": "Použít Aktuální Navrženou Úpravu", "ghost.commands.applyAllSuggestions": "Použít Všechny Navrhované <PERSON>", "ghost.commands.promptCodeSuggestion": "Rychlý Úkol", "ghost.commands.goToNextSuggestion": "Přejít na Další Návrh", "ghost.commands.goToPreviousSuggestion": "Přejít na Předchozí Návrh"}