{"answers": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "cancel": "İptal", "remove": "Kaldır", "keep": "<PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON>", "description": "Geri bildirimlerinizi duymak veya yaşadığınız sorunlarda size yardımcı olmak isteriz.", "githubIssues": "<PERSON><PERSON><PERSON><PERSON>'da bir sorun bildirin", "githubDiscussions": "GitHub tartışmalarına katılın", "discord": "Discord topluluğumuza katılın", "customerSupport": "Müşteri Desteği"}, "ui": {"search_placeholder": "Ara..."}, "mermaid": {"loading": "Mermaid diyagramı oluşturuluyor...", "render_error": "Diyagram render edilemiyor", "fixing_syntax": "Mermaid s<PERSON>z<PERSON><PERSON><PERSON> dü<PERSON>...", "fix_syntax_button": "Sözdizimini AI ile düzelt", "original_code": "Orijinal kod:", "errors": {"unknown_syntax": "Bilinmeyen sözdizimi hatası", "fix_timeout": "LLM düzeltme isteği zaman aşımına uğradı", "fix_failed": "LLM düzeltme başarısız oldu", "fix_attempts": "{{attempts}} denemeden sonra sözdizimi dü<PERSON>. Son hata: {{error}}", "no_fix_provided": "LLM düzeltme sağlayamadı", "fix_request_failed": "Düzeltme isteği başarısız oldu"}, "buttons": {"zoom": "Yakınlaştır", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "Kopyala", "save": "<PERSON><PERSON><PERSON>", "viewCode": "<PERSON><PERSON>", "viewDiagram": "Diyagramı görüntüle", "close": "Ka<PERSON><PERSON>"}, "modal": {"codeTitle": "Mermaid Kodu"}, "tabs": {"diagram": "Diyagram", "code": "Kod"}, "feedback": {"imageCopied": "Görsel panoya kopyalandı", "copyError": "G<PERSON><PERSON>l kopyalama hatası"}}, "file": {"errors": {"invalidDataUri": "Geçersiz veri URI formatı", "copyingImage": "<PERSON><PERSON><PERSON><PERSON> kopyalama hatası: {{error}}", "openingImage": "<PERSON><PERSON><PERSON><PERSON> açma hatası: {{error}}", "pathNotExists": "Yol mevcut değil: {{path}}", "couldNotOpen": "<PERSON><PERSON><PERSON> a<PERSON>adı: {{error}}", "couldNotOpenGeneric": "Dosya açılamadı!"}, "success": {"imageDataUriCopied": "Görsel veri URI'si panoya kopyalandı"}}}