{"title": "Kilo Code Marketplace", "tabs": {"installed": "Installerade", "settings": "Inställningar", "browse": "Bläddra"}, "done": "<PERSON><PERSON>", "refresh": "Uppdatera", "filters": {"search": {"placeholder": "Sök marketplace-objekt...", "placeholderMcp": "Sök MCP:er...", "placeholderMode": "Sök Modes..."}, "type": {"label": "Filtrera efter typ:", "all": "Alla typer", "mode": "Mode", "mcpServer": "MCP Server"}, "sort": {"label": "Sortera efter:", "name": "<PERSON><PERSON>", "author": "Författare", "lastUpdated": "Senast uppdaterad"}, "tags": {"label": "Filtrera efter taggar:", "clear": "<PERSON><PERSON>", "placeholder": "Skriv för att söka och välja taggar...", "noResults": "Inga matchande taggar hittades", "selected": "Visar objekt med någon av de valda taggarna", "clickToFilter": "<PERSON>lick<PERSON> på taggar för att filtrera objekt"}, "none": "Inga"}, "type-group": {"modes": "Modes", "mcps": "MCP-servrar"}, "items": {"empty": {"noItems": "Inga marketplace-objekt hittades", "withFilters": "Försök justera dina filter", "noSources": "Försök lägga till en källa i fliken Källor", "adjustFilters": "Försök justera dina filter eller sö<PERSON>mer", "clearAllFilters": "Rensa alla filter"}, "count": "{{count}} objekt hittades", "components": "{{count}} komponenter", "matched": "{{count}} matchade", "refresh": {"button": "Uppdatera", "refreshing": "Uppdaterar...", "mayTakeMoment": "<PERSON>ta kan ta en stund."}, "card": {"by": "av {{author}}", "from": "f<PERSON><PERSON><PERSON> {{source}}", "install": "Installera", "installProject": "Installera", "installGlobal": "Installera (Globalt)", "remove": "<PERSON> bort", "removeProject": "<PERSON> bort", "removeGlobal": "<PERSON> bort (Globalt)", "viewSource": "Visa", "viewOnSource": "Visa på {{source}}", "noWorkspaceTooltip": "Öppna en arbetsyta för att installera marketplace-objekt", "installed": "Installerad", "removeProjectTooltip": "Ta bort från aktuellt projekt", "removeGlobalTooltip": "Ta bort från global konfiguration", "actionsMenuLabel": "Fler åtgärder"}}, "install": {"title": "Installera {{name}}", "titleMode": "Installera {{name}} Mode", "titleMcp": "Installera {{name}} MCP", "scope": "Installationsomfång", "project": "Projekt (aktuell arbetsyta)", "global": "Globalt (alla arbetsytor)", "method": "Installationsmetod", "prerequisites": "Förutsättningar", "configuration": "Konfiguration", "configurationDescription": "Konfigurera parametrarna som krävs för denna MCP-server", "button": "Installera", "successTitle": "{{name}} installerad", "successDescription": "Installationen slutfördes framgångsrikt", "installed": "Installationen lyckades!", "whatNextMcp": "Du kan nu konfigurera och använda denna MCP-server. Klicka på MCP-ikonen i sidofältet för att byta flik.", "whatNextMode": "Du kan nu använda detta läge. Klicka på Modes-ikonen i sidofältet för att byta flik.", "done": "<PERSON><PERSON>", "goToMcp": "Gå till MCP-fliken", "goToModes": "Gå till Modes-fliken", "moreInfoMcp": "Visa {{name}} MCP-dokumentation", "validationRequired": "Vänligen ange ett värde för {{paramName}}"}, "sources": {"title": "Konfigurera Marketplace-källor", "description": "Lägg till Git-repositorier som innehåller marketplace-objekt. Dessa repositorier kommer att hämtas när du bläddrar i marketplace.", "add": {"title": "Lägg till ny källa", "urlPlaceholder": "Git repository URL (t.ex. https://github.com/username/repo)", "urlFormats": "Stödda format: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git), eller Git-protokoll (git://github.com/username/repo.git)", "namePlaceholder": "Visningsnamn (max 20 tecken)", "button": "Lägg till källa"}, "current": {"title": "Aktuella k<PERSON>llor", "empty": "Inga källor konfigurerade. Lägg till en källa för att komma igång.", "refresh": "<PERSON><PERSON><PERSON><PERSON> denna k<PERSON>", "remove": "<PERSON> bort källa"}, "errors": {"emptyUrl": "URL kan inte vara tom", "invalidUrl": "Ogiltigt URL-format", "nonVisibleChars": "URL innehåller icke-synliga tecken förutom mellanslag", "invalidGitUrl": "URL måste vara en giltig Git repository URL (t.ex. https://github.com/username/repo)", "duplicateUrl": "Denna URL finns redan i listan (skiftläges- och mellanslagsokänslig matchning)", "nameTooLong": "Namnet måste vara 20 tecken eller mindre", "nonVisibleCharsName": "Namnet innehåller icke-synliga tecken förutom mellanslag", "duplicateName": "Detta namn används redan (skiftläges- och mellanslagsokänslig matchning)", "emojiName": "Emoji-tecken kan orsaka visningsproblem", "maxSources": "Maximalt {{max}} k<PERSON><PERSON><PERSON>"}}, "footer": {"issueText": "Hittade du ett problem med ett marketplace-objekt eller har förslag på nya? <0>Öppna ett GitHub-ärende</0> för att meddela oss!"}}