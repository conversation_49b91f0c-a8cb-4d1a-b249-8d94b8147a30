{"answers": {"yes": "Sì", "no": "No", "cancel": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "<PERSON><PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>i piacerebbe ricevere il tuo feedback o aiutarti con qualsiasi problema che stai riscontrando.", "githubIssues": "Segnala un problema su GitHub", "githubDiscussions": "Partecipa alle discussioni su GitHub", "discord": "Unisciti alla nostra comunità Discord", "customerSupport": "Supporto <PERSON>i"}, "ui": {"search_placeholder": "Cerca..."}, "mermaid": {"loading": "Generazione del diagramma mermaid...", "render_error": "Impossibile renderizzare il diagramma", "fixing_syntax": "Correzione della sintassi Mermaid...", "fix_syntax_button": "Correggi sintassi con AI", "original_code": "Codice originale:", "errors": {"unknown_syntax": "Errore di sintassi sconosciuto", "fix_timeout": "Richiesta di correzione LLM scaduta", "fix_failed": "Correzione LLM fallita", "fix_attempts": "Impossibile correggere la sintassi dopo {{attempts}} tentativi. Ultimo errore: {{error}}", "no_fix_provided": "LLM non è riuscito a fornire una correzione", "fix_request_failed": "Richiesta di correzione fallita"}, "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copia", "save": "<PERSON>va immagine", "viewCode": "Visualizza codice", "viewDiagram": "Visualizza diagramma", "close": "<PERSON><PERSON>"}, "modal": {"codeTitle": "Codice Mermaid"}, "tabs": {"diagram": "Di<PERSON>ram<PERSON>", "code": "Codice"}, "feedback": {"imageCopied": "Immagine copiata negli appunti", "copyError": "Errore nella copia dell'immagine"}}, "file": {"errors": {"invalidDataUri": "Formato URI dati non valido", "copyingImage": "Errore nella copia dell'immagine: {{error}}", "openingImage": "Errore nell'apertura dell'immagine: {{error}}", "pathNotExists": "Il percorso non esiste: {{path}}", "couldNotOpen": "Impossibile aprire il file: {{error}}", "couldNotOpenGeneric": "Impossibile aprire il file!"}, "success": {"imageDataUriCopied": "URI dati immagine copiato negli appunti"}}}