{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "コードの計画、構築、修正のためのオープンソース AI コーディング アシスタント。", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "views.activitybar.title": "Kilo Code", "command.newTask.title": "新しいタスク", "command.mcpServers.title": "MCPサーバー", "command.prompts.title": "モード", "command.history.title": "履歴", "command.marketplace.title": "マーケットプレイス", "command.openInEditor.title": "エディタで開く", "command.settings.title": "設定", "command.documentation.title": "ドキュメント", "command.openInNewTab.title": "新しいタブで開く", "command.explainCode.title": "コードの説明", "command.fixCode.title": "コードの修正", "command.improveCode.title": "コードの改善", "command.addToContext.title": "コンテキストに追加", "command.focusInput.title": "入力フィールドにフォーカス", "command.setCustomStoragePath.title": "カスタムストレージパスの設定", "command.importSettings.title": "設定をインポート", "command.terminal.addToContext.title": "ターミナルの内容をコンテキストに追加", "command.terminal.fixCommand.title": "このコマンドを修正", "command.terminal.explainCommand.title": "このコマンドを説明", "command.acceptInput.title": "入力/提案を承認", "command.generateCommitMessage.title": "Kiloでコミットメッセージを生成", "command.profile.title": "プロフィール", "configuration.title": "Kilo Code", "ghost.input.title": "Kilo Code ゴーストライター", "ghost.input.placeholder": "コーディングしたい内容を説明してください...", "ghost.commands.generateSuggestions": "Kilo Code: 提案された編集を生成", "ghost.commands.displaySuggestions": "提案された編集を表示", "ghost.commands.cancelSuggestions": "提案された編集をキャンセル", "ghost.commands.applyCurrentSuggestion": "現在の提案された編集を適用", "ghost.commands.applyAllSuggestions": "すべての提案された編集を適用", "ghost.commands.promptCodeSuggestion": "クイックタスク", "ghost.commands.goToNextSuggestion": "次の提案に移動", "ghost.commands.goToPreviousSuggestion": "前の提案に移動", "commands.allowedCommands.description": "'常に実行操作を承認する'が有効な場合に自動実行できるコマンド", "settings.vsCodeLmModelSelector.description": "VSCode 言語モデル API の設定", "settings.vsCodeLmModelSelector.vendor.description": "言語モデルのベンダー（例：copilot）", "settings.vsCodeLmModelSelector.family.description": "言語モデルのファミリー（例：gpt-4）", "settings.customStoragePath.description": "カスタムストレージパス。デフォルトの場所を使用する場合は空のままにします。絶対パスをサポートします（例：'D:\\KiloCodeStorage'）", "settings.enableCodeActions.description": "Ki<PERSON> Codeのクイック修正を有効にする。", "settings.autoImportSettingsPath.description": "拡張機能の起動時に自動的にインポートするKilo Code設定ファイルへのパス。絶対パスとホームディレクトリからの相対パスをサポートします（例：'~/Documents/kilo-code-settings.json'）。自動インポートを無効にするには、空のままにします。"}