{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Asystent kodowania AI o otwartym kodzie źródłowym do planowania, tworzenia i naprawiania kodu.", "command.newTask.title": "Nowe Zadanie", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "Ulepsz Kod", "command.addToContext.title": "Dodaj do Kontekstu", "command.openInNewTab.title": "Otwórz w Nowej Karcie", "command.focusInput.title": "Fokus na Pole Wprowadzania", "command.setCustomStoragePath.title": "Ustaw Niestandardową Ścieżkę Przechowywania", "command.importSettings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.terminal.addToContext.title": "Dodaj <PERSON>ć Terminala do Kontekstu", "command.terminal.fixCommand.title": "Napraw tę <PERSON>", "command.terminal.explainCommand.title": "Wyjaśnij tę Komendę", "command.acceptInput.title": "Akceptuj Wprowadzanie/Sugestię", "command.generateCommitMessage.title": "<PERSON><PERSON><PERSON> w<PERSON> z Kilo", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Serwery MCP", "command.prompts.title": "Tryby", "command.history.title": "Historia", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Otwórz w Edytorze", "command.settings.title": "Ustawienia", "command.documentation.title": "Dokumentacja", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Polecenia, które mogą być wykonywane automatycznie, gdy włączona jest opcja '<PERSON><PERSON><PERSON> zatwierdzaj operacje wykonania'", "settings.vsCodeLmModelSelector.description": "Ustawienia dla API modelu językowego VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Dostawca modelu językowego (np. copilot)", "settings.vsCodeLmModelSelector.family.description": "Rodzina modelu językowego (np. gpt-4)", "settings.customStoragePath.description": "Niestandardowa ścieżka przechowywania. Pozostaw puste, aby użyć domyślnej lokalizacji. Obsługuje ścieżki bezwzględne (np. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Włącz szybkie poprawki Kilo Code.", "settings.autoImportSettingsPath.description": "Ścieżka do pliku konfiguracyjnego Kilo Code, który ma być automatycznie importowany podczas uruchamiania rozszerzenia. Obsługuje ścieżki bezwzględne i ścieżki względne do katalogu domowego (np. '~/Documents/kilo-code-settings.json'). Pozostaw puste, aby wyłączyć automatyczne importowanie.", "ghost.input.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>' aby pot<PERSON>ć lub '<PERSON>' aby anulo<PERSON>ć", "ghost.input.placeholder": "Opisz co chcesz zrobić...", "ghost.commands.generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON>", "ghost.commands.displaySuggestions": "Wyświetl Sugestie Edycji", "ghost.commands.cancelSuggestions": "<PERSON><PERSON><PERSON> Sugestie Edycji", "ghost.commands.applyCurrentSuggestion": "Zastosuj <PERSON>ą Sugestię Edycji", "ghost.commands.applyAllSuggestions": "Zastosuj Wszystkie Sugestie Edycji", "ghost.commands.promptCodeSuggestion": "Szybkie Zadanie", "ghost.commands.goToNextSuggestion": "Przejdź do Następnej Sugestii", "ghost.commands.goToPreviousSuggestion": "Przejdź do Poprzedniej Sugestii"}