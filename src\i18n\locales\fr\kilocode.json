{"info": {"settings_imported": "Paramètres importés avec succès."}, "userFeedback": {"message_update_failed": "Échec de la mise à jour du message", "no_checkpoint_found": "Aucun point de contrôle trouvé avant ce message", "message_updated": "Message mis à jour avec succès"}, "lowCreditWarning": {"title": "Avertissement de Crédit Faible !", "message": "Vérifiez si vous pouvez recharger avec des crédits gratuits ou en acheter davantage !"}, "notLoggedInError": "Impossible de traiter la demande, veuillez vous assurer que vous êtes connecté et identifié auprès du fournisseur sélectionné.\n\n{{error}}", "rules": {"actions": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "Êtes-vous sûr de vouloir supprimer {{filename}} ?", "deleted": "{{filename}} supprimé"}, "errors": {"noWorkspaceFound": "Aucun dossier d'espace de travail trouvé", "fileAlreadyExists": "Le fichier {{filename}} existe déjà", "failedToCreateRuleFile": "Échec de la création du fichier de règle.", "failedToDeleteRuleFile": "Échec de la suppression du fichier de règle."}, "templates": {"workflow": {"description": "Description du flux de travail ici...", "stepsHeader": "## Étapes", "step1": "Étape 1", "step2": "Étape 2"}, "rule": {"description": "Description de la règle ici...", "guidelinesHeader": "## Directives", "guideline1": "Directive 1", "guideline2": "Directive 2"}}}, "commitMessage": {"activated": "Générateur de message de commit Kilo Code activé", "gitNotFound": "⚠️ Dép<PERSON>t Git introuvable ou git non disponible", "gitInitError": "⚠️ Erreur d'initialisation Git : {{error}}", "generating": "Kilo : Génération du message de commit...", "noChanges": "Kilo : Aucune modification trouvée à analyser", "generated": "<PERSON><PERSON> : Message de commit généré !", "generationFailed": "Kilo: Échec de génération du message de commit : {{errorMessage}}", "generatingFromUnstaged": "Kilo : Génération du message à partir des modifications non indexées", "providerRegistered": "Kilo: Fournisseur de messages de commit enregistré", "activationFailed": "Kilo : Échec d'activation du générateur de messages : {{error}}"}, "autocomplete": {"statusBar": {"disabled": "$(circle-slash) Kilo Autocomplétion", "enabled": "$(sparkle) Kilo Autocomplétion", "warning": "$(warning) Kilo Autocomplétion", "tooltip": {"disabled": "Autocomplétion Kilo Code (désactivée)", "tokenError": "Un jeton valide doit être défini pour utiliser l'autocomplétion", "sessionTotal": "Coût total de la séance :", "basic": "Autocomplétion Kilo Code", "lastCompletion": "Der<PERSON>ère complétion :", "model": "Mod<PERSON><PERSON> :"}, "cost": {"lessThanCent": "<0,01 $", "zero": "0,00 $"}}, "toggleMessage": "Kilo Autocomplétion {{status}}"}, "ghost": {"progress": {"generating": "Génération de suggestions de modifications...", "processing": "Traitement des modifications suggérées...", "analyzing": "Analyse de votre code en cours...", "showing": "Affichage des modifications suggérées...", "title": "Kilo Code"}, "input": {"title": "Kilo Code : Tâche Rapide", "placeholder": "par ex., 'refactorisez cette fonction pour la rendre plus efficace'"}, "commands": {"generateSuggestions": "Kilo Code : Gén<PERSON>rer des suggestions de modifications", "displaySuggestions": "Afficher les modifications suggérées", "applyCurrentSuggestion": "Appliquer la modification suggérée actuelle", "applyAllSuggestions": "Appliquer toutes les modifications suggérées", "cancelSuggestions": "Annuler les modifications suggérées", "promptCodeSuggestion": "Tâche Rapide d'Invite", "category": "Kilo Code"}, "chatParticipant": {"name": "Agent", "fullName": "Agent <PERSON><PERSON>", "description": "Je peux vous aider avec des tâches rapides et des modifications suggérées."}, "codeAction": {"title": "Kilo Code : Modifications suggérées"}, "messages": {"provideCodeSuggestions": "Suggestions de code en cours..."}}}