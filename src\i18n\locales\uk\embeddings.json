{"unknownError": "Невідома помилка", "authenticationFailed": "Не вдалося створити embeddings: Автентифікація не вдалася. Будь ласка, перевір свій API ключ.", "failedWithStatus": "Не вдалося створити embeddings після {{attempts}} спроб: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Не вдалося створити embeddings після {{attempts}} спроб: {{errorMessage}}", "failedMaxAttempts": "Не вдалося створити embeddings після {{attempts}} спроб", "textExceedsTokenLimit": "Текст за індексом {{index}} перевищує максимальний ліміт токенів ({{itemTokens}} > {{maxTokens}}). Пропускаємо.", "rateLimitRetry": "Досягнуто ліміт швидкості, повторна спроба через {{delayMs}}мс (спроба {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Не вдалося прочитати тіло помилки", "requestFailed": "Запит Ollama API не вдався зі статусом {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Нед<PERSON><PERSON>сна структура відповіді від Ollama API: масив \"embeddings\" не знайдено або це не масив.", "embeddingFailed": "<PERSON>llama embedding не вдалося: {{message}}", "serviceNotRunning": "Сер<PERSON><PERSON><PERSON> Ollama не працює за адресою {{baseUrl}}", "serviceUnavailable": "Серв<PERSON><PERSON> Ollama недоступний (статус: {{status}})", "modelNotFound": "Мод<PERSON><PERSON>ь Ollama не знайдено: {{modelId}}", "modelNotEmbeddingCapable": "Мо<PERSON><PERSON><PERSON>ь Ollama не підтримує embedding: {{modelId}}", "hostNotFound": "<PERSON>о<PERSON>т Ollama не знайдено: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Невідома помилка при обробці файлу {{filePath}}", "unknownErrorDeletingPoints": "Невідома помилка при видаленні точок для {{filePath}}", "failedToProcessBatchWithError": "Не вдалося обробити пакет після {{maxRetries}} спроб: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Не вдалося підключитися до векторної бази даних Qdrant. Будь ласка, переконайся, що Qdrant працює і доступний за адресою {{qdrantUrl}}. Помилка: {{errorMessage}}"}, "validation": {"authenticationFailed": "Автентифікація не вдалася. Будь ласка, перевір свій API ключ в налаштуваннях.", "connectionFailed": "Не вдалося підключитися до сервісу embedder. Будь ласка, перевір налаштування підключення і переконайся, що сервіс працює.", "modelNotAvailable": "Вказана модель недоступна. Будь ласка, перевір конфігурацію моделі.", "configurationError": "Недійсна конфігурація embedder. Будь ласка, переглянь свої налаштування.", "serviceUnavailable": "Сер<PERSON><PERSON><PERSON> embedder недоступний. Будь ласка, переконайся, що він працює і доступний.", "invalidEndpoint": "Недійсна кінцева точка API. Будь ласка, перевір конфігурацію URL.", "invalidEmbedderConfig": "Недійсна конфігурація embedder. Будь ласка, перевір свої налаштування.", "invalidApiKey": "Недійсний API ключ. Будь ласка, перевір конфігурацію API ключа.", "invalidBaseUrl": "Недійсний базовий URL. Будь ласка, перевір конфігурацію URL.", "invalidModel": "Недійсна модель. Будь ласка, перевір конфігурацію моделі.", "invalidResponse": "Недійсна відповідь від сервісу embedder. Будь ласка, перевір свою конфігурацію."}, "serviceFactory": {"openAiConfigMissing": "Відсутня конфігурація OpenAI для створення embedder", "ollamaConfigMissing": "Відсутня конфігурація Ollama для створення embedder", "openAiCompatibleConfigMissing": "Відсутня конфігурація OpenAI Compatible для створення embedder", "geminiConfigMissing": "Відсутня конфігурація Gemini для створення embedder", "invalidEmbedderType": "Налаштовано недійсний тип embedder: {{embedderProvider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Не вдалося визначити розмірність вектора для моделі '{{modelId}}' з провайдером '{{provider}}'. Будь ласка, переконайся, що 'Розмірність Embedding' правильно встановлена в налаштуваннях провайдера OpenAI-Compatible.", "vectorDimensionNotDetermined": "Не вдалося визначити розмірність вектора для моделі '{{modelId}}' з провайдером '{{provider}}'. Перевір профілі моделі або конфігурацію.", "qdrantUrlMissing": "Відсутній URL Qdrant для створення векторного сховища", "codeIndexingNotConfigured": "Неможливо створити сервіси: Індексування коду не налаштовано належним чином"}}