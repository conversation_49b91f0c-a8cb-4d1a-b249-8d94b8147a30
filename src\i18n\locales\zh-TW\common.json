{"extension": {"name": "Kilo Code", "description": "用於規劃、建置和修復程式碼的開源 AI 編碼助理。"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "反饋", "description": "我們很樂意聽取您的反饋或幫助您解決遇到的任何問題。", "githubIssues": "在GitHub上報告問題", "githubDiscussions": "加入GitHub討論", "discord": "加入我們的Discord社區", "customerSupport": "客戶支援"}, "welcome": "歡迎，{{name}}！您有 {{count}} 條通知。", "items": {"zero": "沒有項目", "one": "1 個項目", "other": "{{count}}個項目"}, "confirmation": {"reset_state": "您確定要重設擴充套件中的所有狀態和金鑰儲存嗎？此操作無法復原。", "delete_config_profile": "您確定要刪除此設定檔案嗎？", "delete_custom_mode_with_rules": "您確定要刪除此 {scope} 模式嗎？\n\n這也將刪除位於以下位置的關聯規則資料夾：\n{rulesFolderPath}", "delete_message": "您想刪除哪些內容？", "edit_warning": "編輯此訊息將刪除對話中的所有後續訊息。您要繼續嗎？", "delete_just_this_message": "僅這則訊息", "delete_this_and_subsequent": "這則訊息及所有後續訊息", "proceed": "繼續"}, "errors": {"invalid_data_uri": "資料 URI 格式無效", "error_copying_image": "複製圖片時發生錯誤：{{errorMessage}}", "error_saving_image": "儲存圖片時發生錯誤：{{errorMessage}}", "error_opening_image": "開啟圖片時發生錯誤：{{error}}", "could_not_open_file": "無法開啟檔案：{{errorMessage}}", "could_not_open_file_generic": "無法開啟檔案！", "checkpoint_timeout": "嘗試恢復檢查點時超時。", "checkpoint_failed": "恢復檢查點失敗。", "no_workspace": "請先開啟專案資料夾", "update_support_prompt": "更新支援訊息失敗", "reset_support_prompt": "重設支援訊息失敗", "enhance_prompt": "增強訊息失敗", "get_system_prompt": "取得系統訊息失敗", "search_commits": "搜尋提交失敗", "save_api_config": "儲存 API 設定失敗", "create_api_config": "建立 API 設定失敗", "rename_api_config": "重新命名 API 設定失敗", "load_api_config": "載入 API 設定失敗", "delete_api_config": "刪除 API 設定失敗", "list_api_config": "取得 API 設定列表失敗", "update_server_timeout": "更新伺服器超時設定失敗", "hmr_not_running": "本機開發伺服器沒有執行，HMR 將不起作用。請在啟動擴充套件前執行'npm run dev'以啟用 HMR。", "retrieve_current_mode": "從狀態中檢索目前模式失敗。", "failed_delete_repo": "刪除關聯的影子倉庫或分支失敗：{{error}}", "failed_remove_directory": "刪除工作目錄失敗：{{error}}", "custom_storage_path_unusable": "自訂儲存路徑 \"{{path}}\" 無法使用，將使用預設路徑", "cannot_access_path": "無法存取路徑 {{path}}：{{error}}", "settings_import_failed": "設定匯入失敗：{{error}}。", "mistake_limit_guidance": "這可能表明模型思維過程失敗或無法正確使用工具，可透過使用者指導來緩解（例如「嘗試將工作分解為更小的步驟」）。", "violated_organization_allowlist": "執行工作失敗：目前設定檔違反了您的組織設定", "condense_failed": "壓縮上下文失敗", "condense_not_enough_messages": "沒有足夠的訊息來壓縮 {{prevContextTokens}} Token 的上下文。至少需要 {{minimumMessageCount}} 則訊息，但只有 {{messageCount}} 則可用。", "condensed_recently": "上下文最近已壓縮；跳過此次嘗試", "condense_handler_invalid": "壓縮上下文的 API 處理程式無效", "condense_context_grew": "壓縮過程中上下文大小從 {{prevContextTokens}} 增加到 {{newContextTokens}}；跳過此次嘗試", "url_timeout": "網站載入超時。這可能是由於網路連線緩慢、網站負載過重或暫時無法使用。你可以稍後重試或檢查 URL 是否正確。", "url_not_found": "找不到網站位址。請檢查 URL 是否正確並重試。", "no_internet": "無網路連線。請檢查網路連線並重試。", "url_forbidden": "存取此網站被禁止。該網站可能封鎖自動存取或需要身分驗證。", "url_page_not_found": "找不到頁面。請檢查 URL 是否正確。", "url_fetch_failed": "取得 URL 內容失敗：{{error}}", "url_fetch_error_with_url": "取得 {{url}} 內容時發生錯誤：{{error}}", "share_task_failed": "分享工作失敗。請重試。", "share_no_active_task": "沒有活躍的工作可分享", "share_auth_required": "需要身份驗證。請登入以分享工作。", "share_not_enabled": "此組織未啟用工作分享功能。", "share_task_not_found": "未找到工作或存取被拒絕。", "delete_rules_folder_failed": "刪除規則資料夾失敗: {{rulesFolderPath}}。錯誤: {{error}}", "claudeCode": {"processExited": "Claude Code 程序退出，退出碼：{{exitCode}}。", "errorOutput": "錯誤輸出：{{output}}", "processExitedWithError": "Claude Code 程序退出，退出碼：{{exitCode}}。錯誤輸出：{{output}}", "stoppedWithReason": "<PERSON> Code 停止，原因：{{reason}}", "apiKeyModelPlanMismatch": "API 金鑰和訂閱方案允許不同的模型。請確保所選模型包含在您的方案中。"}, "geminiCli": {"oauthLoadFailed": "無法載入 OAuth 憑證。請先進行驗證：{{error}}", "tokenRefreshFailed": "無法重新整理 OAuth 權杖：{{error}}", "onboardingTimeout": "新手導引操作在 60 秒後逾時。請稍後再試。", "projectDiscoveryFailed": "無法發現專案 ID。請確保您已使用 'gemini auth' 進行驗證。", "rateLimitExceeded": "超過速率限制。已達到免費層級限制。", "badRequest": "錯誤的請求：{{details}}", "apiError": "Gemini CLI API 錯誤：{{error}}", "completionError": "Gemini CLI 完成錯誤：{{error}}"}, "mode_import_failed": "匯入模式失敗：{{error}}"}, "warnings": {"no_terminal_content": "沒有選擇終端機內容", "missing_task_files": "此工作的檔案遺失。您想從工作列表中刪除它嗎？", "auto_import_failed": "自動匯入 Kilo Code 設定失敗：{{error}}"}, "info": {"no_changes": "沒有找到更改。", "clipboard_copy": "系統訊息已成功複製到剪貼簿", "history_cleanup": "已從歷史記錄中清理{{count}}個缺少檔案的工作。", "custom_storage_path_set": "自訂儲存路徑已設定：{{path}}", "default_storage_path": "已恢復使用預設儲存路徑", "settings_imported": "設定已成功匯入。", "auto_import_success": "已自動匯入 Kilo Code 設定：{{filename}}", "share_link_copied": "分享連結已複製到剪貼簿", "image_copied_to_clipboard": "圖片資料 URI 已複製到剪貼簿", "image_saved": "圖片已儲存至 {{path}}", "organization_share_link_copied": "組織分享連結已複製到剪貼簿！", "public_share_link_copied": "公開分享連結已複製到剪貼簿！", "mode_exported": "模式 '{{mode}}' 已成功匯出", "mode_imported": "模式已成功匯入"}, "answers": {"yes": "是", "no": "否", "remove": "刪除", "keep": "保留"}, "buttons": {"save": "儲存", "edit": "編輯"}, "tasks": {"canceled": "工作錯誤：它已被使用者停止並取消。", "deleted": "工作失敗：它已被使用者停止並刪除。", "incomplete": "工作 #{{taskNumber}} (未完成)", "no_messages": "工作 #{{taskNumber}} (無訊息)"}, "storage": {"prompt_custom_path": "輸入自訂會話歷史儲存路徑，留空以使用預設位置", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "請輸入絕對路徑（例如 D:\\KiloCodeStorage 或 /home/<USER>/storage）", "enter_valid_path": "請輸入有效的路徑"}, "input": {"task_prompt": "讓 Kilo Code 做什麼？", "task_placeholder": "在這裡輸入工作"}, "settings": {"providers": {"groqApiKey": "Groq API 金鑰", "getGroqApiKey": "取得 Groq API 金鑰", "claudeCode": {"pathLabel": "Claude Code 路徑", "description": "Claude Code CLI 的選用路徑。如果未設定，預設為 'claude'。", "placeholder": "預設: claude"}}}, "customModes": {"errors": {"yamlParseError": ".kilocodemodes 檔案第 {{line}} 行 YAML 格式無效。請檢查：\n• 正確的縮排（使用空格，不要使用定位字元）\n• 匹配的引號和括號\n• 有效的 YAML 語法", "schemaValidationError": ".kilocodemodes 中自訂模式格式無效：\n{{issues}}", "invalidFormat": "自訂模式格式無效。請確保你的設定遵循正確的 YAML 格式。", "updateFailed": "更新自訂模式失敗：{{error}}", "deleteFailed": "刪除自訂模式失敗：{{error}}", "resetFailed": "重設自訂模式失敗：{{error}}", "modeNotFound": "寫入錯誤：未找到模式", "noWorkspaceForProject": "未找到專案特定模式的工作區資料夾"}, "scope": {"project": "專案", "global": "全域"}}, "mdm": {"errors": {"cloud_auth_required": "您的組織需要 Kilo Code Cloud 身份驗證。請登入以繼續。", "organization_mismatch": "您必須使用組織的 Kilo Code Cloud 帳戶進行身份驗證。", "verification_failed": "無法驗證組織身份驗證。"}}, "prompts": {"deleteMode": {"title": "刪除自訂模式", "description": "您確定要刪除此 {{scope}} 模式嗎？這也將刪除位於 {{rulesFolderPath}} 的關聯規則資料夾", "descriptionNoRules": "您確定要刪除此自訂模式嗎？", "confirm": "刪除"}}}