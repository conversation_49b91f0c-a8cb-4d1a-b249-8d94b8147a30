{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "<PERSON><PERSON>, oluşturma ve düzeltme için açık kaynaklı yapay zeka kodlama asistanı.", "command.newTask.title": "<PERSON><PERSON>", "command.explainCode.title": "Kodu Açıkla", "command.fixCode.title": "<PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON>tir", "command.addToContext.title": "Bağ<PERSON><PERSON>", "command.openInNewTab.title": "<PERSON><PERSON>", "command.focusInput.title": "<PERSON><PERSON><PERSON>", "command.setCustomStoragePath.title": "<PERSON><PERSON>", "command.importSettings.title": "Ayarları İçe Aktar", "command.terminal.addToContext.title": "Terminal İçeriğini Bağlama Ekle", "command.terminal.fixCommand.title": "<PERSON><PERSON> <PERSON><PERSON>", "command.terminal.explainCommand.title": "Bu Komutu Açıkla", "command.acceptInput.title": "Girişi/Öneriyi Kabul Et", "command.generateCommitMessage.title": "Kilo ile Commit Mesajı Oluştur", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "MCP Sunucuları", "command.prompts.title": "<PERSON><PERSON><PERSON>", "command.history.title": "Geçmiş", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Düzenleyicide Aç", "command.settings.title": "<PERSON><PERSON><PERSON>", "command.documentation.title": "Dokümantasyon", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "'Her zaman yür<PERSON><PERSON><PERSON> işlemlerini onayla' etkinleştirildiğinde otomatik olarak yürütülebilen komutlar", "settings.vsCodeLmModelSelector.description": "VSCode dil modeli API'si için a<PERSON>", "settings.vsCodeLmModelSelector.vendor.description": "Dil modelinin <PERSON>ğlayıcısı (örn: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON>l modelinin a<PERSON> (örn: gpt-4)", "settings.customStoragePath.description": "Özel depolama yolu. Varsayılan konumu kullanmak için boş bırakın. Mutlak yolları destekler (örn: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Kilo Code hızlı düzeltmeleri etkinleştir.", "settings.autoImportSettingsPath.description": "Uzantı başlangıcında otomatik olarak içe aktarılacak bir Kilo Code yapılandırma dosyasının yolu. Mutlak yolları ve ana dizine göreli yolları destekler (ör. '~/Documents/kilo-code-settings.json'). Otomatik içe aktarmayı devre dışı bırakmak için boş bırakın.", "ghost.input.title": "Onaylamak için 'Enter'a, iptal etmek için 'Escape'e basın", "ghost.input.placeholder": "Ne yapmak istediğinizi açıklayın...", "ghost.commands.generateSuggestions": "Kilo Code: Düzenleme Önerileri Oluştur", "ghost.commands.displaySuggestions": "Düzenleme Önerilerini Göster", "ghost.commands.cancelSuggestions": "Düzenleme Önerilerini İptal Et", "ghost.commands.applyCurrentSuggestion": "Mevcut Düzenleme Önerisini Uygula", "ghost.commands.applyAllSuggestions": "<PERSON><PERSON><PERSON> Düzenleme Önerilerini Uygula", "ghost.commands.promptCodeSuggestion": "Hızlı Görev", "ghost.commands.goToNextSuggestion": "Sonraki Öneriye Git", "ghost.commands.goToPreviousSuggestion": "Önceki Öneriye Git"}