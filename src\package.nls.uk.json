{"extension.displayName": "Kilo Code AI Agent (поєднані функції Cline / Roo)", "extension.description": "Помічник з кодування AI з відкритим кодом для планування, створення та виправлення коду.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "Нове завдання", "command.mcpServers.title": "MCP сервери", "command.prompts.title": "Режими", "command.history.title": "Історія", "command.marketplace.title": "Маркетплейс", "command.openInEditor.title": "Відкрити в редакторі", "command.settings.title": "Налаштування", "command.documentation.title": "Документація", "command.openInNewTab.title": "Відкрити в новій вкладці", "command.explainCode.title": "Пояснити код", "command.fixCode.title": "Виправити код", "command.improveCode.title": "Покращити код", "command.addToContext.title": "Додати до контексту", "command.focusInput.title": "Фокус на полі введення", "command.setCustomStoragePath.title": "Встановити власний шлях зберігання", "command.terminal.addToContext.title": "Додати вміст терміналу до контексту", "command.terminal.fixCommand.title": "Виправити цю команду", "command.terminal.explainCommand.title": "Пояснити цю команду", "command.acceptInput.title": "Прийняти введення/пропозицію", "command.generateCommitMessage.title": "Згенерувати повідомлення коміту за допомогою Kilo", "command.profile.title": "Профіль", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Команди, які можуть автоматично виконуватися, коли увімкнено 'Always approve execute operations'", "settings.vsCodeLmModelSelector.description": "Налаштування для VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Постачальник мовної моделі (наприклад, copilot)", "settings.vsCodeLmModelSelector.family.description": "Сімейство мовної моделі (наприклад, gpt-4)", "settings.customStoragePath.description": "Власний шлях зберігання. Залиште порожнім, щоб використовувати розташування за замовчуванням. Підтримує абсолютні шляхи (наприклад, 'D:\\KiloCodeStorage')", "command.importSettings.title": "Імпортувати налаштування", "settings.enableCodeActions.description": "Увімкнути швидкі виправлення Kilo Code.", "settings.autoImportSettingsPath.description": "Шлях до файлу конфігурації Kilo Code для автоматичного імпорту під час запуску розширення. Підтримує абсолютні шляхи та шляхи відносно домашнього каталогу (наприклад, '~/Documents/kilo-code-settings.json'). Залиште порожнім, щоб вимкнути автоматичний імпорт.", "ghost.input.title": "Натисніть 'Enter' для підтвердження або 'Escape' для скасування", "ghost.input.placeholder": "Опишіть, що ви хочете зробити...", "ghost.commands.generateSuggestions": "Kilo Code: Генерувати Пропозиції Редагування", "ghost.commands.displaySuggestions": "Показати Пропозиції Редагування", "ghost.commands.cancelSuggestions": "Скасувати Пропозиції Редагування", "ghost.commands.applyCurrentSuggestion": "Застосувати Поточну Пропозицію Редагування", "ghost.commands.applyAllSuggestions": "Застосувати Всі Пропозиції Редагування", "ghost.commands.promptCodeSuggestion": "Швидке Завдання", "ghost.commands.goToNextSuggestion": "Перейти до Наступної Пропозиції", "ghost.commands.goToPreviousSuggestion": "Перейти до Попередньої Пропозиції"}