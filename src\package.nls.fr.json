{"extension.displayName": "Agent IA Kilo Code (Cline/Roo fonctions unifiées)", "extension.description": "Assistant de codage d'IA Open Source pour la planification, la création et la correction de code.", "command.newTask.title": "Nouvelle Tâche", "command.explainCode.title": "Expliquer le Code", "command.fixCode.title": "Corriger le Code", "command.improveCode.title": "Améliorer le Code", "command.addToContext.title": "A<PERSON>ter au Contexte", "command.openInNewTab.title": "Ouv<PERSON>r dans un Nouvel Onglet", "command.focusInput.title": "Focus sur le Champ de Saisie", "command.setCustomStoragePath.title": "Définir le Chemin de Stockage Personnalisé", "command.importSettings.title": "Importer les Paramètres", "command.terminal.addToContext.title": "Ajouter le Contenu du Terminal au Contexte", "command.terminal.fixCommand.title": "<PERSON>rri<PERSON> cette <PERSON>e", "command.terminal.explainCommand.title": "Expliquer cette <PERSON>e", "command.acceptInput.title": "Accepter l'Entrée/Suggestion", "command.generateCommitMessage.title": "Générer un message de commit avec Kilo", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Serveurs MCP", "command.prompts.title": "Modes", "command.history.title": "Historique", "command.marketplace.title": "<PERSON><PERSON>", "command.openInEditor.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans l'Éditeur", "command.settings.title": "Paramètres", "command.documentation.title": "Documentation", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Commandes pouvant être exécutées automatiquement lorsque 'Toujours approuver les opérations d'exécution' est activé", "settings.vsCodeLmModelSelector.description": "Paramètres pour l'API du modèle de langage VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Le fournisseur du modèle de langage (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "La famille du modèle de langage (ex: gpt-4)", "settings.customStoragePath.description": "Chemin de stockage personnalisé. Laisser vide pour utiliser l'emplacement par défaut. Prend en charge les chemins absolus (ex: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Activer les correctifs rapides de Kilo Code.", "settings.autoImportSettingsPath.description": "Chemin d'accès à un fichier de configuration Kilo Code à importer automatiquement au démarrage de l'extension. Prend en charge les chemins absolus et les chemins relatifs au répertoire de base (par exemple, '~/Documents/kilo-code-settings.json'). Laisser vide pour désactiver l'importation automatique.", "ghost.input.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "ghost.input.placeholder": "Décrivez ce que vous voulez coder...", "ghost.commands.generateSuggestions": "Kilo Code : Générer des Modifications Suggérées", "ghost.commands.displaySuggestions": "Afficher les Modifications Suggérées", "ghost.commands.cancelSuggestions": "Annuler les Modifications Suggérées", "ghost.commands.applyCurrentSuggestion": "Appliquer la Modification Suggérée Actuelle", "ghost.commands.applyAllSuggestions": "Appliquer Toutes les Modifications Suggérées", "ghost.commands.promptCodeSuggestion": "Tâche Rapide", "ghost.commands.goToNextSuggestion": "Aller à la Suggestion Suivante", "ghost.commands.goToPreviousSuggestion": "Aller à la Suggestion Précédente"}