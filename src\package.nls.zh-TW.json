{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "用於規劃、建置和修復程式碼的開源 AI 開發助理。", "command.newTask.title": "新建任務", "command.explainCode.title": "解釋程式碼", "command.fixCode.title": "修復程式碼", "command.improveCode.title": "改進程式碼", "command.addToContext.title": "新增到上下文", "command.openInNewTab.title": "在新分頁中開啟", "command.focusInput.title": "聚焦輸入框", "command.setCustomStoragePath.title": "設定自訂儲存路徑", "command.importSettings.title": "匯入設定", "command.terminal.addToContext.title": "將終端內容新增到上下文", "command.terminal.fixCommand.title": "修復此命令", "command.terminal.explainCommand.title": "解釋此命令", "command.acceptInput.title": "接受輸入/建議", "command.generateCommitMessage.title": "使用 Kilo 生成提交訊息", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "MCP 伺服器", "command.prompts.title": "模式", "command.history.title": "歷史記錄", "command.marketplace.title": "應用市場", "command.openInEditor.title": "在編輯器中開啟", "command.settings.title": "設定", "command.documentation.title": "文件", "command.profile.title": "設定檔", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "當啟用'始終批准執行操作'時可以自動執行的命令", "settings.vsCodeLmModelSelector.description": "VSCode 語言模型 API 的設定", "settings.vsCodeLmModelSelector.vendor.description": "語言模型供應商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "語言模型系列（例如：gpt-4）", "settings.customStoragePath.description": "自訂儲存路徑。留空以使用預設位置。支援絕對路徑（例如：'D:\\KiloCodeStorage'）", "settings.enableCodeActions.description": "啟用 Kilo Code 快速修復。", "settings.autoImportSettingsPath.description": "Kilo Code 設定檔案的路徑，用於在擴充功能啟動時自動匯入。支援絕對路徑和相對於主目錄的路徑（例如 '~/Documents/kilo-code-settings.json'）。留空以停用自動匯入。", "ghost.input.title": "按 'Enter' 確認或按 'Escape' 取消", "ghost.input.placeholder": "描述您想要做什麼...", "ghost.commands.generateSuggestions": "Kilo Code: 產生編輯建議", "ghost.commands.displaySuggestions": "顯示編輯建議", "ghost.commands.cancelSuggestions": "取消編輯建議", "ghost.commands.applyCurrentSuggestion": "套用目前編輯建議", "ghost.commands.applyAllSuggestions": "套用所有編輯建議", "ghost.commands.promptCodeSuggestion": "快速任務", "ghost.commands.goToNextSuggestion": "前往下一個建議", "ghost.commands.goToPreviousSuggestion": "前往上一個建議"}