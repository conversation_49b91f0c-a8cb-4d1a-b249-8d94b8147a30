// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"label": "watch",
			"dependsOn": ["watch:pnpm", "watch:webview", "watch:bundle", "watch:tsc"],
			"presentation": {
				"reveal": "never"
			},
			"group": {
				"kind": "build",
				"isDefault": true
			},
			"runOptions": {
				"runOn": "folderOpen"
			}
		},
		{
			"label": "watch:pnpm",
			"type": "shell",
			"command": "npx",
			"args": ["nodemon", "--watch", "pnpm-lock.yaml", "--exec", "pnpm install"],
			"group": "build",
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			},
			"problemMatcher": {
				"pattern": { "regexp": "^$" },
				"background": {
					"activeOnStart": true,
					"beginsPattern": "\\[nodemon\\] starting",
					"endsPattern": "\\[nodemon\\] clean exit - waiting for changes before restart"
				}
			}
		},
		{
			"label": "watch:webview",
			"dependsOn": ["watch:pnpm"],
			"type": "shell",
			"command": "pnpm --filter @roo-code/vscode-webview dev",
			"group": "build",
			"problemMatcher": {
				"owner": "vite",
				"pattern": { "regexp": "^$" },
				"background": {
					"activeOnStart": true,
					"beginsPattern": ".*VITE.*",
					"endsPattern": ".*Local:.*"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "watch:bundle",
			"dependsOn": ["watch:pnpm"],
			"type": "shell",
			"command": "npx turbo watch:bundle",
			"group": "build",
			"problemMatcher": {
				"owner": "esbuild",
				"pattern": {
					"regexp": "^$"
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": "esbuild-problem-matcher#onStart",
					"endsPattern": "esbuild-problem-matcher#onEnd"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "watch:tsc",
			"dependsOn": ["watch:pnpm"],
			"type": "shell",
			"command": "npx turbo watch:tsc",
			"group": "build",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "storybook",
			"type": "shell",
			"command": "kill -9 $(lsof -t -i tcp:6006) 2>/dev/null || true && npx turbo storybook",
			"group": "build",
			"problemMatcher": {
				"owner": "storybook",
				"pattern": {
					"regexp": "^$"
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": ".*Storybook.*",
					"endsPattern": ".*Local:.*http://localhost:6006.*"
				}
			},
			"isBackground": true,
			"presentation": {
				"reveal": "always",
				"panel": "new"
			}
		}
	]
}
