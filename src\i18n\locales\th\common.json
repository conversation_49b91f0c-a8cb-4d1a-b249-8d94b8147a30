{"extension": {"name": "Kilo Code", "description": "ผู้ช่วยเขียนโค้ด AI แบบ Open Source สำหรับการวางแผน สร้าง และแก้ไขโค้ด"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "ข้อเสนอแนะ", "description": "เรายินดีรับฟังข้อเสนอแนะของคุณหรือช่วยเหลือเกี่ยวกับปัญหาที่คุณกำลังประสบ", "githubIssues": "รายงานปัญหาบน GitHub", "githubDiscussions": "เข้าร่วมการสนทนาบน GitHub", "discord": "เข้าร่วมชุมชน Discord ของเรา", "customerSupport": "ฝ่ายสนับสนุนลูกค้า"}, "welcome": "ยินดีต้อนรับ {{name}}! คุณมี {{count}} การแจ้งเตือน", "items": {"zero": "ไม่มีรายการ", "one": "หนึ่งรายการ", "other": "{{count}} รายการ"}, "confirmation": {"reset_state": "คุณแน่ใจหรือไม่ว่าต้องการรีเซ็ตสถานะและที่เก็บข้อมูลลับทั้งหมดในส่วนขยาย? การดำเนินการนี้ไม่สามารถยกเลิกได้", "delete_config_profile": "คุณแน่ใจหรือไม่ว่าต้องการลบโปรไฟล์การกำหนดค่านี้?", "delete_custom_mode_with_rules": "คุณแน่ใจหรือไม่ว่าต้องการลบโหมด {scope} นี้?\n\nการดำเนินการนี้จะลบโฟลเดอร์กฎที่เกี่ยวข้องด้วย ที่:\n{rulesFolderPath}", "delete_message": "คุณต้องการลบอะไร?", "edit_warning": "การแก้ไขข้อความนี้จะลบข้อความที่ตามมาทั้งหมดในการสนทนา คุณต้องการดำเนินการต่อหรือไม่?", "delete_just_this_message": "เฉพาะข้อความนี้", "delete_this_and_subsequent": "ข้อความนี้และข้อความที่ตามมาทั้งหมด", "proceed": "ดำเนินการต่อ"}, "errors": {"invalid_data_uri": "รูปแบบ data URI ไม่ถูกต้อง", "error_copying_image": "เกิดข้อผิดพลาดในการคัดลอกรูปภาพ: {{errorMessage}}", "error_opening_image": "เกิดข้อผิดพลาดในการเปิดรูปภาพ: {{error}}", "error_saving_image": "เกิดข้อผิดพลาดในการบันทึกรูปภาพ: {{errorMessage}}", "could_not_open_file": "ไม่สามารถเปิดไฟล์: {{errorMessage}}", "could_not_open_file_generic": "ไม่สามารถเปิดไฟล์!", "checkpoint_timeout": "หมดเวลาเมื่อพยายามกู้คืน checkpoint", "checkpoint_failed": "ล้มเหลวในการกู้คืน checkpoint", "no_workspace": "กรุณาเปิดโฟลเดอร์โปรเจ็กต์ก่อน", "update_support_prompt": "ล้มเหลวในการอัปเดต support prompt", "reset_support_prompt": "ล้มเหลวในการรีเซ็ต support prompt", "enhance_prompt": "ล้มเหลวในการปรับปรุง prompt", "get_system_prompt": "ล้มเหลวในการรับ system prompt", "search_commits": "ล้มเหลวในการค้นหา commits", "save_api_config": "ล้มเหลวในการบันทึกการกำหนดค่า api", "create_api_config": "ล้มเหลวในการสร้างการกำหนดค่า api", "rename_api_config": "ล้มเหลวในการเปลี่ยนชื่อการกำหนดค่า api", "load_api_config": "ล้มเหลวในการโหลดการกำหนดค่า api", "delete_api_config": "ล้มเหลวในการลบการกำหนดค่า api", "list_api_config": "ล้มเหลวในการรับรายการการกำหนดค่า api", "update_server_timeout": "ล้มเหลวในการอัปเดต server timeout", "hmr_not_running": "เซิร์ฟเวอร์การพัฒนาในเครื่องไม่ทำงาน HMR จะไม่ทำงาน กรุณารัน 'npm run dev' ก่อนเปิดส่วนขยายเพื่อเปิดใช้งาน HMR", "retrieve_current_mode": "ข้อผิดพลาด: ล้มเหลวในการดึงโหมดปัจจุบันจากสถานะ", "failed_delete_repo": "ล้มเหลวในการลบ shadow repository หรือ branch ที่เกี่ยวข้อง: {{error}}", "failed_remove_directory": "ล้มเหลวในการลบไดเรกทอรีงาน: {{error}}", "custom_storage_path_unusable": "เส้นทางจัดเก็บที่กำหนดเอง \"{{path}}\" ไม่สามารถใช้งานได้ จะใช้เส้นทางเริ่มต้น", "cannot_access_path": "ไม่สามารถเข้าถึงเส้นทาง {{path}}: {{error}}", "settings_import_failed": "การนำเข้าการตั้งค่าล้มเหลว: {{error}}", "mistake_limit_guidance": "สิ่งนี้อาจบ่งชี้ถึงความล้มเหลวในกระบวนการคิดของโมเดลหรือไม่สามารถใช้เครื่องมือได้อย่างถูกต้อง ซึ่งสามารถบรรเทาได้ด้วยคำแนะนำจากผู้ใช้ (เช่น \"ลองแบ่งงานออกเป็นขั้นตอนเล็กๆ\")", "violated_organization_allowlist": "ล้มเหลวในการรันงาน: โปรไฟล์ปัจจุบันละเมิดการตั้งค่าองค์กรของคุณ", "condense_failed": "ล้มเหลวในการย่อบริบท", "condense_not_enough_messages": "ข้อความไม่เพียงพอที่จะย่อบริบท {{prevContextTokens}} โทเค็น ต้องการข้อความอย่างน้อย {{minimumMessageCount}} ข้อความ แต่มีเพียง {{messageCount}} ข้อความเท่านั้น", "condensed_recently": "บริบทถูกย่อเมื่อเร็วๆ นี้ ข้ามความพยายามนี้", "condense_handler_invalid": "ตัวจัดการ API สำหรับการย่อบริบทไม่ถูกต้อง", "condense_context_grew": "ขนาดบริบทเพิ่มขึ้นจาก {{prevContextTokens}} เป็น {{newContextTokens}} โทเค็นระหว่างการย่อ ข้ามความพยายามนี้", "url_timeout": "เว็บไซต์ใช้เวลาโหลดนานเกินไป (หมดเวลา) อาจเกิดจากการเชื่อมต่อช้า เว็บไซต์หนัก หรือเว็บไซต์ไม่พร้อมใช้งานชั่วคราว คุณสามารถลองอีกครั้งในภายหลังหรือตรวจสอบว่า URL ถูกต้อง", "url_not_found": "ไม่พบที่อยู่เว็บไซต์ กรุณาตรวจสอบว่า URL ถูกต้องและลองอีกครั้ง", "no_internet": "ไม่มีการเชื่อมต่ออินเทอร์เน็ต กรุณาตรวจสอบการเชื่อมต่อเครือข่ายและลองอีกครั้ง", "url_forbidden": "การเข้าถึงเว็บไซต์นี้ถูกห้าม เว็บไซต์อาจบล็อกการเข้าถึงอัตโนมัติหรือต้องการการยืนยันตัวตน", "url_page_not_found": "ไม่พบหน้าเว็บ กรุณาตรวจสอบว่า URL ถูกต้อง", "url_fetch_failed": "ล้มเหลวในการดึงเนื้อหา URL: {{error}}", "url_fetch_error_with_url": "เกิดข้อผิดพลาดในการดึงเนื้อหาสำหรับ {{url}}: {{error}}", "share_task_failed": "ล้มเหลวในการแชร์งาน กรุณาลองอีกครั้ง", "share_no_active_task": "ไม่มีงานที่ใช้งานอยู่เพื่อแชร์", "share_auth_required": "ต้องมีการยืนยันตัวตน กรุณาลงชื่อเข้าใช้เพื่อแชร์งาน", "share_not_enabled": "การแชร์งานไม่ได้เปิดใช้งานสำหรับองค์กรนี้", "share_task_not_found": "ไม่พบงานหรือการเข้าถึงถูกปฏิเสธ", "mode_import_failed": "ล้มเหลวในการนำเข้าโหมด: {{error}}", "delete_rules_folder_failed": "ล้มเหลวในการลบโฟลเดอร์กฎ: {{rulesFolderPath}} ข้อผิดพลาด: {{error}}", "claudeCode": {"processExited": "กระบวนการ Claude Code ออกด้วยรหัส {{exitCode}}", "errorOutput": "ผลลัพธ์ข้อผิดพลาด: {{output}}", "processExitedWithError": "กระบวนการ Claude Code ออกด้วยรหัส {{exitCode}} ผลลัพธ์ข้อผิดพลาด: {{output}}", "stoppedWithReason": "Claude Code หยุดด้วยเหตุผล: {{reason}}", "apiKeyModelPlanMismatch": "คีย์ API และแผนการสมัครสมาชิกอนุญาตให้ใช้โมเดลที่แตกต่างกัน ตรวจสอบให้แน่ใจว่าโมเดลที่เลือกรวมอยู่ในแผนของคุณ"}, "geminiCli": {"oauthLoadFailed": "ล้มเหลวในการโหลดข้อมูลประจำตัว OAuth กรุณายืนยันตัวตนก่อน: {{error}}", "tokenRefreshFailed": "ล้มเหลวในการรีเฟรช OAuth token: {{error}}", "onboardingTimeout": "การดำเนินการ onboarding หมดเวลาหลังจาก 60 วินาที กรุณาลองอีกครั้งในภายหลัง", "projectDiscoveryFailed": "ไม่สามารถค้นหา project ID ตรวจสอบให้แน่ใจว่าคุณยืนยันตัวตนด้วย 'gemini auth'", "rateLimitExceeded": "เกินขีดจำกัดอัตรา ถึงขีดจำกัดของระดับฟรีแล้ว", "badRequest": "คำขอไม่ถูกต้อง: {{details}}", "apiError": "ข้อผิดพลาด Gemini CLI API: {{error}}", "completionError": "ข้อผิดพลาดการทำงานเสร็จสิ้น Gemini CLI: {{error}}"}}, "warnings": {"no_terminal_content": "ไม่มีเนื้อหาเทอร์มินัลที่เลือก", "missing_task_files": "ไฟล์ของงานนี้หายไป คุณต้องการลบออกจากรายการงานหรือไม่?", "auto_import_failed": "ล้มเหลวในการนำเข้าการตั้งค่า Kilo Code อัตโนมัติ: {{error}}"}, "info": {"no_changes": "ไม่พบการเปลี่ยนแปลง", "clipboard_copy": "คัดลอก system prompt ไปยังคลิปบอร์ดเรียบร้อยแล้ว", "history_cleanup": "ทำความสะอาด {{count}} งานที่มีไฟล์หายไปจากประวัติ", "custom_storage_path_set": "ตั้งค่าเส้นทางจัดเก็บที่กำหนดเอง: {{path}}", "default_storage_path": "กลับไปใช้เส้นทางจัดเก็บเริ่มต้น", "settings_imported": "นำเข้าการตั้งค่าเรียบร้อยแล้ว", "auto_import_success": "นำเข้าการตั้งค่า Kilo Code อัตโนมัติจาก {{filename}} เรียบร้อยแล้ว", "share_link_copied": "คัดลอกลิงก์แชร์ไปยังคลิปบอร์ด", "organization_share_link_copied": "คัดลอกลิงก์แชร์องค์กรไปยังคลิปบอร์ด!", "public_share_link_copied": "คัดลอกลิงก์แชร์สาธารณะไปยังคลิปบอร์ด!", "image_copied_to_clipboard": "คัดลอก image data URI ไปยังคลิปบอร์ด", "image_saved": "บันทึกรูปภาพไปที่ {{path}}", "mode_exported": "ส่งออกโหมด '{{mode}}' เรียบร้อยแล้ว", "mode_imported": "นำเข้าโหมดเรียบร้อยแล้ว"}, "answers": {"yes": "ใช่", "no": "ไม่", "cancel": "ยกเลิก", "remove": "ลบ", "keep": "เก็บไว้"}, "buttons": {"save": "บันทึก", "edit": "แก้ไข"}, "tasks": {"canceled": "ข้อผิดพลาดงาน: ถูกหยุดและยกเลิกโดยผู้ใช้", "deleted": "ความล้มเหลวของงาน: ถูกหยุดและลบโดยผู้ใช้", "incomplete": "งาน #{{taskNumber}} (ไม่สมบูรณ์)", "no_messages": "งาน #{{taskNumber}} (ไม่มีข้อความ)"}, "storage": {"prompt_custom_path": "ป้อนเส้นทางจัดเก็บประวัติการสนทนาที่กำหนดเอง เว้นว่างไว้เพื่อใช้ตำแหน่งเริ่มต้น", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "กรุณาป้อนเส้นทางแบบสัมบูรณ์ (เช่น D:\\KiloCodeStorage หรือ /home/<USER>/storage)", "enter_valid_path": "กรุณาป้อนเส้นทางที่ถูกต้อง"}, "input": {"task_prompt": "Kilo Code ควรทำอะไร?", "task_placeholder": "สร้าง ค้นหา ถามอะไรก็ได้"}, "customModes": {"errors": {"yamlParseError": "YAML ไม่ถูกต้องในไฟล์ .kilocodemodes ที่บรรทัด {{line}} กรุณาตรวจสอบ:\n• การเยื้องที่ถูกต้อง (ใช้ space ไม่ใช่ tab)\n• เครื่องหมายคำพูดและวงเล็บที่ตรงกัน\n• ไวยากรณ์ YAML ที่ถูกต้อง", "schemaValidationError": "รูปแบบโหมดกำหนดเองไม่ถูกต้องใน .kilocodemodes:\n{{issues}}", "invalidFormat": "รูปแบบโหมดกำหนดเองไม่ถูกต้อง กรุณาตรวจสอบให้แน่ใจว่าการตั้งค่าของคุณเป็นไปตามรูปแบบ YAML ที่ถูกต้อง", "updateFailed": "ล้มเหลวในการอัปเดตโหมดกำหนดเอง: {{error}}", "deleteFailed": "ล้มเหลวในการลบโหมดกำหนดเอง: {{error}}", "resetFailed": "ล้มเหลวในการรีเซ็ตโหมดกำหนดเอง: {{error}}", "modeNotFound": "ข้อผิดพลาดในการเขียน: ไม่พบโหมด", "noWorkspaceForProject": "ไม่พบโฟลเดอร์พื้นที่ทำงานสำหรับโหมดเฉพาะโครงการ"}, "scope": {"project": "โครงการ", "global": "ทั่วไป"}}, "mdm": {"errors": {"cloud_auth_required": "องค์กรของคุณต้องการการยืนยันตัวตน Kilo Code Cloud กรุณาลงชื่อเข้าใช้เพื่อดำเนินการต่อ", "organization_mismatch": "คุณต้องยืนยันตัวตนด้วยบัญชี Kilo Code Cloud ขององค์กรของคุณ", "verification_failed": "ไม่สามารถตรวจสอบการยืนยันตัวตนขององค์กร"}}, "prompts": {"deleteMode": {"title": "ลบโหมดที่กำหนดเอง", "description": "คุณแน่ใจหรือไม่ว่าต้องการลบโหมด {{scope}} นี้? การดำเนินการนี้จะลบโฟลเดอร์กฎที่เกี่ยวข้องด้วย ที่: {{rulesFolderPath}}", "descriptionNoRules": "คุณแน่ใจหรือไม่ว่าต้องการลบโหมดที่กำหนดเองนี้?", "confirm": "ลบ"}}}