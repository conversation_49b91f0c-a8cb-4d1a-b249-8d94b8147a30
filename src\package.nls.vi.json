{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Trợ lý mã hóa AI nguồn mở để lập kế hoạch, x<PERSON><PERSON> dựng và sửa mã.", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "command.openInNewTab.title": "Mở trong Tab Mới", "command.focusInput.title": "<PERSON>ậ<PERSON>rung vào <PERSON>", "command.setCustomStoragePath.title": "Đặt Đường Dẫn Lưu Trữ Tùy Chỉnh", "command.importSettings.title": "<PERSON><PERSON><PERSON><PERSON> Đặt", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON>m <PERSON>i Dung Terminal vào Ngữ Cảnh", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.acceptInput.title": "<PERSON>ấ<PERSON>/<PERSON><PERSON><PERSON> Ý", "command.generateCommitMessage.title": "<PERSON><PERSON><PERSON> Thông Điệp Commit vớ<PERSON>", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "<PERSON><PERSON><PERSON> MCP", "command.prompts.title": "Chế Độ", "command.history.title": "<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Mở trong Trình <PERSON>", "command.settings.title": "Cài Đặt", "command.documentation.title": "<PERSON><PERSON><PERSON>", "command.profile.title": "<PERSON><PERSON> sơ", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> l<PERSON>nh có thể được thực thi tự động khi 'Luôn phê duyệt các thao tác thực thi' đ<PERSON><PERSON><PERSON> bật", "settings.vsCodeLmModelSelector.description": "Cài đặt cho API mô hình ngôn ngữ VSCode", "settings.vsCodeLmModelSelector.vendor.description": "<PERSON><PERSON><PERSON> cung cấp mô hình ngôn ngữ (ví dụ: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON> mô hình ngôn ngữ (ví dụ: gpt-4)", "settings.customStoragePath.description": "Đường dẫn lưu trữ tùy chỉnh. Để trống để sử dụng vị trí mặc định. Hỗ trợ đường dẫn tuyệt đối (ví dụ: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "<PERSON><PERSON><PERSON> sửa lỗi nhanh <PERSON>lo <PERSON>.", "settings.autoImportSettingsPath.description": "Đường dẫn đến tệp cấu hình Kilo Code để tự động nhập khi khởi động tiện ích mở rộng. Hỗ trợ đường dẫn tuyệt đối và đường dẫn tương đối đến thư mục <PERSON> (ví dụ: '~/Documents/kilo-code-settings.json'). Để trống để tắt tính năng tự động nhập.", "ghost.input.title": "Nhấn 'Enter' để xác nhận hoặc 'Escape' để hủy", "ghost.input.placeholder": "<PERSON><PERSON> tả những gì bạn muốn làm...", "ghost.commands.generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON> Ý Chỉnh Sửa", "ghost.commands.displaySuggestions": "Hiển Th<PERSON>ợi Ý Chỉnh Sửa", "ghost.commands.cancelSuggestions": "<PERSON><PERSON><PERSON> Gợi Ý Chỉnh Sửa", "ghost.commands.applyCurrentSuggestion": "<PERSON><PERSON> Ý Chỉnh Sửa <PERSON>ện Tại", "ghost.commands.applyAllSuggestions": "<PERSON><PERSON> Ý Chỉnh Sửa", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "<PERSON><PERSON> Ý <PERSON><PERSON><PERSON><PERSON>", "ghost.commands.goToPreviousSuggestion": "<PERSON><PERSON>n <PERSON>ợi Ý Trước Đó"}