{"title": "Server MCP", "done": "<PERSON><PERSON>", "description": "Il <0>Model Context Protocol</0> permette la comunicazione con server MCP in esecuzione locale che forniscono strumenti e risorse aggiuntive per estendere le capacità di Kilo Code. Puoi utilizzare <1>server creati dalla comunità</1> o chiedere a Kilo Code di creare nuovi strumenti specifici per il tuo flusso di lavoro (ad esempio, \"aggiungi uno strumento che ottiene la documentazione npm più recente\").", "instructions": "Istruzioni", "enableToggle": {"title": "Abilita server MCP", "description": "Attiva questa opzione per permettere a Kilo Code di usare strumenti dai server MCP collegati. Questo dà a Kilo Code più capacità. Se non vuoi usare questi strumenti extra, disattiva per ridurre i costi dei token API."}, "enableServerCreation": {"title": "Abilita creazione server MCP", "description": "Abilita questa opzione per farti aiutare da Kilo Code a creare <1>nuovi</1> server MCP personalizzati. <0>Scopri di più sulla creazione di server</0>", "hint": "Suggerimento: Per ridurre i costi dei token API, disattiva questa impostazione quando non chiedi a Kilo Code di creare un nuovo server MCP."}, "editGlobalMCP": "Modifica MCP globale", "editProjectMCP": "Modifica MCP del progetto", "learnMoreEditingSettings": "Scopri di più sulla modifica dei file di configurazione MCP", "tool": {"alwaysAllow": "Consenti sempre", "parameters": "Parametri", "noDescription": "Nessuna descrizione", "togglePromptInclusion": "Attiva/disattiva inclusione nel prompt"}, "tabs": {"tools": "Strumenti", "resources": "Risorse", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON><PERSON><PERSON> strumento trovato", "noResources": "Nessuna risorsa trovata", "noErrors": "<PERSON><PERSON><PERSON> errore trovato"}, "networkTimeout": {"label": "Timeout di rete", "description": "Tempo massimo di attesa per le risposte del server", "options": {"15seconds": "15 secondi", "30seconds": "30 secondi", "1minute": "1 minuto", "5minutes": "5 minuti", "10minutes": "10 minuti", "15minutes": "15 minuti", "30minutes": "30 minuti", "60minutes": "60 minuti"}}, "deleteDialog": {"title": "Elimina server MCP", "description": "Sei sicuro di voler eliminare il server MCP \"{{serverName}}\"? Questa azione non può essere annullata.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Elimina"}, "serverStatus": {"retrying": "Riprovo...", "retryConnection": "Riprova connessione"}, "refreshMCP": "Aggiorna server MCP", "execution": {"running": "In esecuzione", "completed": "Completato", "error": "Errore"}}