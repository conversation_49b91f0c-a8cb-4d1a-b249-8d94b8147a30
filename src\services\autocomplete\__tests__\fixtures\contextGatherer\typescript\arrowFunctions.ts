// @ts-nocheck

const _getAddress = (_person: Person): Address => {
	// TODO
}

const _logPerson = (_person: Person) => {
	// TODO
}

const _getHardcodedAddress = (): Address => {
	// TODO
}

const _getAddresses = (_people: Person[]): Address[] => {
	// TODO
}

const _logPersonWithAddres = (_person: Person<Address>): Person<Address> => {
	// TODO
}

const _logPersonOrAddress = (_person: Person | Address): Person | Address => {
	// TODO
}

const _logPersonAndAddress = (_person: Person, _address: Address) => {
	// TODO
}
