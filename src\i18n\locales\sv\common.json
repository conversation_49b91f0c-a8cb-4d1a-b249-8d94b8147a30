{"extension": {"name": "Kilo Code", "description": "Open Source AI-kodningsassistent för planering, byggande och fixande av kod."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>i skulle gärna höra din <PERSON> eller hj<PERSON>pa till med eventuella problem du upplever.", "githubIssues": "Rapportera ett problem på GitHub", "githubDiscussions": "Gå med i GitHub-diskussioner", "discord": "Gå med i vår Discord-community", "customerSupport": "Kundsupport"}, "welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {{name}}! Du har {{count}} notifikationer.", "items": {"zero": "Inga objekt", "one": "<PERSON>tt objekt", "other": "{{count}} objekt"}, "confirmation": {"reset_state": "Är du säker på att du vill återställa all status och hemlig lagring i tillägget? Detta kan inte ångras.", "delete_config_profile": "Är du säker på att du vill ta bort denna konfigurationsprofil?", "delete_custom_mode": "Är du säker på att du vill ta bort detta anpassade läge?", "delete_custom_mode_with_rules": "<PERSON>r du säker på att du vill ta bort detta {scope} läge?\n\nDetta kommer också att ta bort den associerade regelmappen på:\n{rulesFolderPath}", "delete_message": "Vad vill du ta bort?", "edit_warning": "Att redigera detta meddelande kommer att ta bort alla efterföljande meddelanden i konversationen. Vill du fortsätta?", "delete_just_this_message": "<PERSON>a detta meddelande", "delete_this_and_subsequent": "<PERSON>ta och alla efterföljande meddelanden", "proceed": "Fortsätt"}, "errors": {"invalid_data_uri": "Ogiltigt data URI-format", "error_copying_image": "Fel vid kopiering av bild: {{errorMessage}}", "error_opening_image": "Fel vid öppning av bild: {{error}}", "error_saving_image": "Fel vid sparande av bild: {{errorMessage}}", "could_not_open_file": "Kunde inte öppna fil: {{errorMessage}}", "could_not_open_file_generic": "Kunde inte öppna fil!", "checkpoint_timeout": "Tidsgräns uppnådd vid försök att återställa checkpoint.", "checkpoint_failed": "Misslyckades med att återställa checkpoint.", "no_workspace": "Öppna en projektmapp först", "update_support_prompt": "<PERSON><PERSON><PERSON><PERSON> med att uppdatera support prompt", "reset_support_prompt": "Misslyckades med att återställa support prompt", "enhance_prompt": "Misslyckades med att förbättra prompt", "get_system_prompt": "Misslyckades med att hämta system prompt", "search_commits": "<PERSON><PERSON><PERSON><PERSON> med att söka commits", "save_api_config": "<PERSON><PERSON><PERSON><PERSON> med att spara api-konfiguration", "create_api_config": "<PERSON><PERSON><PERSON><PERSON> med att skapa api-konfiguration", "rename_api_config": "Misslyckades med att byta namn på api-konfiguration", "load_api_config": "<PERSON><PERSON><PERSON><PERSON> med att ladda api-konfiguration", "delete_api_config": "<PERSON><PERSON><PERSON><PERSON> med att ta bort api-konfiguration", "list_api_config": "Misslyckades med att hämta lista över api-konfiguration", "update_server_timeout": "<PERSON><PERSON><PERSON><PERSON> med att uppdatera server timeout", "hmr_not_running": "Lokal utvecklingsserver körs inte, HMR kommer inte att fungera. Kör 'npm run dev' innan du startar tillägget för att aktivera HMR.", "retrieve_current_mode": "Fel: miss<PERSON><PERSON><PERSON> med att hämta aktuellt läge från tillstånd.", "failed_delete_repo": "<PERSON><PERSON><PERSON><PERSON> med att ta bort associerat shadow repository eller gren: {{error}}", "failed_remove_directory": "<PERSON><PERSON><PERSON><PERSON> med att ta bort uppgiftskatalog: {{error}}", "custom_storage_path_unusable": "Anpassad lagringssökväg \"{{path}}\" är oanvändbar, kommer att använda standardsökväg", "cannot_access_path": "Kan inte komma åt sökväg {{path}}: {{error}}", "settings_import_failed": "Import av inställningar misslyckades: {{error}}.", "mistake_limit_guidance": "Detta kan indikera ett fel i modellens tankeprocess eller oförmåga att använda ett verktyg korrekt, vilket kan mildras med viss användarvägledning (t.ex. \"Försök dela upp uppgiften i mindre steg\").", "violated_organization_allowlist": "Misslyckades med att köra uppgift: den aktuella profilen bryter mot din organisations inställningar", "condense_failed": "Misslyckades med att kondensera kontext", "condense_not_enough_messages": "Inte tillräckligt med meddelanden för att kondensera kontext på {{prevContextTokens}} tokens. Minst {{minimumMessageCount}} meddelanden krävs, men endast {{messageCount}} är tillgängliga.", "condensed_recently": "Kontext kondenserades nyligen; hoppar över detta försök", "condense_handler_invalid": "API-hanterare för kond<PERSON> av kontext är ogiltig", "condense_context_grew": "Kontextstorlek ökade från {{prevContextTokens}} till {{newContextTokens}} under kondensering; hoppar över detta fö<PERSON>ök", "url_timeout": "Webbplatsen tog för lång tid att ladda (timeout). <PERSON>ta kan bero på en långsam anslutning, tung webbplats eller att webbplatsen är tillfälligt otillgänglig. Du kan försöka igen senare eller kontrollera om URL:en är korrekt.", "url_not_found": "Webbplatsadressen kunde inte hittas. Kontrollera om URL:en är korrekt och försök igen.", "no_internet": "Ingen internetanslutning. Kontrollera din nätverksanslutning och försök igen.", "url_forbidden": "Åtkomst till denna webbplats är förbjuden. Webbplatsen kan blockera automatisk åtkomst eller kräva autentisering.", "url_page_not_found": "<PERSON>an hittades inte. Kontrollera om URL:en är korrekt.", "url_fetch_failed": "Misslyckades med att hämta URL-innehåll: {{error}}", "url_fetch_error_with_url": "Fel vid hämtning av innehåll för {{url}}: {{error}}", "share_task_failed": "Misslyckades med att dela uppgift. Försök igen.", "share_no_active_task": "Ingen aktiv uppgift att dela", "share_auth_required": "Autentisering krävs. Logga in för att dela uppgifter.", "share_not_enabled": "Uppgiftsdelning är inte aktiverat för denna organisation.", "share_task_not_found": "Uppgift hittades inte eller åtkomst nekad.", "mode_import_failed": "<PERSON>lyckades med att importera läge: {{error}}", "delete_rules_folder_failed": "<PERSON><PERSON><PERSON><PERSON> med att ta bort regelmapp: {{rulesFolderPath}}. Fel: {{error}}", "claudeCode": {"processExited": "<PERSON>-processen avslutades med kod {{exitCode}}.", "errorOutput": "Felutdata: {{output}}", "processExitedWithError": "<PERSON>-processen avslutades med kod {{exitCode}}. Felutdata: {{output}}", "stoppedWithReason": "<PERSON> stoppades med anledning: {{reason}}", "apiKeyModelPlanMismatch": "API-nycklar och prenumerationsplaner tillåter olika modeller. Se till att den valda modellen ingår i din plan."}, "geminiCli": {"oauthLoadFailed": "<PERSON><PERSON><PERSON><PERSON> med att ladda OAuth-kredentialer. Autentisera dig först: {{error}}", "tokenRefreshFailed": "<PERSON><PERSON><PERSON><PERSON> med att uppdatera OAuth-token: {{error}}", "onboardingTimeout": "Onboarding-operation tog för lång tid efter 60 sekunder. Försök igen senare.", "projectDiscoveryFailed": "Kunde inte upptäcka projekt-ID. Se till att du är autentiserad med 'gemini auth'.", "rateLimitExceeded": "Hastighetsgräns överskriden. Begränsningar för gratis nivå har n<PERSON>tts.", "badRequest": "<PERSON><PERSON><PERSON>g beg<PERSON>ran: {{details}}", "apiError": "Gemini CLI API-fel: {{error}}", "completionError": "Gemini CLI-kompletteringsfel: {{error}}"}}, "warnings": {"no_terminal_content": "Inget terminalinnehåll valt", "missing_task_files": "<PERSON>na uppgifts filer saknas. Vill du ta bort den från uppgiftslistan?", "auto_import_failed": "Misslyckades med att automatiskt importera Kilo Code-inställningar: {{error}}"}, "info": {"no_changes": "Inga ändringar hittades.", "clipboard_copy": "System prompt kopierades till urklipp", "history_cleanup": "<PERSON><PERSON><PERSON> {{count}} uppgift(er) med saknade filer från historiken.", "custom_storage_path_set": "Anpassad lagringssökväg inställd: {{path}}", "default_storage_path": "Återgick till att använda standardlagringssökväg", "settings_imported": "Inställningar importerades framgångsrikt.", "auto_import_success": "Kilo Code-inställningar importerades automatiskt från {{filename}}", "share_link_copied": "Delningslänk kopierad till urklipp", "organization_share_link_copied": "Organisationsdelningslänk kopierad till urklipp!", "public_share_link_copied": "Offentlig delningslänk kopierad till urklipp!", "image_copied_to_clipboard": "Image data URI kopierad till urklipp", "image_saved": "Bild sparad till {{path}}", "mode_exported": "Läge '{{mode}}' exporterades framgångsrikt", "mode_imported": "Läge importerades framgångsrikt"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON> bort", "keep": "<PERSON><PERSON><PERSON><PERSON>"}, "buttons": {"save": "Spara", "edit": "Rediger<PERSON>"}, "tasks": {"canceled": "Uppgiftsfel: Den stoppades och avbröts av användaren.", "deleted": "Uppgiftsfel: Den stoppades och togs bort av användaren.", "incomplete": "Uppgift #{{taskNumber}} (Ofullständig)", "no_messages": "Uppgift #{{taskNumber}} (Inga meddelanden)"}, "storage": {"prompt_custom_path": "Ange anpassad lagringssökväg för konversationshistorik, lämna tomt för att använda standardplats", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "Ange en absolut sökväg (t.ex. D:\\KiloCodeStorage eller /home/<USER>/storage)", "enter_valid_path": "Ange en giltig sökväg"}, "input": {"task_prompt": "Vad ska Kilo Code göra?", "task_placeholder": "<PERSON><PERSON>, hit<PERSON>, fr<PERSON><PERSON> n<PERSON>"}, "customModes": {"errors": {"yamlParseError": "Ogiltig YAML i .kilocodemodes-filen på rad {{line}}. Kontrollera följande:\n• Korrekt indragning (använd mellanslag, inte tabs)\n• Matchande citattecken och klamrar\n• Giltig YAML-syntax", "schemaValidationError": "Ogiltigt format för anpassade lägen i .kilocodemodes:\n{{issues}}", "invalidFormat": "Ogiltigt format för anpassade lägen. Säkerställ att dina inställningar följer korrekt YAML-format.", "updateFailed": "<PERSON><PERSON><PERSON>ades med att uppdatera anpassat läge: {{error}}", "deleteFailed": "<PERSON><PERSON><PERSON><PERSON> med att ta bort anpassat läge: {{error}}", "resetFailed": "Misslyckades med att återställa anpassade lägen: {{error}}", "modeNotFound": "Skrivfel: <PERSON>äge hittades inte", "noWorkspaceForProject": "Ingen arbetsyta hittades för projektspecifikt läge"}, "scope": {"project": "projekt", "global": "globalt"}}, "mdm": {"errors": {"cloud_auth_required": "Din organisation kräver Kilo Code Cloud-autentisering. Logga in för att fortsätta.", "organization_mismatch": "Du måste vara autentiserad med din organisations Kilo Code Cloud-konto.", "verification_failed": "Kunde inte verifiera organisationsautentisering."}}, "prompts": {"deleteMode": {"title": "Ta bort anpassat läge", "description": "<PERSON>r du säker på att du vill ta bort detta {{scope}} läge? Detta kommer också att ta bort den associerade regelmappen på: {{rulesFolderPath}}", "descriptionNoRules": "Är du säker på att du vill ta bort detta anpassade läge?", "confirm": "<PERSON> bort"}}}