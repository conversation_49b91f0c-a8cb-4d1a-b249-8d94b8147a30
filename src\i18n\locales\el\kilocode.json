{"info": {"settings_imported": "Οι ρυθμίσεις εισήχθησαν επιτυχώς."}, "userFeedback": {"message_update_failed": "Αποτυχία ενημέρωσης μηνύματος", "no_checkpoint_found": "Δεν βρέθηκε checkpoint πριν από αυτό το μήνυμα", "message_updated": "Το μήνυμα ενημερώθηκε επιτυχώς"}, "lowCreditWarning": {"title": "Προειδοποίηση Χαμηλών Πιστώσεων!", "message": "Έλεγξε αν μπορείς να προσθέσεις δωρεάν πιστώσεις ή να αγοράσεις περισσότερες!"}, "notLoggedInError": "Δεν είναι δυνατή η ολοκλήρωση του αιτήματος, βεβαιώσου ότι είσαι συνδεδεμένος με τον επιλεγμένο πάροχο.\n\n{{error}}", "rules": {"actions": {"delete": "Διαγραφή", "confirmDelete": "Είσαι σίγουρος ότι θέλεις να διαγράψεις το {{filename}};", "deleted": "Διαγράφηκε το {{filename}}"}, "errors": {"noWorkspaceFound": "Δεν βρέθηκε φάκελος χώρου εργασίας", "fileAlreadyExists": "Το αρχείο {{filename}} υπάρχει ήδη", "failedToCreateRuleFile": "Αποτυχία δημιουργίας αρχείου κανόνα.", "failedToDeleteRuleFile": "Αποτυχία διαγραφής αρχείου κανόνα."}, "templates": {"workflow": {"description": "Περιγραφή ροής εργασίας εδώ...", "stepsHeader": "## Βήματα", "step1": "Βήμα 1", "step2": "Βήμα 2"}, "rule": {"description": "Περιγραφ<PERSON> κανόνα εδώ...", "guidelinesHeader": "## Κατευθυντήριες γραμμές", "guideline1": "Κατευθυντήρια γραμμή 1", "guideline2": "Κατευθυντήρια γραμμή 2"}}}, "commitMessage": {"activated": "Εργαλεί<PERSON> δημιουργ<PERSON>ας μηνυμάτων commit Kilo Code ενεργοποιήθηκε", "gitNotFound": "⚠️ Το αποθετήριο Git δεν βρέθηκε ή το git δεν είναι διαθέσιμο", "gitInitError": "⚠️ Σφάλμα αρχικοποίησης του Git: {{error}}", "generating": "Kilo: Δημιουργία μηνύματος commit...", "noChanges": "<PERSON><PERSON>: Δεν βρέθηκαν αλλαγές για ανάλυση", "generated": "<PERSON><PERSON>: Το μήνυμα commit δημιουργήθηκε!", "generationFailed": "<PERSON><PERSON>: Αποτυχία δημιουργίας μηνύματος commit: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Δημιουργία μηνύματος με χρήση μη καταχωρημένων αλλαγών", "providerRegistered": "<PERSON><PERSON>: Ο πάροχος μηνύματος υποβολής καταχωρήθηκε", "activationFailed": "<PERSON><PERSON>: Αποτυχία ενεργοποίησης της γεννήτριας μηνυμάτων: {{error}}"}, "autocomplete": {"statusBar": {"warning": "$(warning) <PERSON>lo Αυτόματη Συμπλήρωση", "enabled": "$(sparkle) <PERSON>lo Αυτόματη Συμπλήρωση", "tooltip": {"basic": "<PERSON>lo Code Αυτόματη Συμπλήρωση", "tokenError": "Πρέπει να οριστεί ένα έγκυρο διακριτικό για να χρησιμοποιηθεί η αυτόματη συμπλήρωση", "disabled": "Αυτόματη Συμπλήρωση Kilo Code (απενεργοποιημένη)", "sessionTotal": "Συνολικ<PERSON> κόστος συνεδρίας:", "lastCompletion": "Τελευτα<PERSON>α ολοκλήρωση:", "model": "Μοντέλο:"}, "disabled": "$(circle-slash) <PERSON><PERSON> Αυτόματη Συμπλήρωση", "cost": {"lessThanCent": "<0,01$", "zero": "0,00 €"}}, "toggleMessage": "<PERSON><PERSON> Αυτόματη Συμπλήρωση {{status}}"}, "ghost": {"progress": {"analyzing": "Ανάλυση του κώδικά σας...", "generating": "Δημιουργία προτεινόμενων επεξεργασιών...", "processing": "Επεξεργασία προτεινόμενων αλλαγών...", "showing": "Εμφάνιση προτεινόμενων αλλαγών...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Γρήγορη Εργασία", "placeholder": "π.χ., 'αναδι<PERSON>ργάνωσε αυτή τη συνάρτηση ώστε να είναι πιο αποδοτική'"}, "commands": {"cancelSuggestions": "Ακύρωση Προτεινόμενων Αλλαγών", "generateSuggestions": "Kilo Code: Δημιουργία Προτεινόμενων Επεξεργασιών", "displaySuggestions": "Προβολή Προτεινόμενων Τροποποιήσεων", "promptCodeSuggestion": "Γρήγορη Εργασία με Prompt", "applyCurrentSuggestion": "Εφαρμογή Τρέχουσας Προτεινόμενης Επεξεργασίας", "category": "Kilo Code", "applyAllSuggestions": "Εφαρμογή Όλων των Προτεινόμενων Αλλαγών"}, "codeAction": {"title": "Kilo Code: Προτεινόμενες Αλλαγές"}, "chatParticipant": {"name": "Πράκτορας", "fullName": "Πράκτ<PERSON><PERSON><PERSON><PERSON>", "description": "Μπορώ να σας βοηθήσω με γρήγορες εργασίες και προτεινόμενες διορθώσεις."}, "messages": {"provideCodeSuggestions": "Παρέχονται προτάσεις κώδικα..."}}}