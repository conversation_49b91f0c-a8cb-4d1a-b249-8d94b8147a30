{"welcome": {"greeting": "歡迎使用 Kilo Code！", "introText1": "Kilo Code 是一個免費的開源 AI 程式設計代理。", "introText2": "它適用於最新的 AI 模型，如 Claude 4 Sonnet、Gemini 2.5 Pro、GPT-4.1 以及 450 多個其他模型。", "introText3": "建立免費帳戶，獲得價值 20 美元的 token，可用於任何 AI 模型。", "ctaButton": "建立免費帳戶", "manualModeButton": "使用你自己的 API 金鑰", "alreadySignedUp": "已經註冊了？", "loginText": "在此登入"}, "lowCreditWarning": {"addCredit": "添加額度", "lowBalance": "你的 Kilo Code 餘額不足"}, "notifications": {"toolRequest": "工具請求等待核准", "browserAction": "瀏覽器動作等待核准", "command": "指令等待核准"}, "settings": {"sections": {"mcp": "MCP 伺服器"}, "contextManagement": {"allowVeryLargeReads": {"label": "允許超大檔案讀取", "description": "啟用後，Kilo Code 將執行超大檔案或 MCP 輸出讀取，即使存在高機率溢出上下文視窗（內容大小 >80% 的上下文視窗）。"}}, "systemNotifications": {"label": "啟用系統通知", "description": "啟用後 Kilo Code 將發送系統通知，提醒工作完成或錯誤等重要事件。", "testButton": "測試通知", "testTitle": "Kilo Code", "testMessage": "這是來自 Kilo Code 的測試通知。"}, "provider": {"account": "Kilo Code 帳戶", "apiKey": "Kilo Code API 金鑰", "login": "登入 Kilo Code", "logout": "登出 Kilo Code"}}, "chat": {"condense": {"wantsToCondense": "Kilo Code 想要壓縮你的對話", "condenseConversation": "壓縮對話"}}, "newTaskPreview": {"task": "任務"}, "profile": {"title": "個人檔案", "dashboard": "儀表板", "logOut": "登出", "currentBalance": "目前餘額", "loading": "載入中..."}, "docs": "文件", "rules": {"tooltip": "管理 Kilo Code 規則和工作流程", "ariaLabel": "Kilo Code 規則", "tabs": {"rules": "規則", "workflows": "工作流程"}, "description": {"rules": "規則允許你為 Kilo Code 提供在所有模式和所有提示中都應遵循的指令。它們是在你的工作空間或全域範圍內為所有對話包含上下文和偏好的持久方式。", "workflows": "工作流程是為對話準備的範本。工作流程允許你定義經常使用的提示，並可以包含一系列步驟來指導 Kilo Code 完成重複性任務，如部署服務或提交 PR。要調用工作流程，請輸入", "workflowsInChat": "在聊天中。"}, "sections": {"globalRules": "全域規則", "workspaceRules": "工作區規則", "globalWorkflows": "全域工作流程", "workspaceWorkflows": "工作區工作流程"}, "validation": {"invalidFileExtension": "僅允許 .md、.txt 或無檔案副檔名"}, "placeholders": {"workflowName": "工作流程名稱（.md、.txt 或無副檔名）", "ruleName": "規則名稱（.md、.txt 或無副檔名）"}, "newFile": {"newWorkflowFile": "新建工作流程檔案...", "newRuleFile": "新建規則檔案..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "查看 {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_result": "瀏覽器操作結果", "browser_action": "瀏覽器操作", "browser_action_launch": "瀏覽器啟動", "checkpoint_saved": "檢查點已儲存", "command": "指令執行", "completion_result": "完成結果", "command_output": "指令輸出", "condense_context": "壓縮上下文", "error": "錯誤訊息", "followup": "後續問題", "mcp_server_response": "MCP 伺服器回應", "reasoning": "AI 推理", "text": "AI 回覆", "tool": "工具使用", "use_mcp_server": "MCP 伺服器使用情況", "unknown": "未知訊息類型", "user": "使用者回饋"}}}, "userFeedback": {"editCancel": "取消", "send": "發送", "restoreAndSend": "恢復並發送"}, "ideaSuggestionsBox": {"newHere": "新用戶？", "suggestionText": "<suggestionButton>點擊這裡</suggestionButton>獲取有趣的想法，然後點按 <sendIcon /> 看我施展魔法！", "ideas": {"idea1": "建立一個旋轉發光的 3D 球體，能夠回應滑鼠移動。讓應用程式在瀏覽器中運行。", "idea2": "為 Python 軟體開發者建立作品集網站", "idea3": "在瀏覽器中建立金融應用程式模型。然後測試是否可以正常運作", "idea4": "建立一個目錄網站，包含目前最好的 AI 影片生成模型", "idea5": "建立一個 CSS 漸層產生器，匯出自訂樣式表並顯示即時預覽", "idea6": "產生在滑鼠停留時優雅展示動態內容的卡片", "idea7": "建立一個寧靜的互動式星空，跟隨滑鼠手勢移動"}}}