{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Open-Source-KI-Codierungsassistent zum Planen, Erstellen und Korrigieren von Code.", "command.newTask.title": "Neue Aufgabe", "command.explainCode.title": "Code Erklären", "command.fixCode.title": "Code Reparieren", "command.improveCode.title": "Code Verbessern", "command.addToContext.title": "Zum Kontext Hinzufügen", "command.openInNewTab.title": "In Neuem <PERSON>", "command.focusInput.title": "Eingabefeld Fokussieren", "command.setCustomStoragePath.title": "Benutzerdefinierten Speicherpfad Festlegen", "command.importSettings.title": "Einstellungen Importieren", "command.terminal.addToContext.title": "Terminal-Inhalt zum Kontext Hinzufügen", "command.terminal.fixCommand.title": "<PERSON><PERSON> Befehl Reparieren", "command.terminal.explainCommand.title": "Diesen Befehl Erklären", "command.acceptInput.title": "Eingabe/Vorschlag Akzeptieren", "command.generateCommitMessage.title": "Commit-Na<PERSON><PERSON>t mit <PERSON><PERSON> generieren", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "MCP Server", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplatz", "command.openInEditor.title": "Im Editor <PERSON>", "command.settings.title": "Einstellungen", "command.documentation.title": "Dokumentation", "command.profile.title": "Profil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON><PERSON>, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist", "settings.vsCodeLmModelSelector.description": "Einstellungen für die VSCode-Sprachmodell-API", "settings.vsCodeLmModelSelector.vendor.description": "Der Anbieter des Sprachmodells (z.B. copilot)", "settings.vsCodeLmModelSelector.family.description": "Die Familie des Sprachmodells (z.B. gpt-4)", "settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. <PERSON><PERSON>, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "<PERSON>lo <PERSON> Schnelle Problembehebung aktivieren.", "settings.autoImportSettingsPath.description": "Pfad zu einer Kilo Code-Konfigurationsdatei, die beim Start der Erweiterung automatisch importiert wird. Unterstützt absolute Pfade und Pfade relativ zum Home-Verzeichnis (z.B. '~/Documents/kilo-code-settings.json'). <PERSON><PERSON> <PERSON>, um den automatischen Import zu deaktivieren.", "ghost.input.title": "Kilo <PERSON>ei<PERSON>chreiber", "ghost.input.placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, was <PERSON>e programmieren möchten...", "ghost.commands.generateSuggestions": "Kilo Code: Vorgeschlagene Bearbeitungen Generieren", "ghost.commands.displaySuggestions": "Vorgeschlagene Bearbeitungen Anzeigen", "ghost.commands.cancelSuggestions": "Vorgeschlagene Bearbeitungen Abbrechen", "ghost.commands.applyCurrentSuggestion": "Aktuelle Vorgeschlagene Bearbeitung Anwenden", "ghost.commands.applyAllSuggestions": "Alle Vorgeschlagenen Bearbeitungen Anwenden", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "Zur Nächsten Vorgeschlagenen Bearbeitung", "ghost.commands.goToPreviousSuggestion": "Zur Vorherigen Vorgeschlagenen Bearbeitung"}