{"answers": {"yes": "<PERSON><PERSON>", "no": "Ne", "cancel": "Zrušit", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "Ponechat"}, "number_format": {"thousand_suffix": "tis", "million_suffix": "mil", "billion_suffix": "mld"}, "feedback": {"title": "Zpětná vazba", "description": "<PERSON><PERSON><PERSON> tvou zpětnou vazbu nebo ti pomohli s jakýmikoli problémy, se kterými se potýkáš", "githubIssues": "Nahlásit problém na GitHubu", "githubDiscussions": "Připojit se k diskuzím na GitHubu", "discord": "Připojit se k naší komunitě na Discordu", "customerSupport": "Zákaznická podpora"}, "ui": {"search_placeholder": "Hledat..."}, "mermaid": {"loading": "Vytváření diagramu mermaid...", "render_error": "Nepodařilo se vykreslit diagram", "fixing_syntax": "Opravuji syntaxi Mermaid...", "fix_syntax_button": "Opravit syntaxi pomocí AI", "original_code": "Původní kód:", "errors": {"unknown_syntax": "Neznámá chyba syntaxe", "fix_timeout": "Vypršel časový limit požadavku na opravu LLM", "fix_failed": "Oprava LLM selhala", "fix_attempts": "Nepodařilo se opravit syntaxi po {{attempts}} pokusech. Poslední chyba: {{error}}", "no_fix_provided": "LLM neposkytl opravu", "fix_request_failed": "Požadavek na opravu selhal"}, "buttons": {"zoom": "Přiblížení", "zoomIn": "Přiblížit", "zoomOut": "<PERSON><PERSON><PERSON>", "copy": "Kopírovat", "save": "Uložit obrázek", "viewCode": "Zobrazit kód", "viewDiagram": "Zobrazit diagram", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON>"}, "tabs": {"diagram": "Diagram", "code": "<PERSON><PERSON><PERSON>"}, "feedback": {"imageCopied": "Obrázek zkopírován do schránky", "copyError": "Chyba při kopírování obrázku"}}, "file": {"errors": {"invalidDataUri": "Neplatný formát data URI", "copyingImage": "Chyba při kopírování obrázku: {{error}}", "openingImage": "Chyba při otevírání obrázku: {{error}}", "pathNotExists": "Cesta neexistuje: {{path}}", "couldNotOpen": "Nepodařilo se ote<PERSON><PERSON><PERSON>t soubor: {{error}}", "couldNotOpenGeneric": "Nepodařilo se otevřít soubor!"}, "success": {"imageDataUriCopied": "Image data URI zkopírováno do schránky"}}}