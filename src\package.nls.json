{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Open Source AI coding assistant for planning, building, and fixing code.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "New Task", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Modes", "command.history.title": "History", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Open in Editor", "command.settings.title": "Settings", "command.documentation.title": "Documentation", "command.openInNewTab.title": "Open In New Tab", "command.explainCode.title": "Explain Code", "command.fixCode.title": "Fix Code", "command.improveCode.title": "Improve Code", "command.addToContext.title": "Add To Context", "command.focusInput.title": "Focus Input Field", "command.setCustomStoragePath.title": "Set Custom Storage Path", "command.importSettings.title": "Import Settings", "command.terminal.addToContext.title": "Add Terminal Content to Context", "command.terminal.fixCommand.title": "Fix This Command", "command.terminal.explainCommand.title": "Explain This Command", "command.acceptInput.title": "Accept Input/Suggestion", "command.generateCommitMessage.title": "Generate Commit Message with <PERSON><PERSON>", "command.profile.title": "Profile", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled", "settings.vsCodeLmModelSelector.description": "Settings for VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "The vendor of the language model (e.g. copilot)", "settings.vsCodeLmModelSelector.family.description": "The family of the language model (e.g. gpt-4)", "settings.customStoragePath.description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Enable Kilo Code quick fixes", "settings.autoImportSettingsPath.description": "Path to a Kilo Code configuration file to automatically import on extension startup. Supports absolute paths and paths relative to the home directory (e.g. '~/Documents/kilo-code-settings.json'). Leave empty to disable auto-import.", "ghost.input.title": "Press 'Enter' to confirm or 'Escape' to cancel", "ghost.input.placeholder": "Describe what you want to do...", "ghost.commands.generateSuggestions": "Generate Suggested Edits", "ghost.commands.displaySuggestions": "Display Suggested Edits", "ghost.commands.cancelSuggestions": "Cancel Suggested Edits", "ghost.commands.applyCurrentSuggestion": "Apply Current Suggested Edit", "ghost.commands.applyAllSuggestions": "Apply All Suggested Edits", "ghost.commands.promptCodeSuggestion": "Quick Task", "ghost.commands.goToNextSuggestion": "Go To Next Suggestion", "ghost.commands.goToPreviousSuggestion": "Go To Previous Suggestion"}