{"extension": {"name": "Kilo Code", "description": "Помічник з кодування AI з відкритим кодом для планування, створення та виправлення коду."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Відг<PERSON>к", "description": "Ми будемо раді почути твій відгук або допомогти з будь-якими проблемами, які ти відчуваєш.", "githubIssues": "Повідомити про проблему на GitHub", "githubDiscussions": "Приєднатися до обговорень на GitHub", "discord": "Приєднатися до нашої спільноти Discord", "customerSupport": "Підтримка клієнтів"}, "welcome": "Ласкаво просимо, {{name}}! У тебе {{count}} сповіщень.", "items": {"zero": "Немає елементів", "one": "Один елемент", "other": "{{count}} елементів"}, "confirmation": {"reset_state": "Ти впевнений, що хочеш скинути весь стан і секретне сховище в розширенні? Це не можна скасувати.", "delete_config_profile": "Ти впевнений, що хочеш видалити цей профіль конфігурації?", "delete_custom_mode_with_rules": "Ти впевнений, що хочеш видалити цей {scope} режим?\n\nЦе також видалить пов'язану папку правил за адресою:\n{rulesFolderPath}", "delete_message": "Що ти хочеш видалити?", "edit_warning": "Редагування цього повідомлення видалить усі наступні повідомлення в розмові. Хочеш продовжити?", "delete_just_this_message": "Лише це повідомлення", "delete_this_and_subsequent": "Це та всі наступні повідомлення", "proceed": "Продовжити"}, "errors": {"invalid_data_uri": "Недійсний формат data URI", "error_copying_image": "Помилка копіювання зображення: {{errorMessage}}", "error_opening_image": "Помилка відкриття зображення: {{error}}", "error_saving_image": "Помилка збереження зображення: {{errorMessage}}", "could_not_open_file": "Не вдалося відкрити файл: {{errorMessage}}", "could_not_open_file_generic": "Не вдалося відкрити файл!", "checkpoint_timeout": "Час очікування вичерпано під час спроби відновити checkpoint.", "checkpoint_failed": "Не вдалося відновити checkpoint.", "no_workspace": "Будь ласка, спочатку відкрий папку проекту", "update_support_prompt": "Не вдалося оновити support prompt", "reset_support_prompt": "Не вдалося скинути support prompt", "enhance_prompt": "Не вдалося покращити prompt", "get_system_prompt": "Не вдалося отримати system prompt", "search_commits": "Не вдалося знайти коміти", "save_api_config": "Не вдалося зберегти конфігурацію api", "create_api_config": "Не вдалося створити конфігурацію api", "rename_api_config": "Не вдалося перейменувати конфігурацію api", "load_api_config": "Не вдалося завантажити конфігурацію api", "delete_api_config": "Не вдалося видалити конфігурацію api", "list_api_config": "Не вдалося отримати список конфігурації api", "update_server_timeout": "Не вдалося оновити server timeout", "hmr_not_running": "Локальний сервер розробки не працює, HMR не працюватиме. Будь ласка, запусти 'npm run dev' перед запуском розширення, щоб увімкнути HMR.", "retrieve_current_mode": "Помилка: не вдалося отримати поточний режим зі стану.", "failed_delete_repo": "Не вдалося видалити пов'язаний shadow repository або гілку: {{error}}", "failed_remove_directory": "Не вдалося видалити каталог завдання: {{error}}", "custom_storage_path_unusable": "Користувацький шлях зберігання \"{{path}}\" непридатний для використання, буде використано шлях за замовчуванням", "cannot_access_path": "Неможливо отримати доступ до шляху {{path}}: {{error}}", "settings_import_failed": "Імпорт налаштувань не вдався: {{error}}.", "mistake_limit_guidance": "Це може вказувати на збій у процесі мислення моделі або нездатність правильно використовувати інструмент, що можна пом'якшити за допомогою деяких вказівок користувача (наприклад, \"Спробуй розбити завдання на менші кроки\").", "violated_organization_allowlist": "Не вдалося виконати завдання: поточний профіль порушує налаштування твоєї організації", "condense_failed": "Не вдалося стиснути контекст", "condense_not_enough_messages": "Недостатньо повідомлень для стиснення контексту з {{prevContextTokens}} токенів. Потрібно принаймні {{minimumMessageCount}} повідомлень, але доступно лише {{messageCount}}.", "condensed_recently": "Контекст був стиснутий нещодавно; пропускаємо цю спробу", "condense_handler_invalid": "Обробник API для стиснення контексту недійсний", "condense_context_grew": "Розмір контексту збільшився з {{prevContextTokens}} до {{newContextTokens}} під час стиснення; пропускаємо цю спробу", "url_timeout": "Веб-сайт завантажувався занадто довго (тайм-аут). Це може бути через повільне з'єднання, перевантажений веб-сайт або тимчасову недоступність сайту. Ти можеш спробувати пізніше або перевірити правильність URL.", "url_not_found": "Адресу веб-сайту не знайдено. Будь ласка, перевір правильність URL і спробуй ще раз.", "no_internet": "Немає підключення до інтернету. Будь ласка, перевір мережеве з'єднання і спробуй ще раз.", "url_forbidden": "Доступ до цього веб-сайту заборонено. Сайт може блокувати автоматичний доступ або вимагати автентифікації.", "url_page_not_found": "Сторінку не знайдено. Будь ласка, перевір правильність URL.", "url_fetch_failed": "Не вдалося отримати вміст URL: {{error}}", "url_fetch_error_with_url": "Помилка отримання вмісту для {{url}}: {{error}}", "share_task_failed": "Не вдалося поділитися завданням. Будь ласка, спробуй ще раз.", "share_no_active_task": "Немає активного завдання для поділу", "share_auth_required": "Потрібна автентифікація. Будь ласка, увійди, щоб поділитися завданнями.", "share_not_enabled": "Поділ завдань не увімкнено для цієї організації.", "share_task_not_found": "Завдання не знайдено або доступ заборонено.", "claudeCode": {"processExited": "Процес Claude Code завершився з кодом {{exitCode}}.", "errorOutput": "Вивід помилки: {{output}}", "processExitedWithError": "Процес Claude Code завершився з кодом {{exitCode}}. Вивід помилки: {{output}}", "stoppedWithReason": "<PERSON> Code зупинився з причини: {{reason}}", "apiKeyModelPlanMismatch": "Ключі API та плани підписки дозволяють різні моделі. Переконайся, що вибрана модель включена в твій план."}, "mode_import_failed": "Не вдалося імпортувати режим: {{error}}", "delete_rules_folder_failed": "Не вдалося видалити папку правил: {{rulesFolderPath}}. Помилка: {{error}}", "geminiCli": {"oauthLoadFailed": "Не вдалося завантажити OAuth дані для входу. Будь ласка, спочатку автентифікуйся: {{error}}", "tokenRefreshFailed": "Не вдалося оновити OAuth token: {{error}}", "onboardingTimeout": "Операція onboarding перевищила час очікування після 60 секунд. Будь ласка, спробуй пізніше.", "projectDiscoveryFailed": "Не вдалося знайти ID проекту. Переконайся, що ти автентифікований за допомогою 'gemini auth'.", "rateLimitExceeded": "Перевищено ліміт швидкості. Досягнуто обмежень безкоштовного рівня.", "badRequest": "Неправильний запит: {{details}}", "apiError": "Помилка Gemini CLI API: {{error}}", "completionError": "Помилка завершення Gemini CLI: {{error}}"}}, "warnings": {"no_terminal_content": "Не вибрано вміст терміналу", "missing_task_files": "Файли цього завдання відсутні. Хочеш видалити його зі списку завдань?", "auto_import_failed": "Не вдалося автоматично імпортувати налаштування Kilo Code: {{error}}"}, "info": {"no_changes": "Змін не знайдено.", "clipboard_copy": "System prompt успішно скопійовано в буфер обміну", "history_cleanup": "Очищено {{count}} завдань з відсутніми файлами з історії.", "custom_storage_path_set": "Встановлено користувацький шлях зберігання: {{path}}", "default_storage_path": "Повернуто до використання шляху зберігання за замовчуванням", "settings_imported": "Налаштування успішно імпортовано.", "auto_import_success": "Налаштування Kilo Code автоматично імпортовано з {{filename}}", "share_link_copied": "Посилання для поділу скопійовано в буфер обміну", "organization_share_link_copied": "Посилання для поділу організації скопійовано в буфер обміну!", "public_share_link_copied": "Публічне посилання для поділу скопійовано в буфер обміну!", "image_copied_to_clipboard": "Image data URI скопійовано в буфер обміну", "image_saved": "Зображення збережено в {{path}}", "mode_exported": "Режим '{{mode}}' успішно експортовано", "mode_imported": "Режим успішно імпортовано"}, "answers": {"yes": "Так", "no": "Ні", "cancel": "Скасувати", "remove": "Видалити", "keep": "Залишити"}, "buttons": {"save": "Зберегти", "edit": "Редагувати"}, "tasks": {"canceled": "Помилка завдання: Воно було зупинено та скасовано користувачем.", "deleted": "Збій завдання: Воно було зупинено та видалено користувачем.", "incomplete": "Завдання #{{taskNumber}} (Незавершене)", "no_messages": "Завдання #{{taskNumber}} (Без повідомлень)"}, "storage": {"prompt_custom_path": "Введи користувацький шлях зберігання історії розмов, залиш порожнім для використання розташування за замовчуванням", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "Будь ласка, введи абсолютний шлях (наприклад, D:\\KiloCodeStorage або /home/<USER>/storage)", "enter_valid_path": "Будь ласка, введи дійсний шлях"}, "input": {"task_prompt": "Що має зробити Kilo Code?", "task_placeholder": "Створити, зна<PERSON>ти, запитати щось"}, "customModes": {"errors": {"yamlParseError": "Недійсний YAML у файлі .kilocodemodes на рядку {{line}}. Будь ласка, перевірте:\n• Правильні відступи (використовуйте пробіли, а не табуляції)\n• Відповідні лапки та дужки\n• Дійсний синтаксис YAML", "schemaValidationError": "Недійсний формат користувацьких режимів у .kilocodemodes:\n{{issues}}", "invalidFormat": "Недійсний формат користувацьких режимів. Будь ласка, переконайтеся, що ваші налаштування відповідають правильному формату YAML.", "updateFailed": "Не вдалося оновити користувацький режим: {{error}}", "deleteFailed": "Не вдалося видалити користувацький режим: {{error}}", "resetFailed": "Не вдалося скинути користувацькі режими: {{error}}", "modeNotFound": "Помилка запису: Режим не знайдено", "noWorkspaceForProject": "Не знайдено папку робочого простору для режиму конкретного проекту"}, "scope": {"project": "проект", "global": "глобальний"}}, "mdm": {"errors": {"cloud_auth_required": "Твоя організація вимагає автентифікації Kilo Code Cloud. Будь ласка, увійди, щоб продовжити.", "organization_mismatch": "Ти повинен бути автентифікований з обліковим записом Kilo Code Cloud твоєї організації.", "verification_failed": "Не вдалося перевірити автентифікацію організації."}}, "prompts": {"deleteMode": {"title": "Видалити користувацький режим", "description": "Ти впевнений, що хочеш видалити цей {{scope}} режим? Це також видалить пов'язану папку правил за адресою: {{rulesFolderPath}}", "descriptionNoRules": "Ти впевнений, що хочеш видалити цей користувацький режим?", "confirm": "Видалити"}}}