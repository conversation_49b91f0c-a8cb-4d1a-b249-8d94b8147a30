{"welcome": {"greeting": "Benvingut a Kilo Code!", "introText1": "Kilo Code és un agent de codificació d'IA gratuït i de codi obert.", "introText2": "Funciona amb els models d'IA més recents com Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, i més de 450 més.", "introText3": "Crea un compte gratuït i obtén $20 en tokens per utilitzar en qualsevol model d'IA.", "ctaButton": "<PERSON>rear un compte gratuït", "manualModeButton": "Utilitza la teva pròpia clau API", "alreadySignedUp": "Ja t'has registrat?", "loginText": "Inicia se<PERSON> aqu<PERSON>"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON><PERSON> cr<PERSON>", "lowBalance": "El teu saldo de Kilo Code és baix"}, "notifications": {"toolRequest": "Sol·licitud d'eina pendent d'aprovació", "browserAction": "Acció del navegador pendent d'aprovació", "command": "Ordre pendent d'aprovació"}, "settings": {"sections": {"mcp": "Servidors MCP"}, "provider": {"account": "Compte de Kilo <PERSON>", "apiKey": "Clau API de Kilo Code", "login": "Iniciar <PERSON><PERSON><PERSON> a Kilo <PERSON>", "logout": "<PERSON><PERSON>"}, "contextManagement": {"allowVeryLargeReads": {"label": "Permet lectures de fitxers molt grans", "description": "Quan està activat, Kilo <PERSON> realitzarà lectures de fitxers o sortides MCP molt grans encara que hi hagi una alta probabilitat de desbordar la finestra de context (mida del contingut >80% de la finestra de context)."}}, "systemNotifications": {"label": "Activar notificacions del sistema", "description": "Quan està activat, Kilo Code enviarà notificacions del sistema per a esdeveniments importants com la finalització de tasques o errors.", "testButton": "<PERSON><PERSON>", "testTitle": "Kilo Code", "testMessage": "Aquesta és una notificació de prova de Kilo Code."}}, "profile": {"title": "Perfil", "dashboard": "Tauler de control", "logOut": "<PERSON><PERSON>", "currentBalance": "SALDO ACTUAL", "loading": "Carregant..."}, "chat": {"condense": {"wantsToCondense": "Kilo Code vol condensar la teva conversa", "condenseConversation": "Condensar Conversa"}}, "newTaskPreview": {"task": "Tasca"}, "docs": "Documentació", "rules": {"tooltip": "Gestionar Regles i Fluxos de Treball de Kilo Code", "ariaLabel": "Regles de Kilo Code", "tabs": {"rules": "<PERSON><PERSON>", "workflows": "Fluxos de Treball"}, "description": {"rules": "Les regles et permeten proporcionar a Kilo Code instruccions que ha de seguir en tots els modes i per a tots els prompts. Són una manera persistent d'incloure context i preferències per a totes les converses al teu espai de treball o globalment.", "workflows": "Els fluxos de treball són una plantilla preparada per a una conversa. Els fluxos de treball et permeten definir prompts que uses freqüentment, i poden incloure una sèrie de passos per guiar Kilo Code a través de tasques repetitives, com desplegar un servei o enviar una PR. Per invocar un flux de treball, escriu", "workflowsInChat": "al xat."}, "sections": {"globalRules": "Regles Globals", "workspaceRules": "Regles de l'Espai de Treball", "globalWorkflows": "Fluxos de Treball Globals", "workspaceWorkflows": "Fluxos de Treball de l'Espai de Treball"}, "validation": {"invalidFileExtension": "Només es permeten extensions .md, .txt o sense extensió"}, "placeholders": {"workflowName": "nom-flux-treball (.md, .txt o sense extensió)", "ruleName": "nom-regla (.md, .txt o sense extensió)"}, "newFile": {"newWorkflowFile": "Nou fitxer de flux de treball...", "newRuleFile": "Nou fitxer de regla..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "iniciar <PERSON>", "browser_action": "acció del navegador", "browser_action_result": "resultat de l'acció del navegador", "checkpoint_saved": "punt de control desat", "condense_context": "condensar context", "command_output": "sortida de comanda", "completion_result": "resultat de la compleció", "command": "execució de comandes", "error": "missatge d'error", "followup": "pregunta de seguiment", "mcp_server_response": "Resposta del servidor MCP", "text": "Resposta de la IA", "reasoning": "Raonament d'IA", "unknown": "tipus de missatge desconegut", "tool": "ús de l'eina", "use_mcp_server": "Ús del servidor MCP", "user": "comentaris dels usuaris"}, "clickToScroll": "<PERSON>eure {{messageType}} (núm. {{messageNumber}})"}}, "userFeedback": {"editCancel": "Cancel·lar", "send": "Enviar", "restoreAndSend": "Restaurar i Enviar"}, "ideaSuggestionsBox": {"newHere": "Nou aquí?", "suggestionText": "<suggestionButton>Fes clic aquí</suggestionButton> per a una idea genial, després toca <sendIcon /> i mira'm fer màgia!", "ideas": {"idea1": "Crea una esfera 3D que giri i brilli i respongui als moviments del ratolí. Fes que l'aplicació funcioni al navegador.", "idea2": "Crea un lloc web de cartera per a un desenvolupador de programari Python", "idea3": "Crea una maqueta d'aplicació financera al navegador. Després prova si funciona", "idea4": "Crea un lloc web directori que contingui els millors models de generació de vídeo d'IA actualment", "idea5": "Crea un generador de gradients CSS que exporti fulls d'estil personalitzats i mostri una previsualització en viu", "idea6": "Genera targetes que revelin contingut dinàmic de manera bonica quan s'hi passi el ratolí per sobre", "idea7": "Crea un camp d'estrelles interactiu i tranquil que es mogui amb gestos del ratolí"}}}