{"info": {"settings_imported": "Einstellungen erfolgreich importiert."}, "userFeedback": {"message_update_failed": "Fehler beim Aktualisieren der Nachricht", "no_checkpoint_found": "<PERSON><PERSON> Checkpoint vor dieser Nachricht gefunden", "message_updated": "Nachricht erfolgreich aktualisiert"}, "lowCreditWarning": {"title": "Warnung: <PERSON><PERSON><PERSON><PERSON>!", "message": "<PERSON><PERSON><PERSON><PERSON>, ob <PERSON><PERSON> kostenloses G<PERSON>aben aufladen oder mehr kaufen können!"}, "notLoggedInError": "Anfrage kann nicht abgeschlossen werden. <PERSON><PERSON> sic<PERSON>, dass du mit dem ausgewählten Anbieter verbunden und angemeldet bist.\n\n{{error}}", "rules": {"actions": {"delete": "Löschen", "confirmDelete": "<PERSON><PERSON> du sicher, dass du {{filename}} löschen möchtest?", "deleted": "{{filename}} gelöscht"}, "errors": {"noWorkspaceFound": "<PERSON>in Arbeitsbereich-Ordner gefunden", "fileAlreadyExists": "Datei {{filename}} existiert bereits", "failedToCreateRuleFile": "Regel-Date<PERSON> konnte nicht erstellt werden.", "failedToDeleteRuleFile": "Regel-Datei konnte nicht gelöscht werden."}, "templates": {"workflow": {"description": "Workflow-Beschreibung hier...", "stepsHeader": "## <PERSON><PERSON><PERSON>", "step1": "Schritt 1", "step2": "Schritt 2"}, "rule": {"description": "Regel-Beschreibung hier...", "guidelinesHeader": "## <PERSON><PERSON><PERSON><PERSON>", "guideline1": "Richtlinie 1", "guideline2": "Richtlinie 2"}}}, "commitMessage": {"activated": "Kilo Code Commit-Nachrichten-Generator aktiviert", "gitNotFound": "⚠️ Git-Repository nicht gefunden oder Git nicht verfügbar", "gitInitError": "⚠️ Git-Initialisierungsfehler: {{error}}", "generating": "Kilo: <PERSON><PERSON>e Commit-Nachricht...", "noChanges": "Kilo: <PERSON><PERSON>nderungen zum Analysieren gefunden", "generated": "Kilo: Commit-<PERSON><PERSON><PERSON><PERSON> generiert!", "generationFailed": "Kilo: Fehler beim Generieren der Commit-Nachricht: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON><PERSON><PERSON><PERSON> mit nicht bereitgestellten Änderungen generieren", "activationFailed": "Kilo: Der Nachrichtengenerator konnte nicht aktiviert werden: {{error}}", "providerRegistered": "Kilo: Commit-Nachrichtenanbieter registriert"}, "autocomplete": {"statusBar": {"disabled": "$(circle-slash) Kilo Autovervollständigung", "tooltip": {"basic": "Kilo Code Autovervollständigung", "sessionTotal": "Gesamtkosten der Sitzung:", "disabled": "Kilo Code Autovervollständigung (deaktiviert)", "tokenError": "Ein gültiges Token muss eingestellt werden, um die Autovervollständigung zu verwenden", "lastCompletion": "Letzte Vervollständigung:", "model": "Modell:"}, "warning": "$(warning) Kilo Autovervollständigung", "enabled": "$(sparkle) Kilo Autovervollständigung", "cost": {"lessThanCent": "<0,01 €", "zero": "0,00 €"}}, "toggleMessage": "Kilo Autovervollständigung {{status}}"}, "ghost": {"progress": {"generating": "Generiere vorgeschlagene Änderungen...", "analyzing": "Analysiere deinen Code...", "processing": "Verarbeitung von Änderungsvorschlägen...", "showing": "Zeige Bearbeitungsvorschläge an...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON><PERSON>ufgabe", "placeholder": "z.B. 'diese Funktion umgestalten, um effizienter zu sein'"}, "commands": {"generateSuggestions": "Kilo Code: Vorgeschlagene Änderungen generieren", "displaySuggestions": "Vorgeschlagene Änderungen anzeigen", "cancelSuggestions": "Vorgeschlagene Änderungen abbrechen", "applyAllSuggestions": "Alle vorgeschlagenen Änderungen übernehmen", "promptCodeSuggestion": "Schnellaufgabe erstellen", "applyCurrentSuggestion": "Aktuelle Vorgeschlagene Änderung Übernehmen", "category": "Kilo Code"}, "chatParticipant": {"name": "Agent", "fullName": "Kilo Code Agent", "description": "Ich kann Ihnen mit schnellen Aufgaben und vorgeschlagenen Änderungen helfen."}, "codeAction": {"title": "Kilo Code: Vorgeschlagene Änderungen"}, "messages": {"provideCodeSuggestions": "Code-Vorschläge werden bereitgestellt..."}}}