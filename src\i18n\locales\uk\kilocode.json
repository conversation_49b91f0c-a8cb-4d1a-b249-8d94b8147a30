{"info": {"settings_imported": "Налаштування успішно імпортовано."}, "userFeedback": {"message_update_failed": "Не вдалося оновити повідомлення", "no_checkpoint_found": "Не знайдено checkpoint перед цим повідомленням", "message_updated": "Повідомлення успішно оновлено"}, "lowCreditWarning": {"title": "Попередження про низький кредит!", "message": "Перевір, чи можеш ти поповнити безкоштовними кредитами або придбати більше!"}, "notLoggedInError": "Не вдається виконати запит, переконайся, що ти підключений і увійшов з обраним провайдером.\n\n{{error}}", "rules": {"actions": {"delete": "Видалити", "confirmDelete": "Ти впевнений, що хочеш видалити {{filename}}?", "deleted": "Видалено {{filename}}"}, "errors": {"noWorkspaceFound": "Не знайдено папку робочої області", "fileAlreadyExists": "Файл {{filename}} вже існує", "failedToCreateRuleFile": "Не вдалося створити файл правила.", "failedToDeleteRuleFile": "Не вдалося видалити файл правила."}, "templates": {"workflow": {"description": "Опис робочого процесу тут...", "stepsHeader": "## Кро<PERSON>и", "step1": "Крок 1", "step2": "Крок 2"}, "rule": {"description": "Опис правила тут...", "guidelinesHeader": "## Рекомендації", "guideline1": "Рекомендація 1", "guideline2": "Рекомендація 2"}}}, "commitMessage": {"activated": "Генератор повідомлень для коміту Kilo Code активовано", "gitNotFound": "⚠️ Git репозиторій не знайдено або git недоступний", "gitInitError": "⚠️ Помилка ініціалізації Git: {{error}}", "generating": "Kilo: Генерація повідомлення коміту...", "noChanges": "Kilo: Не знайдено змін для аналізу", "generated": "Kilo: Повідомлення коміту згенеровано!", "generationFailed": "Kilo: Не вдалося створити повідомлення коміту: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Створення повідомлення з використанням незафіксованих змін", "activationFailed": "Kilo: Не вдалося активувати генератор повідомлень: {{error}}", "providerRegistered": "Kilo: Постачальник повідомлень коміту зареєстрований"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Автодоповнення", "disabled": "$(circle-slash) <PERSON>lo Автодоповнення", "tooltip": {"basic": "Автодоповнення Kilo Code", "tokenError": "Для використання автозаповнення необхідно встановити дійсний токен", "disabled": "Kilo Code Автозаповнення (вимкнено)", "lastCompletion": "Останнє завершення:", "model": "Модель:", "sessionTotal": "Зага<PERSON>ьна вартість сеансу:"}, "warning": "$(warning) Kilo Автодоповнення", "cost": {"zero": "0,00 ₴", "lessThanCent": "<$0,01"}}, "toggleMessage": "<PERSON><PERSON> Автодоповнення {{status}}"}, "ghost": {"progress": {"analyzing": "Аналізуємо ваш код...", "processing": "Обробка запропонованих редагувань...", "generating": "Генерування запропонованих виправлень...", "showing": "Відображення запропонованих змін...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Швидке Завдання", "placeholder": "наприклад, 'переробіть цю функцію, щоб вона була ефективнішою'"}, "commands": {"generateSuggestions": "Kilo Code: Створення пропонованих виправлень", "displaySuggestions": "Показати запропоновані редагування", "applyCurrentSuggestion": "Застосувати поточну запропоновану правку", "cancelSuggestions": "Скасувати запропоновані правки", "promptCodeSuggestion": "Швидке Завдання", "category": "Kilo Code", "applyAllSuggestions": "Застосувати всі запропоновані редагування"}, "codeAction": {"title": "Kilo Code: Запропоновані правки"}, "chatParticipant": {"fullName": "<PERSON>lo <PERSON> Агент", "description": "Я можу допомогти вам зі швидкими завданнями та запропонувати правки.", "name": "Агент"}, "messages": {"provideCodeSuggestions": "Надаємо пропозиції коду..."}}}