{"greeting": "Γεια, είμαι ο Kilo Code!", "introduction": "<strong>Ο Kilo Code είναι ο κορυ<PERSON>α<PERSON>ος αυτόνομος βοηθός κωδικοποίησης.</strong> Ετοιμάσου να σχεδιάσεις, να γράψεις κώδικα, να διορθώσεις σφάλματα και να ενισχύσεις την παραγωγικότητά σου όπως ποτέ πριν. Για να συνεχίσεις, ο <PERSON><PERSON> Code χρειάζεται ένα API key.", "notice": "Για να ξεκινή<PERSON>ει<PERSON>, αυτή η επέκταση χρειάζεται έναν πάροχο API.", "start": "Πάμε!", "routers": {"requesty": {"description": "Ο βελτιστοποιημένος LLM router σου", "incentive": "$1 δωρεάν πίστωση"}, "openrouter": {"description": "Μια ενοποιημένη διεπαφή για LLMs"}}, "chooseProvider": "Για να κάνει τα μαγικά του, ο <PERSON>lo Code χρειάζεται ένα API key.", "startRouter": "Συνιστούμε τη χρήση ενός LLM Router:", "startCustom": "Ή μπορείς να φέρεις το δικό σου API key παρόχου:", "telemetry": {"title": "Βοήθησε να Βελτιώσουμε τον Kilo Code", "anonymousTelemetry": "Στείλε δεδομένα σφαλμάτων και χρήσης για να μας βοηθήσεις να διορθώσουμε σφάλματα και να βελτιώσουμε την επέκταση. Δεν στέλνεται ποτέ κώδικας, prompts ή προσωπικές πληροφορίες.", "changeSettings": "Μπορ<PERSON><PERSON>ς πάντα να το αλλάξεις αυτό στο κάτω μέρος των <settingsLink>ρυθμίσεων</settingsLink>", "settings": "ρυθμίσεις", "allow": "Επίτρεψε", "deny": "Απόρριψε"}, "importSettings": "Εισαγωγή Ρυθμίσεων"}