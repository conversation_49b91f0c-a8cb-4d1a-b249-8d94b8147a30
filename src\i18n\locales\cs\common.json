{"extension": {"name": "Kilo Code", "description": "Open Source AI asistent pro kódování pro plánování, vytváření a opravy kódu."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Zpětná vazba", "description": "<PERSON><PERSON><PERSON> tvou zpětnou vazbu nebo ti pomohli s jakýmikoli problémy, k<PERSON><PERSON>.", "githubIssues": "Nahlásit problém na GitHubu", "githubDiscussions": "Připojit se k diskuzím na GitHubu", "discord": "Připoj se k naší Discord komunitě", "customerSupport": "Zákaznická podpora"}, "welcome": "<PERSON><PERSON><PERSON><PERSON>, {{name}}! <PERSON><PERSON><PERSON> {{count}} oznámení.", "items": {"zero": "<PERSON><PERSON><PERSON><PERSON>", "one": "Jedna <PERSON>", "other": "{{count}} <PERSON><PERSON><PERSON>"}, "confirmation": {"reset_state": "<PERSON><PERSON> si jist<PERSON>, <PERSON><PERSON> ch<PERSON><PERSON> resetovat veškerý stav a tajné úložiště v rozšíření? Toto nelze vrátit zpět.", "delete_config_profile": "<PERSON><PERSON> si ji<PERSON>, že ch<PERSON><PERSON> smazat tento konfigurační profil?", "delete_custom_mode_with_rules": "<PERSON><PERSON> si jist<PERSON>, ž<PERSON> ch<PERSON><PERSON> smazat tento {scope} režim?\n\nToto také smaže související složku pravidel na:\n{rulesFolderPath}", "delete_message": "Co bys ch<PERSON><PERSON><PERSON>?", "edit_warning": "Úprava této zprávy smaže všechny následující zprávy v konverzaci. Chceš pokračovat?", "delete_just_this_message": "Pouze tuto zprávu", "delete_this_and_subsequent": "Tuto a všechny následující zprávy", "proceed": "Po<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"invalid_data_uri": "Neplatný formát data URI", "error_copying_image": "Chyba při kopírování obrázku: {{errorMessage}}", "error_opening_image": "Chyba při otevírání obrázku: {{error}}", "error_saving_image": "Chyba při ukládání obrázku: {{errorMessage}}", "could_not_open_file": "<PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON> soubor: {{errorMessage}}", "could_not_open_file_generic": "<PERSON><PERSON>ze otevřít soubor!", "checkpoint_timeout": "Vypršel <PERSON>ový limit při pokusu o obnovení checkpointu.", "checkpoint_failed": "Obnovení checkpointu selhalo.", "no_workspace": "Nejprve prosím otevři složku projektu", "update_support_prompt": "Aktualizace support promptu selhala", "reset_support_prompt": "Reset support promptu selhal", "enhance_prompt": "Vylepšení promptu selhalo", "get_system_prompt": "Získání system promptu selhalo", "search_commits": "Vyhledáván<PERSON> se<PERSON>", "save_api_config": "Uložení konfigurace api selhalo", "create_api_config": "Vytvoření konfigurace api selhalo", "rename_api_config": "Přejmenování konfigurace api selhalo", "load_api_config": "Načtení konfigurace api sel<PERSON>o", "delete_api_config": "Smazání konfigurace api selhalo", "list_api_config": "Získání seznamu konfigurace api selhalo", "update_server_timeout": "Aktualizace server timeout selhala", "hmr_not_running": "Lokální vývojový server neb<PERSON><PERSON><PERSON>, HMR nebude fungovat. Prosím spusť 'npm run dev' před spuštěním rozšíření pro aktivaci HMR.", "retrieve_current_mode": "Chyba: nepoda<PERSON><PERSON> se získat aktuální režim ze stavu.", "failed_delete_repo": "Smazání souvisejícího shadow repository nebo větve selhalo: {{error}}", "failed_remove_directory": "Odstranění ad<PERSON>: {{error}}", "custom_storage_path_unusable": "Vlastní cesta úložiště \"{{path}}\" je nepo<PERSON>, bude použita výchozí cesta", "cannot_access_path": "<PERSON><PERSON><PERSON> př<PERSON><PERSON>t k cestě {{path}}: {{error}}", "settings_import_failed": "Import nastavení selhal: {{error}}.", "mistake_limit_guidance": "To může naznačovat selhání v myšlenkovém procesu modelu nebo neschopnost správně používat nástroj, což lze zmírnit pomocí nějakého vedení uživatele (např. \"Zkus rozdělit úkol na menší kroky\").", "violated_organization_allowlist": "Spuštění <PERSON>: aktuální profil porušuje nastavení tvé organizace", "condense_failed": "Zhušťování kontextu sel<PERSON>o", "condense_not_enough_messages": "Nedostatek zpráv pro zhuštění kontextu {{prevContextTokens}} tokenů. Je vyžadováno alespoň {{minimumMessageCount}} zpr<PERSON>v, ale k dispozici je pouze {{messageCount}}.", "condensed_recently": "Kontext byl nedáv<PERSON>; přeskaku<PERSON> tento pokus", "condense_handler_invalid": "API handler pro <PERSON>hušťování kontextu je neplat<PERSON>ý", "condense_context_grew": "Velikost kontextu se zvě<PERSON>š<PERSON> z {{prevContextTokens}} na {{newContextTokens}} během zhušťování; přeskakuji tento pokus", "url_timeout": "Načítání webové stránky trvalo př<PERSON> (vypršel časový limit). <PERSON><PERSON><PERSON><PERSON> to být způsobeno pomalým připojením, náročnou webovou stránkou nebo dočasnou nedostupností webu. <PERSON><PERSON><PERSON><PERSON><PERSON> to zkusit později nebo zkontrolovat, zda je URL správná.", "url_not_found": "Webová adresa nebyla nalezena. Zkontroluj prosím, zda je URL správná, a zkus to znovu.", "no_internet": "Žádné internetové připojení. Zkontroluj prosím své síťové připojení a zkus to znovu.", "url_forbidden": "Přístup k této webové stránce je zakázán. Web může blokovat automatizovaný přístup nebo vyžadovat autentizaci.", "url_page_not_found": "Stránka nebyla nalezena. Zkontroluj prosím, zda je URL správná.", "url_fetch_failed": "Načtení obsahu URL selhalo: {{error}}", "url_fetch_error_with_url": "Chyba při načítání obsahu pro {{url}}: {{error}}", "share_task_failed": "Sdílení <PERSON> se<PERSON>. Zkus to prosím znovu.", "share_no_active_task": "Žádná aktivní úloha ke sdílení", "share_auth_required": "Je vyžadována autentizace. Prosím přihlas se pro sdílení úloh.", "share_not_enabled": "Sdílení <PERSON> není pro tuto organizaci povoleno.", "share_task_not_found": "Úloha nebyla nalezena nebo byl přístup odepřen.", "mode_import_failed": "Import re<PERSON><PERSON><PERSON>: {{error}}", "delete_rules_folder_failed": "Smazán<PERSON> s<PERSON> pravidel selhalo: {{rulesFolderPath}}. Chyba: {{error}}", "claudeCode": {"processExited": "Proces Claude Code skončil s kódem {{exitCode}}.", "errorOutput": "Chybový výstup: {{output}}", "processExitedWithError": "Proces <PERSON> skončil s kódem {{exitCode}}. Chybový výstup: {{output}}", "stoppedWithReason": "<PERSON> se zastavil s důvodem: {{reason}}", "apiKeyModelPlanMismatch": "API klíče a plány předplatného umožňují různé modely. <PERSON><PERSON><PERSON><PERSON> se, že vybraný model je zahrnut v tvém plánu."}, "geminiCli": {"oauthLoadFailed": "Nepodařilo se načíst OAuth přihlašovací údaje. Nejprve se prosím autentizujte: {{error}}", "tokenRefreshFailed": "Nepodařilo se obnovit OAuth token: {{error}}", "onboardingTimeout": "Onboarding operace vypršela po 60 sekundách. Zkuste to prosím později.", "projectDiscoveryFailed": "Nepodařilo se objevit ID projektu. Ujistěte se, že jste autentizováni pomocí 'gemini auth'.", "rateLimitExceeded": "Překročen limit rychlosti. Limity bezplatné úrovně byly <PERSON>.", "badRequest": "Špatný požadavek: {{details}}", "apiError": "Chyba Gemini CLI API: {{error}}", "completionError": "Chyba dokončení Gemini CLI: {{error}}"}}, "warnings": {"no_terminal_content": "Není vybrán žádný obsah terminálu", "missing_task_files": "So<PERSON>ory této úlohy chybí. Chceš ji odstranit ze seznamu úloh?", "auto_import_failed": "Automatický import nastavení <PERSON>lo <PERSON> selhal: {{error}}"}, "info": {"no_changes": "Nebyly nalezeny žádné změny.", "clipboard_copy": "System prompt byl úspěš<PERSON>ě zkopírován do schránky", "history_cleanup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} úloh s chybějícími soubory z historie.", "custom_storage_path_set": "Nastavena vlastní cesta úložiště: {{path}}", "default_storage_path": "Návrat k použití výchozí cesty úložiště", "settings_imported": "Nastavení byla úspěšně importována.", "auto_import_success": "Nastavení Kilo Code byla automaticky importována ze souboru {{filename}}", "share_link_copied": "Odkaz pro sdílení zkopírován do schránky", "organization_share_link_copied": "Odkaz pro sdílení organizace zkopírován do schránky!", "public_share_link_copied": "Veřejný odkaz pro sdílení zkopírován do schránky!", "image_copied_to_clipboard": "Image data URI zkopírováno do schránky", "image_saved": "Obrázek uložen do {{path}}", "mode_exported": "<PERSON><PERSON><PERSON> '{{mode}}' byl <PERSON><PERSON>n", "mode_imported": "<PERSON><PERSON><PERSON>sp<PERSON>š<PERSON>n"}, "answers": {"yes": "<PERSON><PERSON>", "no": "Ne", "cancel": "Zrušit", "remove": "Odstranit", "keep": "Ponechat"}, "buttons": {"save": "Uložit", "edit": "<PERSON><PERSON><PERSON><PERSON>"}, "tasks": {"canceled": "Chyba úlohy: <PERSON><PERSON> a zrušena uživatelem.", "deleted": "<PERSON>l<PERSON><PERSON><PERSON>: <PERSON><PERSON> a smazána uživatelem.", "incomplete": "Úkol #{{taskNumber}} (Neúplný)", "no_messages": "Úkol #{{taskNumber}} (<PERSON><PERSON><PERSON><PERSON>)"}, "storage": {"prompt_custom_path": "Zadej vlastní cestu úložiště historie konverzací, ponech prázdné pro použití výchozího umístění", "path_placeholder": "D:\\KiloCodeStorage", "enter_absolute_path": "Prosím zadej absolutní cestu (např. D:\\KiloCodeStorage nebo /home/<USER>/storage)", "enter_valid_path": "Prosím zadej platnou cestu"}, "input": {"task_prompt": "Co by m<PERSON><PERSON> udělat?", "task_placeholder": "Vyt<PERSON><PERSON><PERSON>, naj<PERSON><PERSON>, zeptat se na něco"}, "customModes": {"errors": {"yamlParseError": "Neplatný YAML v souboru .kilocodemodes na řádku {{line}}. Zkontroluj prosím:\n• Správné odsazení (použ<PERSON>j mezery, ne tabulátory)\n• Odpovídající uvozovky a závorky\n• Platnou YAML syntaxi", "schemaValidationError": "Neplatný formát vlastních režimů v .kilocodemodes:\n{{issues}}", "invalidFormat": "Neplatný formát vlastních režimů. Ujisti se prosím, že tvá nastavení dodržují správný YAML formát.", "updateFailed": "Nepodařilo se aktualizovat vlastní režim: {{error}}", "deleteFailed": "Nepodařilo se smazat vlastní <PERSON>: {{error}}", "resetFailed": "Nepodařilo se resetovat vlastní režim<PERSON>: {{error}}", "modeNotFound": "Chyba zápisu: <PERSON><PERSON><PERSON>", "noWorkspaceForProject": "Pro projektově specifický režim nebyla nalezena žádná složka workspace"}, "scope": {"project": "projekt", "global": "globální"}}, "mdm": {"errors": {"cloud_auth_required": "Tvá organizace vyžaduje autentizaci Kilo Code Cloud. Prosím přihlas se pro pokračování.", "organization_mismatch": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t autentizován s účtem Kilo Code Cloud tvé organizace.", "verification_failed": "Nelze ověřit autentizaci organizace."}}, "prompts": {"deleteMode": {"title": "Smazat vlastní <PERSON>", "description": "<PERSON><PERSON> si jist<PERSON>, ž<PERSON> ch<PERSON> smazat tento {{scope}} režim? Toto také smaže související složku pravidel na: {{rulesFolderPath}}", "descriptionNoRules": "<PERSON><PERSON> si ji<PERSON>, ž<PERSON> ch<PERSON><PERSON> smazat tento vlastní re<PERSON>?", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}}}