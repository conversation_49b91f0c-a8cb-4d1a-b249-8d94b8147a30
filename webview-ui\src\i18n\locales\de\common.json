{"answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "Abbrechen", "remove": "Entfernen", "keep": "Behalten"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "Wir würden gerne Ihr Feedback hören oder Ihnen bei Problemen helfen, die Si<PERSON> erleben.", "githubIssues": "Ein Problem auf GitHub melden", "githubDiscussions": "An GitHub-Diskussionen teilnehmen", "discord": "Treten Sie unserer Discord-Community bei", "customerSupport": "Kundensupport"}, "ui": {"search_placeholder": "Suchen..."}, "mermaid": {"loading": "Mermaid-Diagramm wird generiert...", "render_error": "Diagramm kann nicht gerendert werden", "fixing_syntax": "Mermaid-Syntax wird korrigiert...", "fix_syntax_button": "Syntax mit KI korrigieren", "original_code": "Ursprünglicher Code:", "errors": {"unknown_syntax": "Unbekannter Syntaxfehler", "fix_timeout": "KI-Korrekturanfrage hat das Zeitlimit überschritten", "fix_failed": "KI-Korrektur fehlgeschlagen", "fix_attempts": "Korrektur nach {{attempts}} Versuchen fehlgeschlagen. Letz<PERSON>hler: {{error}}", "no_fix_provided": "KI konnte keine Korrektur bereitstellen", "fix_request_failed": "Korrekturanfrage fehlgeschlagen"}, "buttons": {"zoom": "Zoom", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern", "copy": "<PERSON><PERSON><PERSON>", "save": "Bild speichern", "viewCode": "Code anzeigen", "viewDiagram": "Diagramm anzeigen", "close": "Schließen"}, "modal": {"codeTitle": "Mermaid-Code"}, "tabs": {"diagram": "Diagramm", "code": "Code"}, "feedback": {"imageCopied": "Bild in die Zwischenablage kopiert", "copyError": "Fehler beim Kopieren des Bildes"}}, "file": {"errors": {"invalidDataUri": "Ungültiges Daten-URI-Format", "copyingImage": "<PERSON><PERSON> beim Ko<PERSON>ren des Bildes: {{error}}", "openingImage": "<PERSON><PERSON> beim Ö<PERSON>nen des Bildes: {{error}}", "pathNotExists": "Pfad existiert nicht: {{path}}", "couldNotOpen": "<PERSON>i konnte nicht geöffnet werden: {{error}}", "couldNotOpenGeneric": "Datei konnte nicht geöffnet werden!"}, "success": {"imageDataUriCopied": "Bild-Daten-URI in die Zwischenablage kopiert"}}}