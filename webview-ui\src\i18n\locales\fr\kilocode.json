{"welcome": {"greeting": "Bienvenue dans Kilo Code !", "introText1": "Kilo Code est un agent de codage IA gratuit et open source.", "introText2": "Il fonctionne avec les derniers modèles d'IA comme Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, et plus de 450 autres.", "introText3": "Créez un compte gratuit et obtenez 20 $ en tokens à utiliser avec n'importe quel modèle d'IA.", "ctaButton": "<PERSON><PERSON><PERSON> un compte gratuit", "manualModeButton": "Utilisez votre propre clé API", "alreadySignedUp": "Déjà inscrit ?", "loginText": "Connectez-vous ici"}, "lowCreditWarning": {"addCredit": "A<PERSON>ter du crédit", "lowBalance": "Votre solde Kilo Code est bas"}, "notifications": {"toolRequest": "Demande d'outil en attente d'approbation", "browserAction": "Action du navigateur en attente d'approbation", "command": "Commande en attente d'approbation"}, "settings": {"sections": {"mcp": "Serveurs MCP"}, "provider": {"account": "Compte <PERSON>", "apiKey": "Clé API Kilo Code", "login": "Se connecter à Kilo Code", "logout": "Se déconnecter de Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Autoriser les lectures de fichiers très volumineux", "description": "Lorsque cette option est activée, Kilo Code effectuera des lectures de fichiers ou de sorties MCP très volumineuses même s'il existe une forte probabilité de dépasser la fenêtre de contexte (taille du contenu >80% de la fenêtre de contexte)."}}, "systemNotifications": {"label": "Activer les notifications système", "description": "Lorsque cette option est activée, Kilo Code enverra des notifications système pour les événements importants comme la fin des tâches ou les erreurs.", "testButton": "Tester la notification", "testTitle": "Kilo Code", "testMessage": "Ceci est une notification de test de Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code souhaite condenser votre conversation", "condenseConversation": "Condenser la Conversation"}}, "newTaskPreview": {"task": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "dashboard": "Tableau de bord", "logOut": "Se déconnecter", "currentBalance": "SOLDE ACTUEL", "loading": "Chargement..."}, "docs": "Documentation", "rules": {"tooltip": "Gérer les Règles et Flux de Travail Kilo Code", "ariaLabel": "<PERSON><PERSON><PERSON>", "tabs": {"rules": "<PERSON><PERSON><PERSON>", "workflows": "Flux de Travail"}, "description": {"rules": "Les règles vous permettent de fournir à Kilo Code des instructions qu'il doit suivre dans tous les modes et pour tous les prompts. Elles sont un moyen persistant d'inclure du contexte et des préférences pour toutes les conversations dans votre espace de travail ou globalement.", "workflows": "Les flux de travail sont un modèle préparé pour une conversation. Les flux de travail vous permettent de définir des prompts que vous utilisez fréquemment, et peuvent inclure une série d'étapes pour guider Kilo Code à travers des tâches répétitives, comme déployer un service ou soumettre une PR. Pour invoquer un flux de travail, tapez", "workflowsInChat": "dans le chat."}, "sections": {"globalRules": "R<PERSON>gles Globales", "workspaceRules": "Règles de l'Espace de Travail", "globalWorkflows": "Flux de Travail Globaux", "workspaceWorkflows": "Flux de Travail de l'Espace de Travail"}, "validation": {"invalidFileExtension": "Seules les extensions .md, .txt, ou aucune extension sont autorisées"}, "placeholders": {"workflowName": "nom-flux-travail (.md, .txt, ou sans extension)", "ruleName": "nom-règle (.md, .txt, ou sans extension)"}, "newFile": {"newWorkflowFile": "Nouveau fichier de flux de travail...", "newRuleFile": "Nouveau fichier de règle..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Voir {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_result": "résultat de l'action du navigateur", "browser_action_launch": "lancement du navigateur", "browser_action": "action du navigateur", "completion_result": "résultat de complétion", "command_output": "sortie de commande", "checkpoint_saved": "point de sauvegarde enregistré", "condense_context": "condenser le contexte", "command": "exécution de commande", "error": "message d'erreur", "followup": "question complémentaire", "mcp_server_response": "Réponse du serveur MCP", "text": "Réponse de l'IA", "unknown": "type de message inconnu", "reasoning": "Raisonnement de l'IA", "use_mcp_server": "Utilisation du serveur MCP", "tool": "utilisation d'outils", "user": "commentaires des utilisateurs"}}}, "userFeedback": {"editCancel": "Annuler", "send": "Envoyer", "restoreAndSend": "Restaurer et Envoyer"}, "ideaSuggestionsBox": {"newHere": "Nouveau ici ?", "suggestionText": "<suggestionButton><PERSON><PERSON><PERSON> ici</suggestionButton> pour une idée géniale, puis appuyez sur <sendIcon /> et regardez-moi faire de la magie !", "ideas": {"idea1": "Créez une sphère 3D rotative et brillante qui répond aux mouvements de la souris. Faites fonctionner l'application dans le navigateur.", "idea2": "Créez un site web de portfolio pour un développeur logiciel Python", "idea3": "Créez une maquette d'application financière dans le navigateur. Puis testez si elle fonctionne", "idea4": "Créez un site web répertoire contenant les meilleurs modèles de génération vidéo IA actuellement", "idea5": "Créez un générateur de dégradés CSS qui exporte des feuilles de style personnalisées et affiche un aperçu en direct", "idea6": "Générez des cartes qui révèlent magnifiquement du contenu dynamique au survol", "idea7": "C<PERSON>ez un champ d'étoiles interactif apaisant qui se déplace avec les gestes de la souris"}}}