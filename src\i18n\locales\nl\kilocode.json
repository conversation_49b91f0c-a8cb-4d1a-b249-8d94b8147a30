{"info": {"settings_imported": "Instellingen succesvol geïmporteerd."}, "userFeedback": {"message_update_failed": "Bijwerken van bericht mislukt", "no_checkpoint_found": "Geen checkpoint gevonden voor dit bericht", "message_updated": "Bericht succesvol bijgewerkt"}, "lowCreditWarning": {"title": "Waarschuwing: laag krediet!", "message": "Controleer of je kunt opwa<PERSON>eren met gratis credits of meer kunt kopen!"}, "notLoggedInError": "<PERSON>n verzoek niet vol<PERSON>, zorg ervoor dat je verbonden bent en ingelogd met de geselecteerde provider.\n\n{{error}}", "rules": {"actions": {"delete": "Verwijderen", "confirmDelete": "Weet je zeker dat je {{filename}} wilt verwijderen?", "deleted": "{{filename}} verwijderd"}, "errors": {"noWorkspaceFound": "Geen werkruimte map gevonden", "fileAlreadyExists": "Bestand {{filename}} bestaat al", "failedToCreateRuleFile": "<PERSON>n regelbestand niet aan<PERSON>ken.", "failedToDeleteRuleFile": "Kon regelbestand niet verwijderen."}, "templates": {"workflow": {"description": "Workflow beschrijving hier...", "stepsHeader": "## <PERSON><PERSON><PERSON>", "step1": "Stap 1", "step2": "Stap 2"}, "rule": {"description": "<PERSON><PERSON> beschrijving hier...", "guidelinesHeader": "## <PERSON><PERSON><PERSON><PERSON><PERSON>", "guideline1": "Richtlijn 1", "guideline2": "Richtlijn 2"}}}, "commitMessage": {"activated": "Kilo Code commit bericht generator geactiveerd", "gitNotFound": "⚠️ Git repository niet gevonden of git niet besch<PERSON>ar", "gitInitError": "⚠️ Git-initialisatiefout: {{error}}", "generating": "Kilo: Commitbericht genereren...", "noChanges": "Kilo: <PERSON>n wijzigingen gevonden om te analyseren", "generated": "Kilo: Commitbericht gegenereerd!", "generationFailed": "Kilo: Mi<PERSON><PERSON>t bij het genereren van een commit-bericht: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON><PERSON><PERSON> genereren met niet-gecommitteerde wijzigingen", "activationFailed": "Kilo: Niet gelukt berichtengenerator te activeren: {{error}}", "providerRegistered": "Kilo: Berichtprovider voor commits geregistreerd"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Automatisch aanvullen", "warning": "$(warning) Kilo Automatisch aanvullen", "tooltip": {"basic": "Kilo Code Automatisch aanvullen", "tokenError": "Een geldige token moet ingesteld worden om automatisch aanvullen te gebruiken", "disabled": "Kilo Code Automatisch aanvullen (uitgeschakeld)", "lastCompletion": "Laatste voltooiing:", "sessionTotal": "Totale sessiekosten:", "model": "Model:"}, "disabled": "$(circle-slash) Kilo Automatisch aanvullen", "cost": {"zero": "€ 0,00", "lessThanCent": "<€0,01"}}, "toggleMessage": "Kilo Automatisch aanvullen {{status}}"}, "ghost": {"progress": {"analyzing": "Je code wordt geanalyseerd...", "generating": "Suggesties voor bewerkingen genereren...", "showing": "Suggesties voor bewerkingen worden weergegeven...", "processing": "Suggesties voor bewerkingen verwerken...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON><PERSON>", "placeholder": "bijv. 'herstructureer deze functie om efficiënter te zijn'"}, "commands": {"displaySuggestions": "Toon voorgestelde aanpassingen", "generateSuggestions": "Kilo Code: <PERSON><PERSON>gestelde Aanpassingen", "cancelSuggestions": "Voorgestelde bewerkingen annuleren", "category": "Kilo Code", "applyAllSuggestions": "Pas alle voorgestelde wijzigingen toe", "applyCurrentSuggestion": "Huidige Voorgestelde Bewerking Toepassen", "promptCodeSuggestion": "Prompt<PERSON><PERSON>"}, "codeAction": {"title": "Kilo Code: Voorgestelde Bewerkingen"}, "chatParticipant": {"name": "Agent", "fullName": "Kilo Code Agent", "description": "<PERSON><PERSON> kan je helpen met snelle taken en voorgestelde bewerkingen."}, "messages": {"provideCodeSuggestions": "Code suggesties worden aangeboden..."}}}