{"info": {"settings_imported": "Nastavení byla úspěšně importována."}, "userFeedback": {"message_update_failed": "Aktualizace zprávy selhala", "no_checkpoint_found": "<PERSON><PERSON><PERSON> to<PERSON> zprávou nebyl nalezen žádný checkpoint", "message_updated": "Zpráva byla úspěšně aktualizována"}, "lowCreditWarning": {"title": "Varování o nízkém kreditu!", "message": "Z<PERSON><PERSON>luj, zda můžeš doplnit bezplatné kredity nebo si koupit dalš<PERSON>!"}, "notLoggedInError": "Požadavek nelze dokončit, ujisti se, že jsi připojen a přihlášen u vybraného poskytovatele.\n\n{{error}}", "rules": {"actions": {"delete": "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON> si ji<PERSON>, že ch<PERSON> smazat {{filename}}?", "deleted": "Smazáno {{filename}}"}, "errors": {"noWorkspaceFound": "Nebyla nalezena složka pracovního prostoru", "fileAlreadyExists": "Soubor {{filename}} již <PERSON>uje", "failedToCreateRuleFile": "Vytvoření souboru pravidla selhalo.", "failedToDeleteRuleFile": "Smazání souboru pravidla selhalo."}, "templates": {"workflow": {"description": "Popis pracovního postupu zde...", "stepsHeader": "## <PERSON><PERSON><PERSON>", "step1": "Krok 1", "step2": "Krok 2"}, "rule": {"description": "Popis pravidla zde...", "guidelinesHeader": "## <PERSON><PERSON><PERSON>", "guideline1": "Pokyn 1", "guideline2": "Pokyn 2"}}}, "commitMessage": {"activated": "<PERSON><PERSON><PERSON><PERSON> zpráv pro commit Kilo Code aktivován", "gitNotFound": "⚠️ Git repozit<PERSON><PERSON> nebyl nalezen nebo git nen<PERSON> dos<PERSON>", "gitInitError": "⚠️ Chyba inicializace Gitu: {{error}}", "generating": "Kilo: Gene<PERSON><PERSON>...", "noChanges": "Kilo: Nenalezeny žádné změny k analýze", "generated": "Kilo: Zpráva byla vygenerována!", "generationFailed": "Kilo: Nepodařilo se vygenerovat zprávu commitu: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Generování zprávy s použitím nenapojených změn", "activationFailed": "Kilo: Aktivace generátoru zpráv se nezdařila: {{error}}", "providerRegistered": "Kilo: Poskytovatel zpráv commitů zaregistrován"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) <PERSON><PERSON><PERSON>í", "disabled": "$(circle-slash) <PERSON><PERSON><PERSON>", "tooltip": {"tokenError": "Pro použití automatického dokončování musí být nastaven platný token", "basic": "<PERSON><PERSON>", "lastCompletion": "Poslední dokončení:", "model": "Model:", "sessionTotal": "Celkové náklady na relaci:", "disabled": "<PERSON><PERSON> (deaktivováno)"}, "warning": "$(warning) <PERSON><PERSON><PERSON>í", "cost": {"zero": "0,00 Kč", "lessThanCent": "<0,01 $"}}, "toggleMessage": "<PERSON><PERSON> {{status}}"}, "ghost": {"progress": {"generating": "Generu<PERSON>...", "analyzing": "Analyzuji váš kód...", "processing": "Zpracování navržených úprav...", "showing": "Zobrazuji navr<PERSON>...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>. \"př<PERSON><PERSON><PERSON><PERSON> tuto <PERSON>, aby byla efektivnější\""}, "commands": {"generateSuggestions": "Kilo Code: Generov<PERSON><PERSON> navrhovaných úprav", "applyCurrentSuggestion": "Použít aktuální navrhovanou úpravu", "displaySuggestions": "Zobrazit navr<PERSON><PERSON>", "cancelSuggestions": "<PERSON>ruš<PERSON> nav<PERSON><PERSON><PERSON>", "applyAllSuggestions": "Použít všechny navrhované úpravy", "promptCodeSuggestion": "Rychlý úkol", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chatParticipant": {"name": "Agent", "fullName": "Kilo Code Agent", "description": "Mohu vám pomoci s rychlými úkoly a navrženými úpravami."}, "messages": {"provideCodeSuggestions": "Poskytuji návrhy kódu..."}}}