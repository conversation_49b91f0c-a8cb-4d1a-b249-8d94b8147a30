{"name": "@roo-code/storybook", "private": true, "type": "module", "scripts": {"generate-theme-styles": "node scripts/generate-vscode-themes.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build --webpack-stats-json", "preview": "storybook preview", "chromatic": "chromatic --exit-zero-on-changes --storybook-base-dir=apps/storybook", "clean": "rimraf storybook-static .turbo"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.62.7", "@vscode/codicons": "^0.0.36", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^18.3.1", "react-dom": "^18.3.1", "seedrandom": "^3.0.5", "shiki": "^3.2.1", "styled-components": "^6.1.13", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.0.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/test-runner": "^0.21.2", "@tailwindcss/vite": "^4.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.5", "@types/seedrandom": "^3.0.8", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.4", "chromatic": "^13.0.0", "jsonc-parser": "^3.3.1", "node-fetch": "2", "rimraf": "^6.0.1", "storybook": "^8.4.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "vite": "6.3.5"}}