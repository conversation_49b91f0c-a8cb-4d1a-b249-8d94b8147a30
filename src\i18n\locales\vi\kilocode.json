{"info": {"settings_imported": "<PERSON>ài đặt đã được nhập thành công."}, "userFeedback": {"message_update_failed": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật tin nhắn", "no_checkpoint_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy điểm kiểm tra trước tin nhắn này", "message_updated": "<PERSON> nhắn đã đ<PERSON><PERSON><PERSON> cập nhật"}, "lowCreditWarning": {"title": "Cảnh Báo Tín Dụng Thấp!", "message": "Kiểm tra xem bạn có thể nạp thêm tín dụng miễn phí hoặc mua thêm không!"}, "notLoggedInError": "<PERSON><PERSON><PERSON><PERSON> thể hoàn thành yêu cầu, h<PERSON><PERSON> đảm bảo bạn đã kết nối và đăng nhập với nhà cung cấp đã chọn.\n\n{{error}}", "rules": {"actions": {"delete": "Xóa", "confirmDelete": "Bạn có chắc chắn muốn xóa {{filename}} không?", "deleted": "<PERSON><PERSON> xóa {{filename}}"}, "errors": {"noWorkspaceFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục workspace", "fileAlreadyExists": "Tệp {{filename}} đã tồn tại", "failedToCreateRuleFile": "<PERSON><PERSON><PERSON><PERSON> thể tạo tệp quy tắc.", "failedToDeleteRuleFile": "<PERSON><PERSON><PERSON><PERSON> thể xóa tệp quy tắc."}, "templates": {"workflow": {"description": "<PERSON><PERSON> tả quy trình làm việc ở đây...", "stepsHeader": "## <PERSON><PERSON><PERSON>", "step1": "Bước 1", "step2": "Bước 2"}, "rule": {"description": "<PERSON><PERSON> tả quy tắc ở đây...", "guidelinesHeader": "## Hướng dẫn", "guideline1": "Hướng dẫn 1", "guideline2": "Hướng dẫn 2"}}}, "commitMessage": {"activated": "<PERSON><PERSON><PERSON><PERSON> tạo thông điệp commit Kilo Code đã đư<PERSON><PERSON> kích ho<PERSON>t", "gitNotFound": "⚠️ Không tìm thấy kho lưu trữ Git hoặc git không khả dụng", "gitInitError": "⚠️ Lỗi khởi tạo Git: {{error}}", "generating": "Kilo: <PERSON><PERSON> tạo tin nh<PERSON>n commit...", "noChanges": "Kilo: <PERSON><PERSON><PERSON><PERSON> tìm thấy thay đổi để phân tích", "generated": "<PERSON><PERSON>: <PERSON><PERSON> tạo thông điệp commit!", "generationFailed": "Kilo: <PERSON><PERSON><PERSON>ng thể tạo thông điệp commit: {{errorMessage}}", "generatingFromUnstaged": "Kilo: <PERSON><PERSON><PERSON> tin nhắn sử dụng các thay đổi chưa được đưa vào dàn dựng", "activationFailed": "Kilo: Kh<PERSON>ng thể kích hoạt trình tạo tin nhắn: {{error}}", "providerRegistered": "Kilo: <PERSON><PERSON> đăng ký nhà cung cấp thông điệp commit"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Tự động hoàn thành", "disabled": "$(circle-slash) Kilo Tự động hoàn thành", "warning": "$(warning) Kilo Tự động hoàn thành", "tooltip": {"disabled": "Tự động hoàn thành <PERSON> (đã tắt)", "basic": "Tự động hoàn thiện Kilo Code", "tokenError": "<PERSON><PERSON><PERSON> thiết lập một token hợp lệ để sử dụng tính năng tự động hoàn thành", "lastCompletion": "<PERSON><PERSON><PERSON><PERSON> hoàn thành cuối cùng:", "model": "<PERSON><PERSON> h<PERSON>nh:", "sessionTotal": "Tổng chi phí phiên:"}, "cost": {"zero": "0₫", "lessThanCent": "<$0,01"}}, "toggleMessage": "<PERSON><PERSON> động hoàn thành {{status}}"}, "ghost": {"progress": {"generating": "<PERSON><PERSON> tạo đề xuất chỉnh sửa...", "processing": "<PERSON><PERSON> xử lý các đề xuất chỉnh sửa...", "analyzing": "Đang phân tích mã của bạn...", "showing": "<PERSON><PERSON> hiển thị các sửa đổi được đề xuất...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON><PERSON><PERSON>", "placeholder": "ví d<PERSON>, 'c<PERSON>u trúc lại hàm này để hiệu quả hơn'"}, "commands": {"generateSuggestions": "Kilo Code: <PERSON><PERSON><PERSON> Ý Chỉnh Sửa", "displaySuggestions": "Hiển thị đề xuất chỉnh sửa", "cancelSuggestions": "<PERSON><PERSON><PERSON> <PERSON><PERSON>ất Chỉnh Sửa", "applyCurrentSuggestion": "<PERSON><PERSON> dụng chỉnh sửa đề xuất hiện tại", "promptCodeSuggestion": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> từ <PERSON> n<PERSON>c", "applyAllSuggestions": "<PERSON><PERSON> dụng <PERSON>ất cả Chỉnh sửa Được Đ<PERSON> xuất", "category": "Kilo Code"}, "codeAction": {"title": "Kilo Code: <PERSON><PERSON><PERSON> ý chỉnh sửa"}, "chatParticipant": {"description": "<PERSON><PERSON><PERSON> có thể gi<PERSON>p bạn với các tác vụ nhanh và các chỉnh sửa được đề xuất.", "fullName": "<PERSON><PERSON> Đại lý", "name": "<PERSON><PERSON><PERSON> lý"}, "messages": {"provideCodeSuggestions": "<PERSON><PERSON> cung cấp gợi ý mã..."}}}