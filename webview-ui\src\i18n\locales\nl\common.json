{"answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Verwijderen", "keep": "Behouden"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "mrd"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We horen graag uw feedback of helpen u graag met eventuele problemen die u ondervindt.", "githubIssues": "Meld een probleem op GitHub", "githubDiscussions": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>-discussies", "discord": "Word lid van onze Discord-community", "customerSupport": "Klantenservice"}, "ui": {"search_placeholder": "Zoeken..."}, "mermaid": {"loading": "Mermaid-diagram genereren...", "render_error": "Kan diagram niet weergeven", "fixing_syntax": "Mermaid-syntax corrigeren...", "fix_syntax_button": "Syntax corriger<PERSON> met AI", "original_code": "Originele code:", "errors": {"unknown_syntax": "Onbekende syntaxfout", "fix_timeout": "LLM-correctieverzoek is verlopen", "fix_failed": "LLM-<PERSON><PERSON> mis<PERSON>t", "fix_attempts": "Syntax kon niet worden gecorrigeerd na {{attempts}} pogingen. Laatste fout: {{error}}", "no_fix_provided": "LLM kon geen correctie leveren", "fix_request_failed": "Correctieverzoek mislukt"}, "buttons": {"zoom": "Zoom", "zoomIn": "Inzoomen", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "save": "Afbeelding opslaan", "viewCode": "Code bekijken", "viewDiagram": "Diagram be<PERSON>jken", "close": "Sluiten"}, "modal": {"codeTitle": "Mermaid-code"}, "tabs": {"diagram": "Diagram", "code": "Code"}, "feedback": {"imageCopied": "Afbeelding gekopieerd naar klembord", "copyError": "Fout bij kopi<PERSON><PERSON> van a<PERSON>"}}, "file": {"errors": {"invalidDataUri": "Ongeldig data-URI-formaat", "copyingImage": "Fout bij kop<PERSON><PERSON><PERSON>: {{error}}", "openingImage": "Fout bij openen van afbeelding: {{error}}", "pathNotExists": "Pad bestaat niet: {{path}}", "couldNotOpen": "Kon bestand niet openen: {{error}}", "couldNotOpenGeneric": "Kon bestand niet openen!"}, "success": {"imageDataUriCopied": "Afbeelding data-URI gekopieerd naar klembord"}}}