{"extension.displayName": "Kilo Code AI Agent (รวมฟีเจอร์ Cline / Roo)", "extension.description": "ผู้ช่วยเขียนโค้ด AI แบบ Open Source สำหรับการวางแผน สร้าง และแก้ไขโค้ด", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "งานใหม่", "command.mcpServers.title": "เซิร์ฟเวอร์ MCP", "command.prompts.title": "โหมด", "command.history.title": "ประวัติ", "command.marketplace.title": "ตลาด", "command.openInEditor.title": "เปิดในตัวแก้ไข", "command.settings.title": "การตั้งค่า", "command.documentation.title": "เอกสาร", "command.openInNewTab.title": "เปิดในแท็บใหม่", "command.explainCode.title": "อธิบายโค้ด", "command.fixCode.title": "แก้ไขโค้ด", "command.improveCode.title": "ปรับปรุงโค้ด", "command.addToContext.title": "เพิ่มไปยังบริบท", "command.focusInput.title": "โฟกัสช่องป้อนข้อมูล", "command.setCustomStoragePath.title": "ตั้งค่าเส้นทางจัดเก็บแบบกำหนดเอง", "command.terminal.addToContext.title": "เพิ่มเนื้อหาเทอร์มินัลไปยังบริบท", "command.terminal.fixCommand.title": "แก้ไขคำสั่งนี้", "command.terminal.explainCommand.title": "อธิบายคำสั่งนี้", "command.acceptInput.title": "ยอมรับอินพุต/คำแนะนำ", "command.generateCommitMessage.title": "สร้างข้อความ Commit ด้วย Kilo", "command.profile.title": "โปรไฟล์", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "คำสั่งที่สามารถดำเนินการอัตโนมัติเมื่อเปิดใช้งาน 'Always approve execute operations'", "settings.vsCodeLmModelSelector.description": "การตั้งค่าสำหรับ VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "ผู้ให้บริการของโมเดลภาษา (เช่น copilot)", "settings.vsCodeLmModelSelector.family.description": "ตระกูลของโมเดลภาษา (เช่น gpt-4)", "settings.customStoragePath.description": "เส้นทางจัดเก็บแบบกำหนดเอง เว้นว่างไว้เพื่อใช้ตำแหน่งเริ่มต้น รองรับเส้นทางแบบสัมบูรณ์ (เช่น 'D:\\KiloCodeStorage')", "command.importSettings.title": "นำเข้าการตั้งค่า", "settings.enableCodeActions.description": "เปิดใช้งานการแก้ไขด่วนของ Kilo Code", "settings.autoImportSettingsPath.description": "เส้นทางไปยังไฟล์กำหนดค่า Kilo Code ที่จะนำเข้าโดยอัตโนมัติเมื่อเริ่มต้นส่วนขยาย รองรับเส้นทางแบบสัมบูรณ์และเส้นทางที่สัมพันธ์กับไดเรกทอรีหลัก (เช่น '~/Documents/kilo-code-settings.json') เว้นว่างไว้เพื่อปิดใช้งานการนำเข้าอัตโนมัติ", "ghost.input.title": "กด 'Enter' เพื่อยืนยันหรือ 'Escape' เพื่อยกเลิก", "ghost.input.placeholder": "อธิบายสิ่งที่คุณต้องการทำ...", "ghost.commands.generateSuggestions": "Kilo Code: สร้างข้อเสนอแนะการแก้ไข", "ghost.commands.displaySuggestions": "แสดงข้อเสนอแนะการแก้ไข", "ghost.commands.cancelSuggestions": "ยกเลิกข้อเสนอแนะการแก้ไข", "ghost.commands.applyCurrentSuggestion": "ใช้ข้อเสนอแนะการแก้ไขปัจจุบัน", "ghost.commands.applyAllSuggestions": "ใช้ข้อเสนอแนะการแก้ไขทั้งหมด", "ghost.commands.promptCodeSuggestion": "งานด่วน", "ghost.commands.goToNextSuggestion": "ไปยังข้อเสนอแนะถัดไป", "ghost.commands.goToPreviousSuggestion": "ไปยังข้อเสนอแนะก่อนหน้า"}