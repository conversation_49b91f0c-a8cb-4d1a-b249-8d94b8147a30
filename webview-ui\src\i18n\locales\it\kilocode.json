{"welcome": {"greeting": "Benvenuto in Kilo Code!", "introText1": "Kilo Code è un agente di coding AI gratuito e open source.", "introText2": "Funziona con i più recenti modelli AI come Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, e oltre 450 altri.", "introText3": "Crea un account gratuito e ottieni $20 di token da utilizzare con qualsiasi modello AI.", "ctaButton": "Crea account gratuito", "manualModeButton": "Usa la tua chiave API", "alreadySignedUp": "Già registrato?", "loginText": "Accedi qui"}, "lowCreditWarning": {"addCredit": "Aggiungi credito", "lowBalance": "Il tuo saldo <PERSON> è basso"}, "notifications": {"toolRequest": "Richiesta strumento in attesa di approvazione", "browserAction": "Azione del browser in attesa di approvazione", "command": "Comando in attesa di approvazione"}, "settings": {"sections": {"mcp": "Server MCP"}, "provider": {"account": "Account <PERSON><PERSON>", "apiKey": "Chiave API Kilo Code", "login": "Accedi a Kilo Code", "logout": "<PERSON>nnetti da Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Consenti letture di file molto grandi", "description": "<PERSON><PERSON><PERSON>, <PERSON>lo Code eseguirà letture di file o output MCP molto grandi anche se c'è un'alta probabilità di superare la finestra di contesto (dimensione del contenuto >80% della finestra di contesto)."}}, "systemNotifications": {"label": "Attiva notifiche di sistema", "description": "<PERSON><PERSON><PERSON> a<PERSON>, Kilo Code invierà notifiche di sistema per eventi importanti come il completamento di attività o errori.", "testButton": "Testa notifica", "testTitle": "Kilo Code", "testMessage": "Questa è una notifica di test da Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code vuole condensare la tua conversazione", "condenseConversation": "Condensa Conversazione"}}, "newTaskPreview": {"task": "Attività"}, "profile": {"title": "<PERSON>ilo", "dashboard": "Dashboard", "logOut": "<PERSON><PERSON><PERSON>", "currentBalance": "SALDO ATTUALE", "loading": "Caricamento..."}, "docs": "Documentazione", "rules": {"tooltip": "Gestisci Regole e Flussi di Lavoro di Kilo Code", "ariaLabel": "Regole di Kilo Code", "tabs": {"rules": "Regole", "workflows": "Flussi di Lavoro"}, "description": {"rules": "Le regole ti permettono di fornire a Kilo Code istruzioni che deve seguire in tutte le modalità e per tutti i prompt. Sono un modo persistente per includere contesto e preferenze per tutte le conversazioni nel tuo workspace o globalmente.", "workflows": "I flussi di lavoro sono un template preparato per una conversazione. I flussi di lavoro ti permettono di definire prompt che usi frequentemente, e possono includere una serie di passaggi per guidare Kilo Code attraverso attività ripetitive, come distribuire un servizio o inviare una PR. Per invocare un flusso di lavoro, digita", "workflowsInChat": "nella chat."}, "sections": {"globalRules": "Regole Globali", "workspaceRules": "Regole dell'Area di Lavoro", "globalWorkflows": "Flussi di Lavoro Globali", "workspaceWorkflows": "Flussi di Lavoro dell'Area di Lavoro"}, "validation": {"invalidFileExtension": "Sono consentite solo estensioni .md, .txt o nessuna estensione"}, "placeholders": {"workflowName": "nome-flusso-lavoro (.md, .txt o nessuna estensione)", "ruleName": "nome-regola (.md, .txt o nessuna estensione)"}, "newFile": {"newWorkflowFile": "Nuovo file flusso di lavoro...", "newRuleFile": "Nuovo file regola..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "avvio del browser", "browser_action": "azione del browser", "browser_action_result": "risultato dell'azione del browser", "checkpoint_saved": "checkpoint salvato", "command_output": "output del comando", "condense_context": "contesto condensato", "error": "messaggio di errore", "command": "esecuzione di comandi", "completion_result": "risultato di completamento", "followup": "domanda di approfondimento", "reasoning": "Ragionamento dell'IA", "mcp_server_response": "Risposta del server MCP", "text": "Risposta dell'IA", "unknown": "tipo di messaggio sconos<PERSON>uto", "use_mcp_server": "Utilizzo del server MCP", "tool": "util<PERSON><PERSON> strumenti", "user": "feedback dell'utente"}, "clickToScroll": "Visualizza {{messageType}} (#{{messageNumber}})"}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "Invia", "restoreAndSend": "Ripristina e Invia"}, "ideaSuggestionsBox": {"newHere": "Nuovo qui?", "suggestionText": "<suggestionButton><PERSON><PERSON><PERSON> qui</suggestionButton> per un'idea fantastica, poi tocca <sendIcon /> e guardami fare magie!", "ideas": {"idea1": "Crea una sfera 3D rotante e luminosa che risponde ai movimenti del mouse. Fai funzionare l'app nel browser.", "idea2": "Crea un sito web portfolio per uno sviluppatore software Python", "idea3": "Crea un mockup di app finanziaria nel browser. Poi testa se funziona", "idea4": "Crea un sito web directory contenente i migliori modelli di generazione video AI attualmente", "idea5": "Crea un generatore di gradienti CSS che esporta fogli di stile personalizzati e mostra un'anteprima live", "idea6": "Genera carte che rivelano contenuti dinamici magnificamente quando ci si passa sopra", "idea7": "Crea un campo stellare interattivo rilassante che si muove con i gesti del mouse"}}}