{"extension.displayName": "Agen AI Kilo Code (fitur Cline / Roo digabungkan)", "extension.description": "Asisten coding AI Open Source untuk mere<PERSON>, me<PERSON><PERSON><PERSON>, dan me<PERSON><PERSON><PERSON> kode.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "<PERSON><PERSON>", "command.mcpServers.title": "Server MCP", "command.prompts.title": "Mode", "command.history.title": "Riwayat", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "<PERSON><PERSON> di Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Dokumentasi", "command.openInNewTab.title": "<PERSON><PERSON> <PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON>", "command.fixCode.title": "Perbaiki Kode", "command.improveCode.title": "Tingkatkan Kode", "command.addToContext.title": "Tambahkan ke Konteks", "command.focusInput.title": "Fokus ke Field Input", "command.setCustomStoragePath.title": "Atur Path Penyimpanan Kustom", "command.importSettings.title": "<PERSON><PERSON><PERSON>", "command.terminal.addToContext.title": "Tambahkan Konten Terminal ke Konteks", "command.terminal.fixCommand.title": "Perbaiki Perintah Ini", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON>", "command.acceptInput.title": "Terima Input/Saran", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> yang dapat dijalankan secara otomatis ketika 'Selalu setujui operasi eksekusi' diak<PERSON><PERSON>kan", "settings.vsCodeLmModelSelector.description": "Pengaturan untuk API Model Bahasa VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Vendor dari model bahasa (misalnya copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON><PERSON><PERSON> dari model bahasa (misalnya gpt-4)", "settings.customStoragePath.description": "Path penyimpanan kustom. Biarkan kosong untuk menggunakan lokasi default. Mendukung path absolut (misalnya 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Aktifkan perbaikan cepat Kilo Code.", "settings.autoImportSettingsPath.description": "Path ke file konfigurasi Kilo Code untuk diimpor secara otomatis saat ekstensi dimulai. Mendukung path absolut dan path relatif terhadap direktori home (misalnya '~/Documents/kilo-code-settings.json'). Biarkan kosong untuk menonaktifkan impor otomatis.", "command.generateCommitMessage.title": "Buat Pesan Commit dengan <PERSON>", "command.profile.title": "Profil", "ghost.input.title": "<PERSON><PERSON> 'Enter' untuk konfirmasi atau 'Escape' untuk membatalkan", "ghost.input.placeholder": "<PERSON><PERSON>kan apa yang ingin Anda lakukan...", "ghost.commands.generateSuggestions": "Kilo Code: Buat Saran Pengeditan", "ghost.commands.displaySuggestions": "<PERSON><PERSON><PERSON><PERSON> Pengedita<PERSON>", "ghost.commands.cancelSuggestions": "Batalkan Saran Pengeditan", "ghost.commands.applyCurrentSuggestion": "Terapkan Saran Pengeditan Saat Ini", "ghost.commands.applyAllSuggestions": "Terapkan Se<PERSON> Pengeditan", "ghost.commands.promptCodeSuggestion": "Tugas Cepat", "ghost.commands.goToNextSuggestion": "Pergi ke Saran Berikutnya", "ghost.commands.goToPreviousSuggestion": "Pergi ke Saran Sebelumnya"}