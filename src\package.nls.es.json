{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Asistente de codificación de IA de código abierto para planificar, crear y corregir código.", "command.newTask.title": "Nueva Tarea", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "<PERSON><PERSON><PERSON> Contexto", "command.openInNewTab.title": "Abrir en Nueva Pestaña", "command.focusInput.title": "Enfocar Campo de Entrada", "command.setCustomStoragePath.title": "Establecer <PERSON> de Almacenamiento Personalizada", "command.importSettings.title": "Importar Configuración", "command.terminal.addToContext.title": "Añadir Contenido de Terminal al Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceptar Entrada/Sugerencia", "command.generateCommitMessage.title": "<PERSON><PERSON> mensaje de commit con <PERSON>", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Historial", "command.marketplace.title": "<PERSON><PERSON><PERSON>", "command.openInEditor.title": "Abrir en Editor", "command.settings.title": "Configuración", "command.documentation.title": "Documentación", "command.profile.title": "Perfil", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Comandos que pueden ejecutarse automáticamente cuando 'Aprobar siempre operaciones de ejecución' está activado", "settings.vsCodeLmModelSelector.description": "Configuración para la API del modelo de lenguaje VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveedor del modelo de lenguaje (ej. copilot)", "settings.vsCodeLmModelSelector.family.description": "La familia del modelo de lenguaje (ej. gpt-4)", "settings.customStoragePath.description": "Ruta de almacenamiento personalizada. Dejar vacío para usar la ubicación predeterminada. Admite rutas absolutas (ej. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Habilitar correcciones rápidas de Kilo Code.", "settings.autoImportSettingsPath.description": "Ruta a un archivo de configuración de Kilo Code para importar automáticamente al iniciar la extensión. Admite rutas absolutas y rutas relativas al directorio de inicio (por ejemplo, '~/Documents/kilo-code-settings.json'). Dejar vacío para desactivar la importación automática.", "ghost.input.title": "Presiona 'Enter' para confirmar o 'Escape' para cancelar", "ghost.input.placeholder": "Describe lo que quieres hacer...", "ghost.commands.generateSuggestions": "Kilo Code: Generar Ediciones Sugeridas", "ghost.commands.displaySuggestions": "Mostrar Ediciones Sugeridas", "ghost.commands.cancelSuggestions": "Cancelar Ediciones Sugeridas", "ghost.commands.applyCurrentSuggestion": "Aplicar Edición Sugerida Actual", "ghost.commands.applyAllSuggestions": "Aplicar Todas las Ediciones Sugeridas", "ghost.commands.promptCodeSuggestion": "<PERSON><PERSON>", "ghost.commands.goToNextSuggestion": "Ir a la Siguiente Sugerencia", "ghost.commands.goToPreviousSuggestion": "Ir a la Sugerencia Anterior"}