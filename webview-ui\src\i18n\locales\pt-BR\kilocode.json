{"welcome": {"greeting": "Bem-vindo ao Kilo Code!", "introText1": "Kilo Code é um agente de codificação AI gratuito e de código aberto.", "introText2": "Funciona com os mais recentes modelos de IA como Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, e mais de 450 outros.", "introText3": "Crie uma conta gratuita e receba $20 em tokens para usar com qualquer modelo de IA.", "ctaButton": "<PERSON>riar conta gratuita", "manualModeButton": "Use sua própria chave de API", "alreadySignedUp": "Já se cadastrou?", "loginText": "Faça login aqui"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON><PERSON>", "lowBalance": "Seu saldo do Kilo Code está baixo"}, "notifications": {"toolRequest": "Solicitação de ferramenta aguardando aprovação", "browserAction": "Ação do navegador aguardando aprovação", "command": "Comando aguardand<PERSON>"}, "settings": {"sections": {"mcp": "Servidores MCP"}, "provider": {"account": "Conta Kilo Code", "apiKey": "Chave de API do Kilo Code", "login": "Entrar no Kilo Code", "logout": "Sair do Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Per<PERSON><PERSON> le<PERSON> de arquivos muito grandes", "description": "<PERSON>uando ativado, o Kilo Code realizará leituras de arquivos ou saídas MCP muito grandes mesmo que haja uma alta probabilidade de exceder a janela de contexto (tamanho do conteúdo >80% da janela de contexto)."}}, "systemNotifications": {"label": "Ativar notificações do sistema", "description": "<PERSON>uando ativado, o Kilo Code enviará notificações do sistema para eventos importantes como conclusão de tarefas ou erros.", "testButton": "Testar notificação", "testTitle": "Kilo Code", "testMessage": "Esta é uma notificação de teste do Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code quer condensar sua conversa", "condenseConversation": "Condensar Conversa"}}, "newTaskPreview": {"task": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Perfil", "dashboard": "<PERSON><PERSON>", "logOut": "<PERSON><PERSON>", "currentBalance": "SALDO ATUAL", "loading": "Carregando..."}, "docs": "Documentação", "rules": {"tooltip": "Gerenciar Regras e Fluxos de Trabalho do Kilo Code", "ariaLabel": "Regras do Kilo Code", "tabs": {"rules": "Regras", "workflows": "Fluxos de Trabalho"}, "description": {"rules": "As regras permitem fornecer ao Kilo Code instruções que ele deve seguir em todos os modos e para todos os prompts. Elas são uma forma persistente de incluir contexto e preferências para todas as conversas em seu workspace ou globalmente.", "workflows": "Os fluxos de trabalho são um modelo preparado para uma conversa. Os fluxos de trabalho permitem definir prompts que você usa frequentemente, e podem incluir uma série de passos para guiar o Kilo Code através de tarefas repetitivas, como implantar um serviço ou enviar um PR. Para invocar um fluxo de trabalho, digite", "workflowsInChat": "no chat."}, "sections": {"globalRules": "Regras Globais", "workspaceRules": "Regras do Workspace", "globalWorkflows": "Fluxos de Trabalho Globais", "workspaceWorkflows": "Fluxos de Trabalho do Workspace"}, "validation": {"invalidFileExtension": "Apenas extensões .md, .txt ou sem extensão são permitidas"}, "placeholders": {"workflowName": "nome-fluxo-trabalho (.md, .txt ou sem extensão)", "ruleName": "nome-regra (.md, .txt ou sem extensão)"}, "newFile": {"newWorkflowFile": "Novo arquivo de fluxo de trabalho...", "newRuleFile": "Novo arquivo de regra..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Ver {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_result": "resultado da ação do navegador", "browser_action_launch": "iniciar <PERSON>", "browser_action": "ação do navegador", "command": "execução de comando", "checkpoint_saved": "ponto de verificação salvo", "command_output": "saída do comando", "completion_result": "resultado da conclusão", "followup": "pergunta de acompanhamento", "error": "mensagem de erro", "condense_context": "condensar contexto", "reasoning": "Raciocínio de IA", "mcp_server_response": "Resposta do servidor MCP", "text": "Resposta da IA", "tool": "uso de ferramentas", "unknown": "tipo de mensagem desconhecido", "use_mcp_server": "Uso do servidor MCP", "user": "feedback do usuário"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "Enviar", "restoreAndSend": "Restaurar e Enviar"}, "ideaSuggestionsBox": {"newHere": "Novo aqui?", "suggestionText": "<suggestionButton>Clique aqui</suggestionButton> para uma ideia legal, depois toque em <sendIcon /> e me veja fazer mágica!", "ideas": {"idea1": "Crie uma esfera 3D giratória e brilhante que responde aos movimentos do mouse. Faça o app funcionar no navegador.", "idea2": "Crie um site de portfólio para um desenvolvedor de software Python", "idea3": "Crie um mockup de aplicativo financeiro no navegador. Depois teste se funciona", "idea4": "Crie um site de diretório contendo os melhores modelos de geração de vídeo AI atualmente", "idea5": "Crie um gerador de gradientes CSS que exporta folhas de estilo personalizadas e mostra uma prévia ao vivo", "idea6": "Gere cards que revelam conteúdo dinâmico de forma bonita quando você passa o mouse sobre eles", "idea7": "Crie um campo de estrelas interativo relaxante que se move com gestos do mouse"}}}