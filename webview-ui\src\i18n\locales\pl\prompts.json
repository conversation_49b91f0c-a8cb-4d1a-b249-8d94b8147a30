{"title": "Tryby", "done": "<PERSON><PERSON><PERSON>", "modes": {"title": "Tryby", "createNewMode": "Utwórz nowy tryb", "importMode": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryb", "noMatchFound": "Nie znaleziono trybów", "editModesConfig": "Edytuj konfigurację trybów", "editGlobalModes": "Ed<PERSON><PERSON>j tryby globalne", "editProjectModes": "Edyt<PERSON>j tryby projektu (.kilocodemodes)", "createModeHelpText": "Tryby to wys<PERSON>ja<PERSON><PERSON>wan<PERSON> persony, kt<PERSON>re dostosowują zachowanie Kilo Code. <0>Dowiedz się o używaniu trybów</0> lub <1>dostosowywaniu trybów.</1>", "selectMode": "Szukaj trybów"}, "apiConfiguration": {"title": "Konfiguracja API", "select": "<PERSON><PERSON><PERSON><PERSON>, której konfiguracji API użyć dla tego trybu"}, "tools": {"title": "Dostępne narzędzia", "builtInModesText": "Narzędzia dla wbudowanych trybów nie mogą być modyfikowane", "editTools": "Edytuj narzędzia", "doneEditing": "Zakończ edycję", "allowedFiles": "Dozwolone pliki:", "toolNames": {"read": "Czytaj pliki", "edit": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "browser": "Używaj przeglądarki", "command": "Uruchamiaj polecenia", "mcp": "Używaj MCP"}, "noTools": "Brak"}, "roleDefinition": {"title": "Definicja roli", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Zdefiniuj wiedzę specjalistyczną i osobowość Kilo Code dla tego trybu. Ten opis kształtuje, jak Kilo Code prezentuje się i podchodzi do zadań."}, "description": {"title": "Krótki opis (dla lud<PERSON>)", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>ślny opis", "description": "Krótki opis wyświetlany w rozwijanej liście wyboru trybu."}, "whenToUse": {"title": "<PERSON><PERSON><PERSON> (opcjonalne)", "description": "<PERSON><PERSON><PERSON>, kiedy ten tryb powinien by<PERSON>. Pomaga to Orchestratorowi wybrać odpowiedni tryb dla zadania.", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> domyślny opis 'Kiedy używać'"}, "customInstructions": {"title": "Niestandardowe instrukcje dla trybu (opcjonalne)", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Dodaj wytyczne dotyczące zachowania specyficzne dla trybu {{modeName}}.", "loadFromFile": "Niestandardowe instrukcje dla trybu {{mode}} mogą być również ładowane z folderu <span>.kilocode/rules-{{slug}}/</span> w Twoim obszarze roboczym (.kilocoderules-{{slug}} i .clinerules-{{slug}} są przestarzałe i wkrótce przestaną działać)."}, "exportMode": {"title": "Eksportuj tryb", "description": "Eksportuj ten tryb z regułami z folderu .kilocode/rules-{{slug}}/ połączonymi w udostępnialny plik YAML. Oryginalne pliki pozostają niezmienione.", "exporting": "Eksportowanie..."}, "importMode": {"selectLevel": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> ten tryb:", "import": "Import<PERSON>j", "importing": "Importowanie...", "global": {"label": "Poziom globalny", "description": "Dostępne we wszystkich projektach. Jeśli wyeksportowany tryb zawierał pliki reguł, zostaną one odtworzone w globalnym folderze .kilocode/rules-{slug}/."}, "project": {"label": "Poziom projektu", "description": "Dostępne tylko w tym obszarze roboczym. Jeśli wyeksportowany tryb zawierał pliki reguł, zostaną one odtworzone w folderze .kilocode/rules-{slug}/."}}, "advanced": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "globalCustomInstructions": {"title": "Niestandardowe instrukcje dla wszystkich trybów", "description": "Te instrukcje dotyczą wszystkich trybów. Zapewniają podstawowy zestaw zachowań, które mogą być rozszerzone przez instrukcje specyficzne dla trybów poniżej. <0>Dowiedz się więcej</0>", "loadFromFile": "Instrukcje mogą być również ładowane z folderu <span>.kilocode/rules/</span> w Twoim obszarze roboczym (.kilocoderules i .clinerules są przestarzałe i wkrótce przestaną działać)."}, "systemPrompt": {"preview": "Podgląd podpowiedzi systemowej", "copy": "Kopiuj podpowiedź systemową do schowka", "title": "Podpowiedź systemowa (tryb {{modeName}})"}, "supportPrompts": {"title": "Podpowied<PERSON> pomo<PERSON>ze", "resetPrompt": "Zresetuj podpowiedź {{promptType}} do domyślnej", "prompt": "Podpowiedź", "enhance": {"apiConfiguration": "Konfiguracja API", "apiConfigDescription": "Możesz wybrać konfigurację API, która będzie zawsze używana do ulepszania podpowiedzi, lub po prostu użyć aktualnie wybranej", "useCurrentConfig": "Użyj aktualnie wybranej konfiguracji API", "testPromptPlaceholder": "W<PERSON><PERSON><PERSON><PERSON> podpowiedź, aby przetestować ulepszenie", "previewButton": "Podgląd ulepszenia podpowiedzi", "testEnhancement": "<PERSON><PERSON><PERSON>"}, "types": {"ENHANCE": {"label": "Ulepsz podpowiedź", "description": "Uż<PERSON>j ulepszenia podpowiedzi, aby u<PERSON><PERSON><PERSON> dostosowane sugestie lub ulepszenia dla swoich danych wejściowych. Zapewnia to, że Kilo Code rozumie Twoje intencje i dostarcza najlepsze możliwe odpowiedzi. Dostępne za pośrednictwem ikony ✨ w czacie."}, "EXPLAIN": {"label": "Wyjaśnij kod", "description": "Uzyskaj szczegółowe wyjaśnienia fragmentów kodu, funkcji lub całych plików. Przydatne do zrozumienia złożonego kodu lub nauki nowych wzorców. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "FIX": {"label": "<PERSON><PERSON><PERSON> problemy", "description": "Uzyskaj pomoc w identyfikowaniu i rozwiązywaniu błędów, usterek lub problemów z jakością kodu. Zapewnia krok po kroku wskazówki do naprawy problemów. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "IMPROVE": {"label": "Ulepsz kod", "description": "Otrzymuj sugestie dotyczące optymalizacji kodu, lepszych praktyk i ulepszeń architektonicznych przy zachowaniu funkcjonalności. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "ADD_TO_CONTEXT": {"label": "Dodaj do kontekstu", "description": "Dodaj kontekst do bieżącego zadania lub rozmowy. Przydatne do dostarczania dodatkowych informacji lub wyjaśnień. Dostępne w akcjach kodu (ikona żarówki w edytorze) i w menu kontekstowym edytor (prawy przycisk myszy na wybranym kodzie)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Dodaj zawartość terminala do kontekstu", "description": "Dodaj wyjście terminala do bieżącego zadania lub rozmowy. Przydatne do dostarczania wyników poleceń lub logów. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "TERMINAL_FIX": {"label": "Napraw polecenie terminala", "description": "Uzyskaj pomoc w naprawianiu poleceń terminala, które zawiodły lub wymagają ulepszeń. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "TERMINAL_EXPLAIN": {"label": "Wyjaśnij polecenie terminala", "description": "Uzyskaj szczegółowe wyjaśnienia poleceń terminala i ich wyników. Dostępne w menu kontekstowym terminala (prawy przycisk myszy na wybranej zawartości terminala)."}, "NEW_TASK": {"label": "Rozpocznij nowe zadanie", "description": "Rozpocznij nowe zadanie z wprowadzonymi danymi. Dostępne w palecie poleceń."}, "COMMIT_MESSAGE": {"label": "Generowanie Komunikatów Zatwierdzania", "description": "Generuj opisowe komunikaty commita na podstawie zaplanowanych zmian w git. Dostosuj zapytanie, aby generować komunikaty zgodne z najlepszymi praktykami Twojego repozytorium."}}}, "advancedSystemPrompt": {"title": "Zaawansowane: Zastąp podpowiedź systemową", "description": "<2>⚠️ Ostrzeżenie:</2> Ta zaawansowana funkcja omija zabezpieczenia. <1>PRZECZYTAJ TO PRZED UŻYCIEM!</1>Zastąp domyślną podpowiedź systemową, tworz<PERSON>c plik w <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Utwórz nowy tryb", "close": "Zamknij", "name": {"label": "Nazwa", "placeholder": "Wprowadź nazwę trybu"}, "slug": {"label": "Slug", "description": "Slug jest używany w adresach URL i nazwach plików. Powinien być małymi literami i zawierać tylko litery, liczby i myślniki."}, "saveLocation": {"label": "Lokalizacja zapisu", "description": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> ten tryb. Tryby specyficzne dla projektu mają pierwszeństwo przed trybami globalnymi.", "global": {"label": "Globalny", "description": "Dostępny we wszystkich obszarach roboczych"}, "project": {"label": "Specyficzny dla projektu (.kilocodemodes)", "description": "Dostępny tylko w tym obszarze roboczym, ma pierwszeństwo przed globalnym"}}, "roleDefinition": {"label": "Definicja roli", "description": "Zdefiniuj wiedzę specjalistyczną i osobowość Kilo Code dla tego trybu."}, "whenToUse": {"label": "<PERSON><PERSON><PERSON> (opcjonalne)", "description": "Podaj jasny opis, kiedy ten tryb jest najbardziej efektywny i w jakich typach zadań się sprawdza."}, "tools": {"label": "Dostępne narzędzia", "description": "<PERSON><PERSON><PERSON><PERSON>, których narzędzi może używać ten tryb."}, "description": {"label": "Krótki opis (dla lud<PERSON>)", "description": "Krótki opis wyświetlany w rozwijanej liście wyboru trybu."}, "customInstructions": {"label": "Niestandardowe instrukcje (opcjonalne)", "description": "Dodaj wytyczne dotyczące zachowania specyficzne dla tego trybu."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryb"}, "deleteMode": "<PERSON><PERSON><PERSON> tryb"}, "allFiles": "wszystkie pliki", "deleteMode": {"title": "<PERSON><PERSON><PERSON> tryb", "message": "<PERSON>zy na pewno chcesz usunąć tryb \"{{modeName}}\"?", "rulesFolder": "Ten tryb ma folder z regułami w {{folderPath}}, kt<PERSON>ry również zostanie usunięty.", "descriptionNoRules": "<PERSON>zy na pewno chcesz usunąć ten niestandardowy tryb?", "confirm": "Usuń", "cancel": "<PERSON><PERSON><PERSON>"}}