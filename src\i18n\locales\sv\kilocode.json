{"info": {"settings_imported": "Inställningar importerades framgångsrikt."}, "userFeedback": {"message_update_failed": "<PERSON><PERSON><PERSON>ades med att uppdatera meddelande", "no_checkpoint_found": "Ingen checkpoint hittades före detta meddelande", "message_updated": "Meddelande uppdaterat"}, "lowCreditWarning": {"title": "Varning för låg kredit!", "message": "Kontrollera om du kan fylla på med gratis krediter eller köpa fler!"}, "notLoggedInError": "Kan inte slutföra beg<PERSON>, se till att du är ansluten och inloggad med den valda leverantören.\n\n{{error}}", "rules": {"actions": {"delete": "<PERSON> bort", "confirmDelete": "<PERSON>r du säker på att du vill ta bort {{filename}}?", "deleted": "Tog bort {{filename}}"}, "errors": {"noWorkspaceFound": "Ingen arbetsytemapp hittades", "fileAlreadyExists": "Filen {{filename}} finns redan", "failedToCreateRuleFile": "<PERSON><PERSON><PERSON><PERSON> med att skapa regelfil.", "failedToDeleteRuleFile": "<PERSON><PERSON><PERSON>ades med att ta bort regelfil."}, "templates": {"workflow": {"description": "Arbetsflödesbeskrivning här...", "stepsHeader": "## Steg", "step1": "Steg 1", "step2": "Steg 2"}, "rule": {"description": "Regelbeskrivning här...", "guidelinesHeader": "## <PERSON><PERSON><PERSON><PERSON><PERSON>", "guideline1": "Riktlinje 1", "guideline2": "Riktlinje 2"}}}, "commitMessage": {"activated": "Kilo Code meddelandegenerator för kodkommittar aktiverad", "gitNotFound": "⚠️ Git-arkiv hittades inte eller git är inte tillgängligt", "gitInitError": "⚠️ Git initialiseringsfel: {{error}}", "generating": "<PERSON>lo: <PERSON><PERSON><PERSON> commit-meddelande...", "noChanges": "Kilo: Inga förändringar hittades att analysera", "generated": "<PERSON><PERSON>: Commit-meddelande genererat!", "generationFailed": "<PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON> med att generera commit-meddelande: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Genererar meddelande med ej iscensatta ändringar", "providerRegistered": "Kilo: Meddelandeleverantör för incheckningar registrerad", "activationFailed": "<PERSON><PERSON>: Misslyckades med att aktivera meddelandegeneratorn: {{error}}"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Autocomplete", "warning": "$(warning) Kilo Autocomplete", "disabled": "$(circle-slash) Kilo Autocomplete", "tooltip": {"disabled": "Kilo Code Autocomplete (inaktiverad)", "basic": "Kilo Code Autocomplete", "tokenError": "En giltig token måste anges för att använda automatisk komplettering", "lastCompletion": "Senaste slutförande:", "sessionTotal": "Sessionens totala kostnad:", "model": "Modell:"}, "cost": {"zero": "0,00 kr", "lessThanCent": "<0,01 kr"}}, "toggleMessage": "Kilo Autocomplete {{status}}"}, "ghost": {"progress": {"analyzing": "Analyserar din kod...", "generating": "Genererar föreslagna ändringar...", "processing": "Bearbetar föreslagna ändringar...", "showing": "Visar föreslagna ändringar...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: <PERSON><PERSON><PERSON> Uppgift", "placeholder": "t.ex. 'omstrukturera den här funktionen för att göra den mer effektiv'"}, "commands": {"displaySuggestions": "Visa föreslagna redigeringar", "generateSuggestions": "Kilo Code: <PERSON>rera Föreslagna Ändringar", "cancelSuggestions": "Avbryt föreslagna ändringar", "applyCurrentSuggestion": "Tillämpa nuvarande förslag på ändring", "applyAllSuggestions": "Tillämpa Alla Föreslagna Ändringar", "category": "Kilo Code", "promptCodeSuggestion": "Snabb uppgift"}, "codeAction": {"title": "Kilo Code: Föreslagna ändringar"}, "chatParticipant": {"name": "Agent", "fullName": "Kilo Code Agent", "description": "Jag kan hjä<PERSON>pa dig med snabba uppgifter och föreslagna ändringar."}, "messages": {"provideCodeSuggestions": "Ger kodförslag..."}}}