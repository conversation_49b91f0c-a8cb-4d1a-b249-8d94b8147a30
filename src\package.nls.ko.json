{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "코드를 계획, 빌드, 수정하기 위한 오픈소스 AI 코딩 도우미입니다.", "command.newTask.title": "새 작업", "command.explainCode.title": "코드 설명", "command.fixCode.title": "코드 수정", "command.improveCode.title": "코드 개선", "command.addToContext.title": "컨텍스트에 추가", "command.openInNewTab.title": "새 탭에서 열기", "command.focusInput.title": "입력 필드 포커스", "command.setCustomStoragePath.title": "사용자 지정 저장소 경로 설정", "command.importSettings.title": "설정 가져오기", "command.terminal.addToContext.title": "터미널 내용을 컨텍스트에 추가", "command.terminal.fixCommand.title": "이 명령어 수정", "command.terminal.explainCommand.title": "이 명령어 설명", "command.acceptInput.title": "입력/제안 수락", "command.generateCommitMessage.title": "Kilo로 커밋 메시지 생성", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "MCP 서버", "command.prompts.title": "모드", "command.history.title": "기록", "command.marketplace.title": "마켓플레이스", "command.openInEditor.title": "에디터에서 열기", "command.settings.title": "설정", "command.documentation.title": "문서", "command.profile.title": "프로필", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "'항상 실행 작업 승인' 이 활성화되어 있을 때 자동으로 실행할 수 있는 명령어", "settings.vsCodeLmModelSelector.description": "VSCode 언어 모델 API 설정", "settings.vsCodeLmModelSelector.vendor.description": "언어 모델 공급자 (예: copilot)", "settings.vsCodeLmModelSelector.family.description": "언어 모델 계열 (예: gpt-4)", "settings.customStoragePath.description": "사용자 지정 저장소 경로. 기본 위치를 사용하려면 비워두세요. 절대 경로를 지원합니다 (예: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Kilo Code 빠른 수정 사용 설정", "settings.autoImportSettingsPath.description": "확장 프로그램 시작 시 자동으로 가져올 Kilo Code 구성 파일의 경로입니다. 절대 경로 및 홈 디렉토리에 대한 상대 경로를 지원합니다(예: '~/Documents/kilo-code-settings.json'). 자동 가져오기를 비활성화하려면 비워 둡니다.", "ghost.input.title": "'Enter'를 눌러 확인하거나 'Escape'를 눌러 취소하세요", "ghost.input.placeholder": "무엇을 하고 싶은지 설명해주세요...", "ghost.commands.generateSuggestions": "Kilo Code: 편집 제안 생성", "ghost.commands.displaySuggestions": "편집 제안 표시", "ghost.commands.cancelSuggestions": "편집 제안 취소", "ghost.commands.applyCurrentSuggestion": "현재 편집 제안 적용", "ghost.commands.applyAllSuggestions": "모든 편집 제안 적용", "ghost.commands.promptCodeSuggestion": "빠른 작업", "ghost.commands.goToNextSuggestion": "다음 제안으로 이동", "ghost.commands.goToPreviousSuggestion": "이전 제안으로 이동"}