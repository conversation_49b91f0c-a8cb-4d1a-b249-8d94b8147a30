{"answers": {"yes": "Sí", "no": "No", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Eliminar", "keep": "<PERSON><PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Comentarios", "description": "Nos encantaría escuchar tus comentarios o ayudarte con cualquier problema que estés experimentando.", "githubIssues": "Reportar un problema en GitHub", "githubDiscussions": "Unirse a las discusiones de GitHub", "discord": "Únete a nuestra comunidad de Discord", "customerSupport": "Soporte al Cliente"}, "ui": {"search_placeholder": "Buscar..."}, "mermaid": {"loading": "Generando diagrama mermaid...", "render_error": "No se puede renderizar el diagrama", "fixing_syntax": "Corrigiendo sintaxis de Mermaid...", "fix_syntax_button": "Corregir sintaxis con IA", "original_code": "Código original:", "errors": {"unknown_syntax": "Error de sintaxis desconocido", "fix_timeout": "La solicitud de corrección de IA agotó el tiempo de espera", "fix_failed": "La corrección de IA falló", "fix_attempts": "No se pudo corregir la sintaxis después de {{attempts}} intentos. Último error: {{error}}", "no_fix_provided": "La IA no pudo proporcionar una corrección", "fix_request_failed": "La solicitud de corrección falló"}, "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "Reducir", "copy": "Copiar", "save": "Guardar imagen", "viewCode": "<PERSON>er código", "viewDiagram": "Ver diagrama", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Código"}, "feedback": {"imageCopied": "Imagen copiada al portapapeles", "copyError": "Error copiando la <PERSON>n"}}, "file": {"errors": {"invalidDataUri": "Formato de URI de datos inválido", "copyingImage": "Error copiando la imagen: {{error}}", "openingImage": "Error abriendo la imagen: {{error}}", "pathNotExists": "La ruta no existe: {{path}}", "couldNotOpen": "No se pudo abrir el archivo: {{error}}", "couldNotOpenGeneric": "¡No se pudo abrir el archivo!"}, "success": {"imageDataUriCopied": "URI de datos de imagen copiada al portapapeles"}}}