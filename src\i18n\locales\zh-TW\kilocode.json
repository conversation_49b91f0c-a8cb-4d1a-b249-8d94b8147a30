{"info": {"settings_imported": "設定匯入成功。"}, "userFeedback": {"message_update_failed": "更新訊息失敗", "no_checkpoint_found": "在此訊息之前未找到檢查點", "message_updated": "訊息已成功更新"}, "lowCreditWarning": {"title": "餘額不足警告！", "message": "請檢查是否可以充值免費額度或購買更多額度！"}, "notLoggedInError": "無法完成請求，請確保你已連接並登入所選的提供者。\n\n{{error}}", "rules": {"actions": {"delete": "刪除", "confirmDelete": "確定要刪除 {{filename}} 嗎？", "deleted": "已刪除 {{filename}}"}, "errors": {"noWorkspaceFound": "未找到工作區資料夾", "fileAlreadyExists": "檔案 {{filename}} 已存在", "failedToCreateRuleFile": "建立規則檔案失敗。", "failedToDeleteRuleFile": "刪除規則檔案失敗。"}, "templates": {"workflow": {"description": "工作流程描述在此...", "stepsHeader": "## 步驟", "step1": "步驟 1", "step2": "步驟 2"}, "rule": {"description": "規則描述在此...", "guidelinesHeader": "## 指導原則", "guideline1": "指導原則 1", "guideline2": "指導原則 2"}}}, "commitMessage": {"activated": "Kilo Code: 代码提交消息生成器已激活", "gitNotFound": "⚠️ Git 仓库未找到或 git 不可用", "gitInitError": "⚠️ Git 初始化错误：{{error}}", "generating": "<PERSON>lo: 正在生成提交信息...", "noChanges": "Kilo: 未发现可供分析的变更", "generated": "Kilo：提交信息已生成！", "generationFailed": "Kilo：无法生成提交信息：{{errorMessage}}", "generatingFromUnstaged": "Kilo: 使用未暂存的更改生成消息", "activationFailed": "Kilo：未能激活消息生成器：{{error}}", "providerRegistered": "Kilo：提交消息提供者已注册"}, "autocomplete": {"statusBar": {"disabled": "$(circle-slash) Kilo 自动补全", "enabled": "$(sparkle) Kilo 自动补全", "tooltip": {"basic": "Kilo自动补全", "lastCompletion": "上次完成：", "disabled": "Kilo自动补全（已禁用）", "sessionTotal": "会话总费用:", "tokenError": "必须设置有效令牌才能使用自动完成功能", "model": "模型："}, "warning": "$(warning) Kilo自动补全", "cost": {"lessThanCent": "<$0.01", "zero": "¥0.00"}}, "toggleMessage": "Kilo 自动补全 {{status}}"}, "ghost": {"progress": {"generating": "正在生成建议的编辑...", "analyzing": "正在分析您的代码...", "showing": "显示建议的编辑...", "processing": "处理建议的编辑...", "title": "Kilo Code"}, "input": {"title": "Kilo Code：快速任务", "placeholder": "例如，\"重构这个函数使其更高效\""}, "commands": {"applyCurrentSuggestion": "应用当前建议的编辑", "generateSuggestions": "Kilo Code: 生成建议的修改", "cancelSuggestions": "取消建议的编辑", "displaySuggestions": "显示建议的编辑", "category": "Kilo Code", "promptCodeSuggestion": "快速任务提示", "applyAllSuggestions": "应用所有建议的编辑"}, "codeAction": {"title": "Kilo Code：建议的编辑"}, "chatParticipant": {"name": "代理", "fullName": "Kilo Code 代理", "description": "我可以帮你处理快速任务和提供建议的编辑。"}, "messages": {"provideCodeSuggestions": "正在提供代码建议..."}}}