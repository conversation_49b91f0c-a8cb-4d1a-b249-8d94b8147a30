{"welcome": {"greeting": "¡Bienvenido a Kilo Code!", "introText1": "Kilo Code es un agente de codificación de IA gratuito y de código abierto.", "introText2": "Funciona con los modelos de IA más recientes como Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, y más de 450 modelos adicionales.", "introText3": "Crea una cuenta gratuita y obtén $20 en tokens para usar con cualquier modelo de IA.", "ctaButton": "<PERSON>rear cuenta gratuita", "manualModeButton": "Usa tu propia clave API", "alreadySignedUp": "¿Ya te registraste?", "loginText": "Inicia sesión aquí"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON>", "lowBalance": "Tu saldo de Kilo Code es bajo"}, "notifications": {"toolRequest": "Solicitud de herramienta pendiente de aprobación", "browserAction": "Acción del navegador pendiente de aprobación", "command": "Comando pendiente de aprobación"}, "settings": {"sections": {"mcp": "Servidores MCP"}, "provider": {"account": "Cuenta de Kilo Code", "apiKey": "Clave API de Kilo Code", "login": "Iniciar sesión en Kilo Code", "logout": "<PERSON><PERSON>r se<PERSON> de Kilo <PERSON>"}, "contextManagement": {"allowVeryLargeReads": {"label": "Permitir lecturas de archivos muy grandes", "description": "<PERSON><PERSON>do está activado, Kilo Code realizará lecturas de archivos o salidas MCP muy grandes aunque exista una alta probabilidad de desbordar la ventana de contexto (tamaño del contenido >80% de la ventana de contexto)."}}, "systemNotifications": {"label": "Activar notificaciones del sistema", "description": "<PERSON><PERSON>do está activado, Kilo Code enviará notificaciones del sistema para eventos importantes como la finalización de tareas o errores.", "testButton": "Probar notificación", "testTitle": "Kilo Code", "testMessage": "Esta es una notificación de prueba de Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code quiere condensar tu conversación", "condenseConversation": "Condensar Conversación"}}, "newTaskPreview": {"task": "Tarea"}, "profile": {"title": "Perfil", "dashboard": "Panel de control", "logOut": "<PERSON><PERSON><PERSON>", "currentBalance": "SALDO ACTUAL", "loading": "Cargando..."}, "docs": "Documentos", "rules": {"tooltip": "Gestionar Reglas y Flujos de Trabajo de Kilo Code", "ariaLabel": "Reglas de Kilo Code", "tabs": {"rules": "Reg<PERSON>", "workflows": "Flujos de Trabajo"}, "description": {"rules": "Las reglas te permiten proporcionar a Kilo Code instrucciones que debe seguir en todos los modos y para todos los prompts. Son una forma persistente de incluir contexto y preferencias para todas las conversaciones en tu espacio de trabajo o globalmente.", "workflows": "Los flujos de trabajo son una plantilla preparada para una conversación. Los flujos de trabajo te permiten definir prompts que usas frecuentemente, y pueden incluir una serie de pasos para guiar a Kilo Code a través de tareas repetitivas, como desplegar un servicio o enviar una PR. Para invocar un flujo de trabajo, escribe", "workflowsInChat": "en el chat."}, "sections": {"globalRules": "Reglas Globales", "workspaceRules": "Reglas del Espacio de Trabajo", "globalWorkflows": "Flujos de Trabajo Globales", "workspaceWorkflows": "Flujos de Trabajo del Espacio de Trabajo"}, "validation": {"invalidFileExtension": "Solo se permiten extensiones .md, .txt, o sin extensión"}, "placeholders": {"workflowName": "nombre-flujo-trabajo (.md, .txt, o sin extensión)", "ruleName": "nombre-regla (.md, .txt, o sin extensión)"}, "newFile": {"newWorkflowFile": "Nuevo archivo de flujo de trabajo...", "newRuleFile": "Nuevo archivo de regla..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Ver {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "lanzamiento del navegador", "browser_action": "acción del navegador", "checkpoint_saved": "punto de control guardado", "command_output": "salida del comando", "browser_action_result": "resultado de la acción del navegador", "command": "ejecución de comandos", "condense_context": "condensar contexto", "mcp_server_response": "Respuesta del servidor MCP", "completion_result": "resultado de completado", "followup": "pregunta de seguimiento", "reasoning": "Razonamiento de IA", "tool": "uso de herramientas", "text": "Respuesta de IA", "error": "mensaje de <PERSON>", "unknown": "tipo de mensaje desconocido", "use_mcp_server": "Uso del servidor MCP", "user": "opinión del usuario"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "Enviar", "restoreAndSend": "Restaurar y Enviar"}, "ideaSuggestionsBox": {"newHere": "¿Nuevo aquí?", "suggestionText": "<suggestionButton>Haz clic aquí</suggestionButton> para una idea genial, luego toca <sendIcon /> y ¡mira cómo hago magia!", "ideas": {"idea1": "Crea una esfera 3D giratoria y brillante que responda a los movimientos del ratón. Haz que la aplicación funcione en el navegador.", "idea2": "Crea un sitio web de portafolio para un desarrollador de software Python", "idea3": "Crea una maqueta de aplicación financiera en el navegador. <PERSON>ego prueba si funciona", "idea4": "Crea un sitio web directorio que contenga los mejores modelos de generación de video AI actualmente", "idea5": "Crea un generador de gradientes CSS que exporte hojas de estilo personalizadas y muestre una vista previa en vivo", "idea6": "Genera tarjetas que revelen contenido dinámico de forma hermosa al pasar el ratón por encima", "idea7": "Crea un campo de estrellas interactivo y relajante que se mueva con gestos del ratón"}}}