{"greeting": "<PERSON><PERSON> Code có thể giúp gì cho bạn?", "task": {"title": "Nhiệm vụ", "seeMore": "<PERSON><PERSON>", "seeLess": "<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Bộ nhớ đệm:", "apiCost": "Chi phí API:", "contextWindow": "<PERSON><PERSON><PERSON> dài b<PERSON>i cảnh:", "closeAndStart": "<PERSON><PERSON><PERSON> nhiệm vụ và bắt đầu nhiệm vụ mới", "export": "<PERSON><PERSON><PERSON> lịch sử nhiệm vụ", "delete": "<PERSON><PERSON><PERSON> nhi<PERSON> vụ (Shift + Click để bỏ qua xác nhận)", "condenseContext": "<PERSON><PERSON> đọng ngữ cảnh thông minh", "share": "Chia sẻ nhiệm vụ", "shareWithOrganization": "<PERSON>a sẻ với tổ chức", "shareWithOrganizationDescription": "Chỉ thành viên tổ chức của bạn mới có thể truy cập", "sharePublicly": "<PERSON>a sẻ công khai", "sharePubliclyDescription": "<PERSON><PERSON>t kỳ ai có liên kết đều có thể truy cập", "connectToCloud": "<PERSON><PERSON><PERSON> n<PERSON>i với <PERSON>", "connectToCloudDescription": "<PERSON><PERSON><PERSON> nhập v<PERSON><PERSON> Cloud để chia sẻ tác vụ", "sharingDisabledByOrganization": "<PERSON><PERSON> sẻ bị tổ chức vô hiệu hóa", "shareSuccessOrganization": "<PERSON><PERSON><PERSON> kết tổ chức đã đư<PERSON>c sao chép vào clipboard", "shareSuccessPublic": "<PERSON><PERSON><PERSON> kết công khai đã được sao chép vào clipboard"}, "history": {"title": "<PERSON><PERSON><PERSON>"}, "unpin": "Bỏ ghim khỏi đầu", "pin": "<PERSON><PERSON> lên đ<PERSON>u", "tokenProgress": {"availableSpace": "<PERSON><PERSON>ông gian khả dụng: {{amount}} tokens", "tokensUsed": "Tokens đã sử dụng: {{used}} trong {{total}}", "reservedForResponse": "<PERSON><PERSON><PERSON> riêng cho phản hồi mô hình: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON> lại", "tooltip": "<PERSON><PERSON><PERSON> lại thao tác"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> n<PERSON> vụ mới", "tooltip": "<PERSON>ắt đầu một nhiệm vụ mới"}, "reportBug": {"title": "Báo lỗi"}, "proceedAnyways": {"title": "Vẫn tiếp tục", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tục trong khi lệnh đang chạy"}, "save": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> c<PERSON>c thay đổi tệp"}, "reject": {"title": "<PERSON><PERSON> chối", "tooltip": "Từ chối hành động này"}, "completeSubtaskAndReturn": "<PERSON><PERSON><PERSON> thành nhi<PERSON> vụ phụ và quay lại", "approve": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON>t hành động này"}, "runCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON>h<PERSON><PERSON> thi lệnh này"}, "proceedWhileRunning": {"title": "<PERSON><PERSON><PERSON><PERSON> tục trong khi chạy", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> bất chấp cảnh báo"}, "killCommand": {"title": "<PERSON><PERSON><PERSON> l<PERSON>", "tooltip": "<PERSON><PERSON><PERSON> l<PERSON>nh hiện tại"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ", "tooltip": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ hiện tại"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> n<PERSON> v<PERSON> hiện tại"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> thao tác hiện tại"}, "scrollToBottom": "<PERSON><PERSON><PERSON>n xuống cuối cuộc trò chuyện", "about": "<PERSON><PERSON><PERSON>, tái cấu trúc và gỡ lỗi mã bằng sự hỗ trợ của AI. Ki<PERSON>m tra <DocsLink>tài liệu</DocsLink> của chúng tôi để tìm hiểu thêm.", "onboarding": "<strong><PERSON><PERSON> sách nhiệm vụ của bạn trong không gian làm việc này trống.</strong> Bắt đầu bằng cách nhập nhiệm vụ bên dưới. Bạn không chắc chắn nên bắt đầu như thế nào? Đọc thêm về những gì Kilo Code có thể làm cho bạn trong <DocsLink>tài liệu</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> vụ", "description": "<PERSON><PERSON> nhỏ các nhiệm vụ thành các phần nhỏ hơn, <PERSON><PERSON> quản lý hơn."}, "stickyModels": {"title": "<PERSON><PERSON> độ dính", "description": "Mỗi chế độ ghi nhớ mô hình đã sử dụng cuối cùng của bạn"}, "tools": {"title": "<PERSON><PERSON><PERSON> cụ", "description": "<PERSON> phép AI gi<PERSON>i quyết vấn đề bằng cách duyệt web, ch<PERSON><PERSON> l<PERSON>, v.v."}, "customizableModes": {"title": "Chế độ tùy chỉnh", "description": "<PERSON><PERSON>c nhân vật chuyên biệt với hành vi riêng và mô hình được chỉ định"}}, "selectMode": "<PERSON><PERSON><PERSON> chế độ tương tác", "selectApiConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh <PERSON>", "selectModelConfig": "<PERSON><PERSON><PERSON> mô hình", "enhancePrompt": "<PERSON><PERSON><PERSON> cao yêu cầu với ngữ cảnh bổ sung", "addImages": "<PERSON><PERSON><PERSON><PERSON> hình <PERSON>nh vào tin nh<PERSON>n", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "stopTts": "<PERSON><PERSON><PERSON> chuyển văn bản thành giọng nói", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn...", "typeTask": "<PERSON><PERSON><PERSON>, t<PERSON><PERSON>, hỏi điều gì đó", "addContext": "@ để thêm ngữ cảnh, / để chuyển chế độ", "dragFiles": "giữ shift để kéo tệp", "dragFilesImages": "giữ shift để kéo tệp/hình <PERSON>nh", "enhancePromptDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cao yêu cầu' gi<PERSON><PERSON> cải thiện yêu cầu của bạn bằng cách cung cấp ngữ cảnh bổ sung, làm rõ hoặc diễn đạt lại. H<PERSON><PERSON> thử nhập yêu cầu tại đây và nhấp vào nút một lần nữa để xem cách thức hoạt động.", "modeSelector": {"title": "<PERSON><PERSON> độ", "marketplace": "Chợ Chế độ", "settings": "Cài đặt Chế độ", "description": "<PERSON><PERSON><PERSON> nhân cách chuyên biệt điều chỉnh hành vi của Kilo Code."}, "errorReadingFile": "Lỗi khi đọc tệp:", "noValidImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh hợp lệ nào đư<PERSON><PERSON> xử lý", "separator": "<PERSON><PERSON><PERSON> phân cách", "edit": "Chỉnh sửa...", "forNextMode": "cho chế độ tiếp theo", "error": "Lỗi", "diffError": {"title": "Chỉnh sửa không thành công"}, "troubleMessage": "Kilo Code đang gặp sự cố...", "apiRequest": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "failed": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> thất bại", "streaming": "<PERSON><PERSON><PERSON> cầu API...", "cancelled": "<PERSON><PERSON><PERSON> c<PERSON>u API đã hủy", "streamingFailed": "Streaming API thất bại"}, "checkpoint": {"initial": "<PERSON><PERSON><PERSON><PERSON> kiểm tra ban đầu", "regular": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "initializingWarning": "<PERSON>ang khởi tạo điểm kiểm tra... Nếu quá trình này mất quá nhiều thời gian, bạn có thể vô hiệu hóa điểm kiểm tra trong <settingsLink>cài đặt</settingsLink> và khởi động lại tác vụ của bạn.", "menu": {"viewDiff": "<PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON> phục điểm kiểm tra", "restoreFiles": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>p", "restoreFilesDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này.", "restoreFilesAndTask": "Khô<PERSON> phục tệ<PERSON> & nhiệm vụ", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "restoreFilesAndTaskDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này và xóa tất cả tin nhắn sau điểm này."}, "current": "<PERSON><PERSON><PERSON> t<PERSON>i"}, "instructions": {"wantsToFetch": "Kilo Code muốn lấy hướng dẫn chi tiết để hỗ trợ nhiệm vụ hiện tại"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> muốn đ<PERSON><PERSON> tệ<PERSON> này:", "wantsToReadOutsideWorkspace": "<PERSON>lo Code muốn đọc tệp này bên ngoài không gian làm việc:", "didRead": "<PERSON>lo Code đã đọc tệp này:", "wantsToEdit": "<PERSON><PERSON> Code muốn chỉnh sửa tệp này:", "wantsToEditOutsideWorkspace": "<PERSON>lo Code muốn chỉnh sửa tệp này bên ngoài không gian làm việc:", "wantsToEditProtected": "<PERSON><PERSON> Code muốn chỉnh sửa tệp cấu hình đượ<PERSON> bảo vệ:", "wantsToCreate": "<PERSON><PERSON> muốn tạo một tệp mới:", "wantsToSearchReplace": "<PERSON><PERSON> muốn thực hiện tìm kiếm và thay thế trong tệp này:", "didSearchReplace": "<PERSON><PERSON> Code đã thực hiện tìm kiếm và thay thế trong tệp này:", "wantsToInsert": "<PERSON><PERSON> muốn chèn nội dung vào tệp này:", "wantsToInsertWithLineNumber": "Kilo Code muốn chèn nội dung vào dòng {{lineNumber}} của tệp này:", "wantsToInsertAtEnd": "<PERSON><PERSON> muốn thêm nội dung vào cuối tệp này:", "wantsToReadAndXMore": "<PERSON><PERSON> <PERSON> muốn đọc tệp này và {{count}} tệp khác:", "wantsToReadMultiple": "<PERSON>lo <PERSON> muốn đ<PERSON>c nhi<PERSON>u tệp:", "wantsToApplyBatchChanges": "<PERSON><PERSON> muốn áp dụng thay đổi cho nhiều tệp:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON> muốn xem các tệp cấp cao nhất trong thư mục này:", "didViewTopLevel": "<PERSON><PERSON> Code đã xem các tệp cấp cao nhất trong thư mục này:", "wantsToViewRecursive": "<PERSON>lo <PERSON> muốn xem đệ quy tất cả các tệp trong thư mục này:", "didViewRecursive": "<PERSON><PERSON> Code đã xem đệ quy tất cả các tệp trong thư mục này:", "wantsToViewDefinitions": "Kilo Code muốn xem tên định nghĩa mã nguồn đư<PERSON>c sử dụng trong thư mục này:", "didViewDefinitions": "<PERSON>lo Code đã xem tên định nghĩa mã nguồn đư<PERSON><PERSON> sử dụng trong thư mục này:", "wantsToSearch": "Kilo Code muốn tìm kiếm trong thư mục này cho <code>{{regex}}</code>:", "didSearch": "Kilo Code đã tìm kiếm trong thư mục này cho <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code muốn tìm kiếm trong thư mục này (ngo<PERSON>i không gian làm việc) cho <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code đã tìm kiếm trong thư mục này (ngo<PERSON>i không gian làm việc) cho <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON>lo Code muốn xem các tệp cấp cao nhất trong thư mục này (ngo<PERSON>i không gian làm việc):", "didViewTopLevelOutsideWorkspace": "<PERSON>lo Code đã xem các tệp cấp cao nhất trong thư mục này (ngoài không gian làm việc):", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code muốn xem đệ quy tất cả các tệp trong thư mục này (ngo<PERSON>i không gian làm việc):", "didViewRecursiveOutsideWorkspace": "<PERSON>lo Code đã xem đệ quy tất cả các tệp trong thư mục này (ngoài không gian làm việc):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code muốn xem tên định nghĩa mã nguồn được sử dụng trong thư mục này (ngoài không gian làm việc):", "didViewDefinitionsOutsideWorkspace": "<PERSON>lo Code đã xem tên định nghĩa mã nguồn được sử dụng trong thư mục này (ngoài không gian làm việc):"}, "commandOutput": "<PERSON><PERSON><PERSON> qu<PERSON> l<PERSON>nh", "response": "<PERSON><PERSON><PERSON>", "arguments": "<PERSON>ham s<PERSON>", "mcp": {"wantsToUseTool": "Kilo Code muốn sử dụng một công cụ trên máy chủ MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code muốn truy cập một tài nguyên trên máy chủ MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code muốn chuyển sang chế độ <code>{{mode}}</code>", "wantsToSwitchWithReason": "Kilo Code muốn chuyển sang chế độ <code>{{mode}}</code> vì: {{reason}}", "didSwitch": "Kilo Code đã chuyển sang chế độ <code>{{mode}}</code>", "didSwitchWithReason": "Kilo Code đã chuyển sang chế độ <code>{{mode}}</code> vì: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code muốn tạo một nhiệm vụ phụ mới trong chế độ <code>{{mode}}</code>:", "wantsToFinish": "Kilo Code muốn hoàn thành nhiệm vụ phụ này", "newTaskContent": "Hướng dẫn nhiệm vụ phụ", "completionContent": "Nhiệm vụ phụ đã hoàn thành", "resultContent": "<PERSON><PERSON><PERSON> qu<PERSON> n<PERSON> vụ phụ", "defaultResult": "<PERSON>ui lòng tiếp tục với nhiệm vụ tiếp theo.", "completionInstructions": "Nhiệm vụ phụ đã hoàn thành! Bạn có thể xem lại kết quả và đề xuất các sửa đổi hoặc bước tiếp theo. Nếu mọi thứ có vẻ tốt, hãy xác nhận để trả kết quả về nhiệm vụ chính."}, "questions": {"hasQuestion": "Kilo Code có một câu hỏi:"}, "taskCompleted": "Nhiệm v<PERSON> hoàn thành", "powershell": {"issues": "<PERSON><PERSON> vẻ như bạn đang gặp vấn đề với Windows PowerShell, vui lòng xem"}, "autoApprove": {"title": "Tự động phê duyệt:", "none": "K<PERSON>ô<PERSON>", "description": "Tự động phê duyệt cho phép Kilo Code thực hiện hành động mà không cần xin phép. Chỉ bật cho các hành động bạn hoàn toàn tin tưởng. Cấu hình chi tiết hơn có sẵn trong <settingsLink>Cài đặt</settingsLink>."}, "reasoning": {"thinking": "<PERSON><PERSON> suy nghĩ", "seconds": "{{count}} gi<PERSON>y"}, "contextCondense": {"title": "<PERSON><PERSON> cảnh đã tóm tắt", "condensing": "<PERSON><PERSON> cô đọng ngữ cảnh...", "errorHeader": "<PERSON><PERSON><PERSON><PERSON> thể cô đọng ngữ cảnh", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON>o chép vào ô nhập liệu (hoặc Shift + nhấp chuột)", "autoSelectCountdown": "Tự động chọn sau {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Roo Code {{version}} <PERSON><PERSON> phát hành", "description": "Roo Code {{version}} mang đến các tính năng mạnh mẽ mới và cải tiến đáng kể để nâng cao quy trình phát triển của bạn.", "whatsNew": "<PERSON>ó gì mới", "feature1": "<bold>Lập chỉ mục Codebase Tốt nghiệp từ Thử nghiệm</bold>: Lập chỉ mục codebase đầy đủ hiện đã ổn định và sẵn sàng cho sử dụng sản xuất với tìm kiếm cải tiến và hiểu biết ngữ cảnh.", "feature2": "<bold><PERSON><PERSON><PERSON> năng <PERSON> sách Todo <PERSON>ớ<PERSON></bold>: <PERSON><PERSON><PERSON> nhiệm vụ của bạn đúng hướng với quản lý todo tích hợp giúp bạn duy trì tổ chức và tập trung vào mục tiêu phát triển.", "feature3": "<bold><PERSON><PERSON><PERSON> thiện Chu<PERSON>ển đổi từ Architect sang Code</bold>: <PERSON>yển đổi mượt mà từ lập kế hoạch trong chế độ Architect sang triển khai trong chế độ Code.", "hideButton": "Ẩn thông báo", "detailsDiscussLinks": "<PERSON><PERSON><PERSON><PERSON> thêm chi tiết và thảo luận tại <discordLink>Discord</discordLink> và <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Kilo Code muốn sử dụng trình duy<PERSON>t:", "consoleLogs": "<PERSON><PERSON><PERSON><PERSON> ký bảng điều khiển", "noNewLogs": "(<PERSON><PERSON><PERSON><PERSON> có nhật ký mới)", "screenshot": "Ảnh chụp màn hình trình du<PERSON>t", "cursor": "con trỏ", "navigation": {"step": "Bước {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "sessionStarted": "<PERSON><PERSON><PERSON> trình duyệt đã bắt đầu", "actions": {"title": "<PERSON><PERSON><PERSON> động trình du<PERSON>: ", "launch": "Khởi chạy trình du<PERSON>t tại {{url}}", "click": "<PERSON><PERSON><PERSON>p ({{coordinate}})", "type": "G<PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>ng", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> lên", "close": "<PERSON><PERSON><PERSON> tr<PERSON>"}}, "codeblock": {"tooltips": {"expand": "Mở rộng khối mã", "collapse": "<PERSON><PERSON> gọn kh<PERSON>i mã", "enable_wrap": "<PERSON><PERSON>t tự động xuống dòng", "disable_wrap": "Tắt tự động xuống dòng", "copy_code": "Sao chép mã"}}, "systemPromptWarning": "CẢNH BÁO: <PERSON><PERSON> kích hoạt ghi đè lệnh nhắc hệ thống tùy chỉnh. Điều này có thể phá vỡ nghiêm trọng chức năng và gây ra hành vi không thể dự đoán.", "profileViolationWarning": "<PERSON><PERSON> sơ hiện tại vi phạm cài đặt của tổ chức của bạn", "shellIntegration": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o thực thi lệnh", "description": "Lệnh của bạn đang được thực thi mà không có tích hợp shell terminal VSCode. Đ<PERSON> <PERSON>n cảnh báo nà<PERSON>, bạn có thể vô hiệu hóa tích hợp shell trong phần <strong>Terminal</strong> của <settingsLink>cài đặt Kilo Code</settingsLink> hoặc khắc phục sự cố tích hợp terminal VSCode bằng liên kết bên dưới.", "troubleshooting": "<PERSON><PERSON>ấ<PERSON> vào đây để xem tài liệu tích hợp shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Đ<PERSON> Đ<PERSON>t G<PERSON>ới Hạn <PERSON> Tự Động <PERSON> Duy<PERSON>", "description": "Kilo Code đã đạt đến giới hạn tự động phê duyệt là {{count}} yêu cầu API. Bạn có muốn đặt lại bộ đếm và tiếp tục nhiệm vụ không?", "button": "Đặt lại và T<PERSON>ế<PERSON> tục"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code> trong <code>{{path}}</code>:", "didSearch": "<PERSON><PERSON> tìm thấy {{count}} kết quả cho <code>{{query}}</code>:", "resultTooltip": "<PERSON><PERSON><PERSON><PERSON> tương tự: {{score}} (nhấp để mở tệp)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> nhận tất cả"}, "deny": {"title": "<PERSON>ừ chối tất cả"}}, "indexingStatus": {"ready": "Chỉ mục sẵn sàng", "indexing": "<PERSON><PERSON> lập chỉ mục {{percentage}}%", "indexed": "<PERSON><PERSON> lập chỉ mục", "error": "Lỗi chỉ mục", "status": "Tr<PERSON>ng thái chỉ mục"}, "versionIndicator": {"ariaLabel": "<PERSON><PERSON><PERSON> bản {{version}} - <PERSON><PERSON><PERSON><PERSON> để xem ghi chú phát hành"}}