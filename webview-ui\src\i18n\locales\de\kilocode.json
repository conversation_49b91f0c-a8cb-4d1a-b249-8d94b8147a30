{"welcome": {"greeting": "Willkommen bei Kilo Code!", "introText1": "Kilo Code ist ein kostenloser Open-Source-KI-Coding-Agent.", "introText2": "<PERSON>r funktioniert mit den neuesten KI-Modellen wie Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 und über 450 weiteren.", "introText3": "<PERSON><PERSON><PERSON> ein kostenloses Konto und erhalte $20 an Tokens zur Nutzung mit jedem KI-Modell.", "ctaButton": "Kostenloses <PERSON> er<PERSON>llen", "manualModeButton": "Eigenen API-Schlüssel verwenden", "alreadySignedUp": "Bereits registriert?", "loginText": "<PERSON><PERSON>"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON><PERSON>", "lowBalance": "Ihr Kilo Code G<PERSON>aben ist niedrig"}, "notifications": {"toolRequest": "Werkzeuganfrage wartet auf Genehmigung", "browserAction": "Browseraktion wartet auf Genehmigung", "command": "Befehl wartet auf Genehmigung"}, "settings": {"sections": {"mcp": "MCP-Server"}, "provider": {"account": "<PERSON><PERSON>", "apiKey": "Kilo Code API-Schlüssel", "login": "Bei Kilo Code anmelden", "logout": "Von Kilo Code abmelden"}, "contextManagement": {"allowVeryLargeReads": {"label": "Sehr große Dateilesevorgänge erlauben", "description": "<PERSON><PERSON> a<PERSON>, führt Kilo Code sehr große Datei- oder MCP-Ausgabelesevorgänge durch, auch wenn eine hohe Wahrscheinlichkeit besteht, das Kontextfenster zu überschreiten (Inhaltsgröße >80% des Kontextfensters)."}}, "systemNotifications": {"label": "Systembenachrichtigungen aktivieren", "description": "<PERSON><PERSON> a<PERSON>, sendet Kilo Code Systembenachrichtigungen für wichtige Ereignisse wie Aufgabenabschluss oder Fehler.", "testButton": "Benachrichtigung testen", "testTitle": "Kilo Code", "testMessage": "Dies ist eine Testbenachrichtigung von Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code möchte deine Unterhaltung zusammenfassen", "condenseConversation": "Unterhaltung zusammenfassen"}}, "newTaskPreview": {"task": "Aufgabe"}, "profile": {"title": "Profil", "dashboard": "Dashboard", "logOut": "Abmelden", "currentBalance": "AKTUELLES GUTHABEN", "loading": "Lädt..."}, "docs": "Dokumentation", "rules": {"tooltip": "Kilo Code Regeln & Workflows verwalten", "ariaLabel": "Kilo <PERSON>n", "tabs": {"rules": "Regeln", "workflows": "Workflows"}, "description": {"rules": "Regeln ermöglichen es dir, <PERSON><PERSON> zu geben, die es in allen Modi und für alle Prompts befolgen soll. Sie sind eine dauerhafte Möglichkeit, Kontext und Präferenzen für alle Unterhaltungen in deinem Arbeitsbereich oder global einzubeziehen.", "workflows": "Workflows sind eine vorbereitete Vorlage für eine Unterhaltung. Workflows ermöglichen es dir, Prompts zu definieren, die du häufig verwendest, und können eine Reihe von Schritten enthalten, um Kilo Code durch sich wiederholende Aufgaben zu führen, wie das Bereitstellen eines Dienstes oder das Einreichen einer PR. Um einen Workflow aufzurufen, tippe", "workflowsInChat": "im Chat."}, "sections": {"globalRules": "Globale Regeln", "workspaceRules": "Arbeitsbereich-Regeln", "globalWorkflows": "Globale Workflows", "workspaceWorkflows": "Arbeitsbereich-Workflows"}, "validation": {"invalidFileExtension": "Nur .md, .txt oder keine Dateierweiterung erlaubt"}, "placeholders": {"workflowName": "workflow-name (.md, .txt oder keine Erweiterung)", "ruleName": "regel-name (.md, .txt oder keine Erweiterung)"}, "newFile": {"newWorkflowFile": "Neue Workflow-Datei...", "newRuleFile": "Neue Regel-Datei..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "Browser-Start", "browser_action_result": "Browser-Aktionsergebnis", "browser_action": "Browser-Aktion", "command": "Befehlsausführung", "completion_result": "Fertigstellungsergebnis", "condense_context": "Kontext verdichten", "command_output": "Befehlsausgabe", "error": "Fehlermeldung", "reasoning": "KI-Denken", "mcp_server_response": "MCP-Server-Antwort", "checkpoint_saved": "Kontrollpunkt gespeichert", "text": "KI-Antwort", "unknown": "unbekannter Nachrichtentyp", "tool": "Werkzeugnutzung", "use_mcp_server": "MCP-Server-<PERSON><PERSON><PERSON>", "followup": "Nachfrage", "user": "Nutzerfeedback"}, "clickToScroll": "{{messageType}} (#{{messageNumber}}) an<PERSON>hen"}}, "userFeedback": {"editCancel": "Abbrechen", "send": "Senden", "restoreAndSend": "Wiederherstellen & Senden"}, "ideaSuggestionsBox": {"newHere": "Neu hier?", "suggestionText": "<suggestionButton><PERSON><PERSON> hier</suggestionButton> für eine coole Idee, dann drück auf <sendIcon /> und schau zu, wie ich Magie wirke!", "ideas": {"idea1": "<PERSON><PERSON><PERSON> eine rotierende, le<PERSON><PERSON><PERSON> 3D-<PERSON><PERSON>, die auf Mausbewegungen reagiert. Lass die App im Browser laufen.", "idea2": "<PERSON><PERSON><PERSON> eine Portfolio-Website für einen Python-Softwareentwickler", "idea3": "<PERSON><PERSON><PERSON> ein Finanz-<PERSON><PERSON>-<PERSON>ckup im Browser. <PERSON><PERSON> teste, ob es funktioniert", "idea4": "<PERSON><PERSON><PERSON> eine Verzeichnis-Website mit den aktuell besten AI-Videogenerierungsmodellen", "idea5": "<PERSON><PERSON><PERSON> einen CSS-Gradienten-Generator, der benutzerdefinierte Stylesheets exportiert und eine Live-Vorschau zeigt", "idea6": "<PERSON><PERSON><PERSON>, die beim Hovern schön dynamischen Inhalt offenbaren", "idea7": "<PERSON><PERSON><PERSON> ein beruhigendes interaktives Sternfeld, das sich mit Mausgesten bewegt"}}}