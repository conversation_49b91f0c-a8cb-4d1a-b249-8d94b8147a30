name: Playwright E2E Tests

on:
    pull_request:
        types: [opened, reopened, ready_for_review, synchronize]
        branches: [main]
    push:
        branches: [main]
    workflow_dispatch:

# Cancel in-progress jobs when new workflow is triggered
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

env:
    DOCKER_BUILDKIT: 1
    COMPOSE_DOCKER_CLI_BUILD: 1
    NODE_VERSION: 20.19.2
    PNPM_VERSION: 10.8.1

jobs:
    playwright-e2e:
        runs-on: ubuntu-latest
        timeout-minutes: 30

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}

            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Type check Playwright E2E
              run: |
                  cd apps/playwright-e2e
                  pnpm check-types

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Cache Docker layers
              uses: actions/cache@v4
              with:
                  path: /tmp/.buildx-cache
                  key: ${{ runner.os }}-buildx-${{ hashFiles('apps/playwright-e2e/Dockerfile.playwright-ci') }}
                  restore-keys: |
                      ${{ runner.os }}-buildx-

            - name: Run Playwright E2E tests
              continue-on-error: true
              run: |
                  cd apps/playwright-e2e
                  node run-docker-playwright.js
              env:
                  OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
                  WORKSPACE_ROOT: ${{ github.workspace }}

            # - name: Comment PR with test results
            #   if: always() && github.event_name == 'pull_request'
            #   uses: actions/github-script@v7
            #   with:
            #       script: |
            #           const { commentPlaywrightResults } = require('./.github/scripts/comment-playwright-results.js');
            #           await commentPlaywrightResults(github, context);
