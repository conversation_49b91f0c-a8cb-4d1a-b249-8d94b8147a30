{"greeting": "<PERSON>n ch<PERSON>o, tôi là Kilo Code!", "introduction": "<strong><PERSON>lo <PERSON> là công cụ lập trình tự động hàng đầu.</strong> Hãy sẵn sàng để thiết kế kiến trúc, viế<PERSON> mã, gỡ lỗi và tăng năng suất của bạn như chưa từng có trước đây. <PERSON><PERSON> tiế<PERSON> tụ<PERSON>, <PERSON><PERSON> yêu cầu một khóa API.", "notice": "<PERSON><PERSON> bắt đầu, ti<PERSON><PERSON> ích mở rộng này cần một nhà cung cấp API.", "start": "<PERSON><PERSON>t đầu thôi!", "routers": {"requesty": {"description": "<PERSON><PERSON> định tuyến LLM đư<PERSON>c tối ưu hóa của bạn", "incentive": "$1 tín dụng miễn phí"}, "openrouter": {"description": "<PERSON><PERSON><PERSON> <PERSON> thống nhất cho các LLM"}}, "chooseProvider": "<PERSON><PERSON> thực hiện phép màu củ<PERSON> mình, <PERSON><PERSON> cần một khóa API.", "startRouter": "<PERSON><PERSON><PERSON> tôi khuyên bạn nên sử dụng bộ định tuyến LLM:", "startCustom": "Hoặc bạn có thể sử dụng khóa API của riêng mình:", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>i thi<PERSON><PERSON>", "anonymousTelemetry": "Gửi dữ liệu lỗi và sử dụng để gi<PERSON>p chúng tôi sửa lỗi và cải thiện tiện ích mở rộng. Không bao giờ gửi mã, prompts hoặc thông tin cá nhân.", "changeSettings": "Bạn luôn có thể thay đổi điều này ở cuối phần <settingsLink>cài đặt</settingsLink>", "settings": "cài đặt", "allow": "<PERSON> phép", "deny": "<PERSON><PERSON> chối"}, "importSettings": "<PERSON><PERSON><PERSON><PERSON> cài đặt"}