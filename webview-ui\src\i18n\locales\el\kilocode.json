{"welcome": {"greeting": "Καλώς ήρθες στο Kilo Code!", "introText1": "Το <PERSON>lo Code είναι ένας δωρεάν, ανο<PERSON><PERSON><PERSON><PERSON><PERSON> κώδικα AI coding agent.", "introText2": "Λειτουργ<PERSON>ί με τα πιο πρόσφατα μοντέλα AI όπως Claude 4 <PERSON><PERSON>, Gemini 2.5 Pro, GPT-4.1, κα<PERSON> <PERSON>λλα 450+.", "introText3": "Δημιούργη<PERSON><PERSON> έναν δωρεάν λογαριασμό και πάρε $20 σε tokens για χρήση με οποιοδήποτε μοντέλο AI.", "ctaButton": "Δημιουργ<PERSON>α δωρεάν λογαριασμού", "manualModeButton": "Χρησιμοποίησε το δικό σου API key", "alreadySignedUp": "Έχεις ήδη εγγραφεί;", "loginText": "Σύνδεση εδώ"}, "lowCreditWarning": {"addCredit": "Προσθήκη Credit", "lowBalance": "Το υπόλοιπ<PERSON> σου είναι χαμηλό"}, "notifications": {"toolRequest": "Αίτημα εργαλείου σε αναμονή για έγκριση", "browserAction": "Ενέργεια browser σε αναμονή για έγκριση", "command": "Εντολή σε αναμονή για έγκριση"}, "settings": {"sections": {"mcp": "MCP Servers"}, "provider": {"account": "Λογα<PERSON><PERSON>α<PERSON><PERSON><PERSON><PERSON>", "apiKey": "Kilo Code API Key", "login": "Σύνδεση στο Kilo Code", "logout": "Αποσύνδεση από το Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Επιτρέπει ανάγνωση πολύ μεγάλων αρχείων", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> Code θα εκτελεί αναγνώσεις πολύ μεγάλων αρχείων ή εξόδων MCP ακόμη και αν υπάρχει υψηλή πιθανότητα υπερχείλισης του παραθύρου πλαισίου (μέγεθος περιεχομένου >80% του παραθύρου πλαισίου)."}}, "systemNotifications": {"label": "Ενεργοποίηση Ειδοποιήσεων Συστήματος", "description": "Όταν είναι ενεργοποιημένο, το <PERSON><PERSON> θα στέλνει ειδοποιήσεις συστήματος για σημαντικά γεγονότα όπως ολοκλήρωση εργασιών ή σφάλματα.", "testButton": "Δοκιμή Ειδοποίησης", "testTitle": "Kilo Code", "testMessage": "Αυτή είναι μια δοκιμαστική ειδοποίηση από το Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Τ<PERSON> <PERSON>lo Code θέλει να συμπυκνώσει τη συνομιλία σου", "condenseConversation": "Συμπύκνωση Συνομιλίας"}}, "newTaskPreview": {"task": "Εργασία"}, "profile": {"title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "dashboard": "Πίνα<PERSON><PERSON><PERSON>λ<PERSON>γχου", "logOut": "Αποσύνδεση", "currentBalance": "ΤΡΕΧΟΝ ΥΠΟΛΟΙΠΟ", "loading": "Φόρτωση..."}, "docs": "Τεκμηρίωση", "rules": {"tooltip": "Διαχείριση Kilo Code Rules και Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "Rules", "workflows": "Workflows"}, "description": {"rules": "Τα Rules σου επιτρέπουν να δώσεις στο Kilo Code οδηγίες που πρέπει να ακολουθεί σε όλες τις λειτουργίες και για όλα τα prompts. <PERSON><PERSON><PERSON><PERSON><PERSON> ένας μόνιμος τρόπος να συμπεριλάβεις πλαίσιο και προτιμήσεις για όλες τις συνομιλίες στο workspace σου ή παγκοσμίως.", "workflows": "Τα Workflows είναι ένα προετοιμασμένο πρότυπο για μια συνομιλία. Τα Workflows μπορούν να σου επιτρέψουν να ορίσεις prompts που χρησιμοποιείς συχνά, και μπορούν να περιλαμβάνουν μια σειρά βημάτων για να καθοδηγήσουν το Kilo Code μέσα από επαναλαμβανόμενες εργασίες, όπως η ανάπτυξη μιας υπηρεσίας ή η υποβολή ενός PR. Για να καλέσεις ένα workflow, πληκτρολόγησε", "workflowsInChat": "στη συνομιλία."}, "sections": {"globalRules": "Καθολικά Rules", "workspaceRules": "Workspace Rules", "globalWorkflows": "Καθολικά Workflows", "workspaceWorkflows": "Workspace Workflows"}, "validation": {"invalidFileExtension": "Επιτρέπονται μόνο .md, .txt ή χωρίς επέκταση αρχείου"}, "placeholders": {"workflowName": "workflow-name (.md, .txt ή χωρίς επέκταση)", "ruleName": "rule-name (.md, .txt ή χωρίς επέκταση)"}, "newFile": {"newWorkflowFile": "Νέο αρχείο workflow...", "newRuleFile": "Νέο αρχείο rule..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Προβολή {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "εκκίνηση browser", "browser_action_result": "αποτέλεσμα ενέργειας browser", "browser_action": "ενέργεια browser", "checkpoint_saved": "αποθηκευμένο checkpoint", "command": "εκτέλεση εντολής", "command_output": "<PERSON>ξ<PERSON><PERSON><PERSON> εντολής", "completion_result": "αποτέλεσμα ολοκλήρωσης", "condense_context": "συμπύκνωση πλαισίου", "error": "μήνυμα σφάλματος", "followup": "ερώτηση παρακολούθησης", "mcp_server_response": "απόκριση MCP server", "reasoning": "συλλογισμός AI", "text": "απόκριση AI", "tool": "χρήση εργαλείου", "unknown": "άγνωστος τύπος μηνύματος", "use_mcp_server": "χρήση MCP server", "user": "σχόλια χρηστών"}}}, "userFeedback": {"editCancel": "Ακύρωση", "send": "Αποστολή", "restoreAndSend": "Επαναφορά & Αποστολή"}, "ideaSuggestionsBox": {"newHere": "<PERSON><PERSON><PERSON> εδ<PERSON>;", "suggestionText": "<suggestionButton>Κάντε κλικ εδώ</suggestionB<PERSON>on> για μια υπέροχη ιδέα, στη συνέχεια πατήστε <sendIcon /> και παρακολουθήστε με να κάνω μαγικά!", "ideas": {"idea1": "Φτιάξτε μια περιστρεφόμενη, λα<PERSON><PERSON><PERSON><PERSON><PERSON> 3D σφαίρα που αντιδρά στις κινήσεις του ποντικιού. Κάντε την εφαρμογή να λειτουργεί στον περιηγητή.", "idea2": "Δημιουργή<PERSON>τ<PERSON> έναν ιστότοπο χαρτοφυλακίου για έναν προγραμματιστή λογισμικού Python", "idea3": "Δημιουργήστε ένα mockup χρηματοοικονομικής εφαρμογής στον περιηγητή. Στη συνέχεια δοκιμάστε αν λειτουργεί", "idea4": "Δημιουργή<PERSON>τε έναν ιστότοπο καταλόγου που περιέχει τα καλύτερα μοντέλα παραγωγής βίντεο AI αυτή τη στιγμή", "idea5": "Δημιουργήστε έναν γεννήτορα CSS gradients που εξάγει προσαρμοσμένα stylesheets και δείχνει μια ζωντανή προεπισκόπηση", "idea6": "Δημιουρ<PERSON>ή<PERSON><PERSON><PERSON> κάρτες που αποκαλύπτουν όμορφα δυναμικό περιεχόμενο όταν τοποθετείτε το ποντίκι πάνω τους", "idea7": "Δημιουργήστε ένα ήρεμο διαδραστικό αστρικό πεδίο που κινείται με χειρονομίες του ποντικιού"}}}