{"answers": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Xóa", "keep": "Giữ"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>g tôi rất muốn nghe phản hồi của bạn hoặc giúp bạn với bất kỳ vấn đề nào bạn đang gặp phải.", "githubIssues": "<PERSON><PERSON><PERSON> cáo vấn đề trên <PERSON>", "githubDiscussions": "<PERSON>ham gia thảo luận trê<PERSON>", "discord": "Tham gia cộng đồng Discord của chúng tôi", "customerSupport": "Hỗ trợ khách hàng"}, "ui": {"search_placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "mermaid": {"loading": "<PERSON><PERSON> tạo biểu đồ mermaid...", "render_error": "<PERSON><PERSON><PERSON><PERSON> thể hiển thị biểu đồ", "fixing_syntax": "<PERSON><PERSON> sửa cú pháp Mermaid...", "fix_syntax_button": "Sửa cú pháp bằng AI", "original_code": "Mã gốc:", "errors": {"unknown_syntax": "Lỗi cú pháp không xác định", "fix_timeout": "<PERSON><PERSON><PERSON> c<PERSON>u sửa LLM đã hết thời gian", "fix_failed": "Sửa LLM thất bại", "fix_attempts": "<PERSON><PERSON><PERSON><PERSON> thể sửa cú pháp sau {{attempts}} lần thử. Lỗi cuối cùng: {{error}}", "no_fix_provided": "LLM không cung cấ<PERSON> đ<PERSON><PERSON><PERSON> bản sửa", "fix_request_failed": "<PERSON><PERSON><PERSON> c<PERSON>u sửa thất bại"}, "buttons": {"zoom": "<PERSON><PERSON> ph<PERSON>g", "zoomIn": "<PERSON><PERSON><PERSON> to", "zoomOut": "<PERSON>hu nhỏ", "copy": "Sao chép", "save": "<PERSON><PERSON><PERSON>", "viewCode": "<PERSON><PERSON> mã", "viewDiagram": "<PERSON><PERSON> bi<PERSON> đ<PERSON>", "close": "Đ<PERSON><PERSON>"}, "modal": {"codeTitle": "Mã Mermaid"}, "tabs": {"diagram": "<PERSON><PERSON><PERSON><PERSON> đồ", "code": "Mã"}, "feedback": {"imageCopied": "<PERSON><PERSON>nh ảnh đã đư<PERSON>c sao chép vào clipboard", "copyError": "Lỗi sao chép hình <PERSON>nh"}}, "file": {"errors": {"invalidDataUri": "<PERSON><PERSON><PERSON> dạng URI dữ liệu không hợp lệ", "copyingImage": "Lỗi sao chép hình ảnh: {{error}}", "openingImage": "Lỗi mở hình ảnh: {{error}}", "pathNotExists": "Đường dẫn không tồn tại: {{path}}", "couldNotOpen": "<PERSON><PERSON><PERSON><PERSON> thể mở tệp: {{error}}", "couldNotOpenGeneric": "<PERSON>h<PERSON>ng thể mở tệp!"}, "success": {"imageDataUriCopied": "URI dữ liệu hình ảnh đã đư<PERSON>c sao chép vào clipboard"}}}