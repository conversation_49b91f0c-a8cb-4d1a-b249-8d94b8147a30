{"greeting": "Selamat datang di Kilo Code", "task": {"title": "Tugas", "seeMore": "<PERSON><PERSON> lebih banyak", "seeLess": "<PERSON><PERSON> lebih sedikit", "tokens": "Token:", "cache": "Cache:", "apiCost": "Biaya API:", "condenseContext": "Kondensasi konteks secara cerdas", "contextWindow": "Panjang Konteks:", "closeAndStart": "Tutup tugas dan mulai yang baru", "export": "Ekspor riwayat tugas", "share": "Bagikan tugas", "delete": "<PERSON><PERSON> (Shift + Klik untuk lewati konfirmasi)", "shareWithOrganization": "Bagikan dengan organisasi", "shareWithOrganizationDescription": "Hanya anggota organisasi Anda yang dapat mengakses", "sharePublicly": "Bagikan secara publik", "sharePubliclyDescription": "Siapa pun dengan tautan dapat mengakses", "connectToCloud": "Hubungkan ke Cloud", "connectToCloudDescription": "Masuk ke Kilo Code Cloud untuk berbagi tugas", "sharingDisabledByOrganization": "Berbagi dinonaktifkan oleh organisasi", "shareSuccessOrganization": "Tautan organisasi disalin ke clipboard", "shareSuccessPublic": "Tautan publik disalin ke clipboard"}, "history": {"title": "Riwayat"}, "unpin": "Lepas Pin", "pin": "<PERSON>n", "retry": {"title": "<PERSON><PERSON>", "tooltip": "Coba operasi lagi"}, "startNewTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> tug<PERSON> baru"}, "reportBug": {"title": "Laporkan Bug"}, "proceedAnyways": {"title": "Lanju<PERSON><PERSON>", "tooltip": "Lanjutkan saat perintah di<PERSON>an"}, "save": {"title": "Simpan", "tooltip": "<PERSON><PERSON><PERSON> file"}, "tokenProgress": {"availableSpace": "<PERSON>uang tersedia: {{amount}} token", "tokensUsed": "Token digunakan: {{used}} dari {{total}}", "reservedForResponse": "Dicadangkan untuk respons model: {{amount}} token"}, "reject": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> aksi ini"}, "completeSubtaskAndReturn": "Selesaikan Subtugas dan <PERSON>", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Setujui aksi ini"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "deny": {"title": "<PERSON><PERSON>"}}, "runCommand": {"title": "Jalankan <PERSON>ah", "tooltip": "Eks<PERSON><PERSON><PERSON> perintah ini"}, "proceedWhileRunning": {"title": "Lanjutkan Saat Berjalan", "tooltip": "Lanjutkan meskipun ada peringatan"}, "killCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Hentikan perintah saat ini"}, "resumeTask": {"title": "<PERSON>n<PERSON><PERSON><PERSON>", "tooltip": "Lanjutkan tugas saat ini"}, "terminate": {"title": "Hentikan", "tooltip": "<PERSON><PERSON><PERSON> tugas saat ini"}, "cancel": {"title": "<PERSON><PERSON>", "tooltip": "Batalkan operasi saat ini"}, "scrollToBottom": "<PERSON><PERSON><PERSON> ke bawah chat", "about": "<PERSON><PERSON><PERSON>, refa<PERSON><PERSON>, dan debug kode dengan bantuan AI.<br />Lihat <DocsLink>dokumentasi</DocsLink> kami untuk mempelajari lebih lanjut.", "onboarding": "Daftar tugas di workspace ini kosong.", "rooTips": {"boomerangTasks": {"title": "Orkestrasi Tugas", "description": "Bagi tugas menjadi bagian-bagian kecil yang dapat dikelola"}, "stickyModels": {"title": "Model Sticky", "description": "Setiap mode mengingat model te<PERSON><PERSON> yang kamu gunakan"}, "tools": {"title": "Tools", "description": "Izinkan AI menyelesaikan masalah dengan browsing web, men<PERSON><PERSON><PERSON> perintah, dan la<PERSON><PERSON>"}, "customizableModes": {"title": "Mode yang Dapat Disesuaikan", "description": "<PERSON>a k<PERSON>us dengan perilaku dan model yang ditu<PERSON> sendiri"}}, "selectMode": "Pilih mode untuk interaksi", "selectApiConfig": "Pilih konfigurasi API", "selectModelConfig": "<PERSON><PERSON><PERSON> model", "enhancePrompt": "Tingkatkan prompt dengan konteks tambahan", "enhancePromptDescription": "Tombol 'Tingkatkan Prompt' membantu memperbaiki prompt kamu dengan member<PERSON>n konteks tambahan, k<PERSON><PERSON><PERSON><PERSON>, atau pen<PERSON><PERSON>an ulang. Coba ketik prompt di sini dan klik tombol lagi untuk melihat cara kerjanya.", "modeSelector": {"title": "Mode", "marketplace": "Marketplace Mode", "settings": "Pengaturan Mode", "description": "Persona khusus yang menyesuaikan perilaku Kilo Code."}, "addImages": "Tambahkan gambar ke pesan", "sendMessage": "<PERSON><PERSON>", "stopTts": "Hentikan text-to-speech", "typeMessage": "<PERSON><PERSON><PERSON> pesan...", "typeTask": "<PERSON><PERSON>, cari, tanya sesuatu", "addContext": "@ untuk menambah konteks, / untuk ganti mode", "dragFiles": "<PERSON>han shift untuk drag file", "dragFilesImages": "tahan shift untuk drag file/gambar", "errorReadingFile": "Error membaca file:", "noValidImages": "Tidak ada gambar valid yang diproses", "separator": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Edit...", "forNextMode": "untuk mode selanjutnya", "apiRequest": {"title": "Permintaan API", "failed": "Permintaan API Gagal", "streaming": "Permintaan API...", "cancelled": "Permintaan API Dibatalkan", "streamingFailed": "Streaming API Gagal"}, "checkpoint": {"initial": "Checkpoint Awal", "regular": "Checkpoint", "initializingWarning": "<PERSON><PERSON><PERSON> men<PERSON> checkpoint... <PERSON><PERSON> ini terlalu lama, kamu bisa menonaktifkan checkpoint di <settingsLink>pengaturan</settingsLink> dan restart tugas.", "menu": {"viewDiff": "<PERSON><PERSON>", "restore": "Pulihkan Checkpoint", "restoreFiles": "Pulihkan File", "restoreFilesDescription": "Mengembalikan file proyek kamu ke snapshot yang diambil pada titik ini.", "restoreFilesAndTask": "Pulihkan File & Tugas", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "cannotUndo": "Aksi ini tidak dapat di<PERSON>alk<PERSON>.", "restoreFilesAndTaskDescription": "Mengembalikan file proyek kamu ke snapshot yang diambil pada titik ini dan menghapus semua pesan setelah titik ini."}, "current": "Saat Ini"}, "contextCondense": {"title": "Konteks Dikondensasi", "condensing": "Mengondensasi konteks...", "errorHeader": "Gagal mengondensasi konteks", "tokens": "token"}, "instructions": {"wantsToFetch": "Kilo Code ingin mengambil instruksi detail untuk membantu tugas saat ini"}, "fileOperations": {"wantsToRead": "Kilo Code ingin membaca file ini:", "wantsToReadMultiple": "Kilo Code ingin membaca beberapa file:", "wantsToReadAndXMore": "Kilo Code ingin membaca file ini dan {{count}} la<PERSON><PERSON>:", "wantsToReadOutsideWorkspace": "Kilo Code ingin membaca file ini di luar workspace:", "didRead": "Kilo Code membaca file ini:", "wantsToEdit": "Kilo Code ingin mengedit file ini:", "wantsToEditOutsideWorkspace": "Kilo Code ingin mengedit file ini di luar workspace:", "wantsToEditProtected": "Kilo Code ingin mengedit file konfigurasi yang dilindungi:", "wantsToApplyBatchChanges": "Kilo Code ingin menerapkan perubahan ke beberapa file:", "wantsToCreate": "Kilo Code ingin membuat file baru:", "wantsToSearchReplace": "Kilo Code ingin mencari dan mengganti di file ini:", "didSearchReplace": "Kilo Code melakukan pencarian dan penggantian pada file ini:", "wantsToInsert": "Kilo Code ingin menyisipkan konten ke file ini:", "wantsToInsertWithLineNumber": "Kilo Code ingin menyisipkan konten ke file ini di baris {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code ingin menambahkan konten ke akhir file ini:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code ingin melihat file tingkat atas di direktori ini:", "didViewTopLevel": "Kilo Code melihat file tingkat atas di direktori ini:", "wantsToViewRecursive": "Kilo Code ingin melihat semua file secara rekursif di direktori ini:", "didViewRecursive": "Kilo Code melihat semua file secara rekursif di direktori ini:", "wantsToViewDefinitions": "Kilo Code ingin melihat nama definisi source code yang digunakan di direktori ini:", "didViewDefinitions": "Kilo Code melihat nama definisi source code yang digunakan di direktori ini:", "wantsToSearch": "Kilo Code ingin mencari direktori ini untuk <code>{{regex}}</code>:", "didSearch": "Kilo Code mencari direktori ini untuk <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code ingin mencari direktori ini (di luar workspace) untuk <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code mencari direktori ini (di luar workspace) untuk <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code ingin melihat file tingkat atas di direktori ini (di luar workspace):", "didViewTopLevelOutsideWorkspace": "Kilo Code melihat file tingkat atas di direktori ini (di luar workspace):", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code ingin melihat semua file secara rekursif di direktori ini (di luar workspace):", "didViewRecursiveOutsideWorkspace": "Kilo Code melihat semua file secara rekursif di direktori ini (di luar workspace):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code ingin melihat nama definisi source code yang digunakan di direktori ini (di luar workspace):", "didViewDefinitionsOutsideWorkspace": "Kilo Code melihat nama definisi source code yang digunakan di direktori ini (di luar workspace):"}, "codebaseSearch": {"wantsToSearch": "Kilo Code ingin mencari codebase untuk <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code ingin mencari codebase untuk <code>{{query}}</code> di <code>{{path}}</code>:", "didSearch": "Ditem<PERSON>n {{count}} hasil untuk <code>{{query}}</code>:", "resultTooltip": "Skor kemiripan: {{score}} (klik untuk membuka file)"}, "commandOutput": "Output Perintah", "response": "Respons", "arguments": "Argumen", "mcp": {"wantsToUseTool": "Kilo Code ingin menggunakan tool di server MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code ingin mengakses resource di server MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code ingin beralih ke mode {{mode}}", "wantsToSwitchWithReason": "Kilo Code ingin beralih ke mode {{mode}} karena: {{reason}}", "didSwitch": "Kilo Code beralih ke mode {{mode}}", "didSwitchWithReason": "Kilo Code beralih ke mode {{mode}} karena: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code ingin membuat subtugas baru dalam mode {{mode}}:", "wantsToFinish": "Kilo Code ingin menyelesaikan subtugas ini", "newTaskContent": "Instruksi Subtugas", "completionContent": "Subtugas Selesai", "resultContent": "<PERSON><PERSON>", "defaultResult": "<PERSON><PERSON><PERSON> lan<PERSON>kan ke tugas berikutnya.", "completionInstructions": "Subtugas selesai! Kamu bisa meninjau hasilnya dan menyarankan koreksi atau langkah selanjutnya. Jika semuanya terlihat baik, konfirmasi untuk mengembalikan hasil ke tugas induk."}, "questions": {"hasQuestion": "Kilo Code punya pertanyaan:"}, "taskCompleted": "<PERSON><PERSON>", "error": "Error", "diffError": {"title": "<PERSON> <PERSON><PERSON><PERSON>"}, "troubleMessage": "Kilo Code mengalami masalah...", "powershell": {"issues": "Sepertinya kamu mengalami masalah Windows PowerShell, silakan lihat ini"}, "autoApprove": {"title": "Auto-approve:", "none": "Tidak Ada", "description": "Auto-approve memungkinkan Kilo Code melakukan aksi tanpa meminta izin. Hanya aktifkan untuk aksi yang benar-benar kamu percayai. Konfigurasi lebih detail tersedia di <settingsLink>Pengaturan</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} Dirilis", "description": "Roo Code {{version}} menghadirkan fitur-fitur baru yang kuat dan peningkatan signifikan untuk meningkatkan alur kerja pengembangan Anda.", "whatsNew": "<PERSON>", "feature1": "<bold>Pengindeksan Codebase Lulus dari Eksperimental</bold>: Pengindeksan codebase lengkap kini stabil dan siap untuk penggunaan produksi dengan pencarian yang ditingkatkan dan pemahaman konteks.", "feature2": "<bold><PERSON><PERSON></bold>: <PERSON><PERSON> tugas Anda tetap pada jalur dengan manajemen todo terintegrasi yang membantu Anda tetap terorganisir dan fokus pada tujuan pengembangan.", "feature3": "<bold>Transisi Arsitektur ke Kode yang Ditingkatkan</bold>: Transfer yang mulus dari perencanaan di mode Arsitektur ke implementasi di mode Kode.", "hideButton": "Sembunyikan pen<PERSON>", "detailsDiscussLinks": "Dapatkan detail lebih lanjut dan bergabung dalam diskusi di <discordLink>Discord</discordLink> dan <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}}d"}, "followUpSuggest": {"copyToInput": "Salin ke input (sama dengan shift + klik)", "autoSelectCountdown": "<PERSON><PERSON><PERSON><PERSON> otomati<PERSON> da<PERSON> {{count}}dtk", "countdownDisplay": "{{count}}dtk"}, "browser": {"rooWantsToUse": "Kilo Code ingin menggunakan browser:", "consoleLogs": "<PERSON><PERSON>", "noNewLogs": "(Tidak ada log baru)", "screenshot": "Screenshot browser", "cursor": "kursor", "navigation": {"step": "Langkah {{current}} dari {{total}}", "previous": "Sebelumnya", "next": "Selanjutnya"}, "sessionStarted": "<PERSON><PERSON>", "actions": {"title": "<PERSON><PERSON><PERSON>: ", "launch": "Luncurkan browser di {{url}}", "click": "<PERSON>lik ({{coordinate}})", "type": "<PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON> ke bawah", "scrollUp": "<PERSON><PERSON><PERSON> ke atas", "close": "Tutup browser"}}, "codeblock": {"tooltips": {"expand": "<PERSON><PERSON><PERSON> blok kode", "collapse": "Tutup blok kode", "enable_wrap": "Aktifkan word wrap", "disable_wrap": "Nonaktifkan word wrap", "copy_code": "<PERSON><PERSON> kode"}}, "systemPromptWarning": "PERINGATAN: Override system prompt kustom aktif. Ini dapat merusak fungsionalitas secara serius dan menyebabkan perilaku yang tidak terduga.", "profileViolationWarning": "Profil saat ini melanggar pengaturan organisasi kamu", "shellIntegration": {"title": "Peringatan Eksekus<PERSON>", "description": "<PERSON>intah kamu dijalankan tanpa integrasi shell terminal VSCode. Untuk menekan peringatan ini kamu bisa menonaktifkan integrasi shell di bagian <strong>Terminal</strong> dari <settingsLink>pengaturan Kilo Code</settingsLink> atau troubleshoot integrasi terminal VSCode menggunakan link di bawah.", "troubleshooting": "Klik di sini untuk dokumentasi integrasi shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Batas <PERSON> yang <PERSON> Otomatis Terca<PERSON>i", "description": "Kilo Code telah mencapai batas {{count}} permintaan API yang disetujui otomatis. Apakah kamu ingin mengatur ulang hitungan dan melanjutkan tugas?", "button": "Atur Ulang dan <PERSON>"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> siap", "indexing": "Mengindeks {{percentage}}%", "indexed": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON>rror in<PERSON>s", "status": "Status indeks"}, "versionIndicator": {"ariaLabel": "Versi {{version}} - Klik untuk melihat catatan rilis"}}