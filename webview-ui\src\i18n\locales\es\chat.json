{"greeting": "¿Qué puede hacer <PERSON>lo <PERSON> por ti?", "task": {"title": "Tarea", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Costo de API:", "contextWindow": "Longitud del contexto:", "closeAndStart": "Cerrar tarea e iniciar una nueva", "export": "Exportar historial de tareas", "delete": "Eliminar tarea (Shift + Clic para omitir confirmación)", "condenseContext": "Condensar contexto de forma inteligente", "share": "Compartir tarea", "shareWithOrganization": "Compartir con organización", "shareWithOrganizationDescription": "Solo los miembros de tu organización pueden acceder", "sharePublicly": "Compartir públicamente", "sharePubliclyDescription": "Cualquiera con el enlace puede acceder", "connectToCloud": "Conectar al Cloud", "connectToCloudDescription": "Inicia sesión en Kilo Code Cloud para compartir tareas", "sharingDisabledByOrganization": "Compartir deshabilitado por la organización", "shareSuccessOrganization": "Enlace de organización copiado al portapapeles", "shareSuccessPublic": "Enlace público copiado al portapapeles"}, "history": {"title": "Historial"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON>", "retry": {"title": "Reintentar", "tooltip": "Intenta la operación de nuevo"}, "startNewTask": {"title": "Iniciar nueva tarea", "tooltip": "Comienza una nueva tarea"}, "reportBug": {"title": "<PERSON><PERSON>"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de todos modos", "tooltip": "Continuar mientras se ejecuta el comando"}, "save": {"title": "Guardar", "tooltip": "Guardar los cambios del archivo"}, "tokenProgress": {"availableSpace": "Espacio disponible: {{amount}} tokens", "tokensUsed": "Tokens utilizados: {{used}} de {{total}}", "reservedForResponse": "Reservado para respuesta del modelo: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Re<PERSON>zar esta acción"}, "completeSubtaskAndReturn": "Completar subtarea y regresar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprobar esta acción"}, "runCommand": {"title": "Ejecutar comando", "tooltip": "Ejecutar este comando"}, "proceedWhileRunning": {"title": "Continuar mientras se ejecuta", "tooltip": "Continuar a pesar de las advertencias"}, "killCommand": {"title": "Terminar comando", "tooltip": "Terminar el comando actual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> la tarea actual"}, "terminate": {"title": "Terminar", "tooltip": "Terminar la tarea actual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar la operación actual"}, "scrollToBottom": "Desplazarse al final del chat", "about": "Genera, refactoriza y depura código con asistencia de IA. Consulta nuestra <DocsLink>documentación</DocsLink> para obtener más información.", "onboarding": "<strong>Tu lista de tareas en este espacio de trabajo está vacía.</strong> Comienza escribiendo una tarea abajo. ¿No estás seguro cómo empezar? Lee más sobre lo que Kilo Code puede hacer por ti en <DocsLink>la documentación</DocsLink>.", "rooTips": {"boomerangTasks": {"title": "Orquestación de Tareas", "description": "Divide las tareas en partes más pequeñas y manejables."}, "stickyModels": {"title": "Modos persistentes", "description": "Cada modo recuerda tu último modelo utilizado"}, "tools": {"title": "Herramientas", "description": "Permite que la IA resuelva problemas navegando por la web, ejecutando comandos y mucho más."}, "customizableModes": {"title": "Modos personalizables", "description": "Personalidades especializadas con sus propios comportamientos y modelos asignados"}}, "selectMode": "Seleccionar modo de interacción", "selectApiConfig": "Seleccionar configuración de API", "selectModelConfig": "Seleccionar modelo", "enhancePrompt": "Mejorar el mensaje con contexto adicional", "addImages": "Agregar imágenes al mensaje", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "stopTts": "Detener texto a voz", "typeMessage": "Escribe un mensaje...", "typeTask": "Construir, buscar, preguntar algo", "addContext": "@ para agregar contexto, / para cambiar modos", "dragFiles": "mantén shift para arrastrar archivos", "dragFilesImages": "mantén shift para arrastrar archivos/imágenes", "enhancePromptDescription": "El botón 'Mejorar el mensaje' ayuda a mejorar tu petición proporcionando contexto adicional, aclaraciones o reformulaciones. Intenta escribir una petición aquí y haz clic en el botón nuevamente para ver cómo funciona.", "modeSelector": {"title": "Modos", "marketplace": "Marketplace de Modos", "settings": "Configuración de Modos", "description": "Personalidades especializadas que adaptan el comportamiento de Kilo Code."}, "errorReadingFile": "Error al leer el archivo:", "noValidImages": "No se procesaron imágenes válidas", "separator": "Separador", "edit": "Editar...", "forNextMode": "para el siguiente modo", "error": "Error", "diffError": {"title": "Edición fallida"}, "troubleMessage": "Kilo Code está teniendo problemas...", "apiRequest": {"title": "Solicitud API", "failed": "Solicitud API falló", "streaming": "Solicitud API...", "cancelled": "Solicitud API cancelada", "streamingFailed": "Transmisión API falló"}, "checkpoint": {"initial": "Punto de control inicial", "regular": "Punto de control", "initializingWarning": "Todavía inicializando el punto de control... Si esto tarda demasiado, puedes desactivar los puntos de control en la <settingsLink>configuración</settingsLink> y reiniciar tu tarea.", "menu": {"viewDiff": "Ver diferencias", "restore": "Restaurar punto de control", "restoreFiles": "Restaurar archivos", "restoreFilesDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto.", "restoreFilesAndTask": "Restaurar archivos y tarea", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta acción no se puede deshacer.", "restoreFilesAndTaskDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto y elimina todos los mensajes posteriores a este punto."}, "current": "Actual"}, "instructions": {"wantsToFetch": "Kilo Code quiere obtener instrucciones detalladas para ayudar con la tarea actual"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> quiere leer este archivo:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON> quiere leer este archivo fuera del espacio de trabajo:", "didRead": "Kilo Code leyó este archivo:", "wantsToEdit": "Kilo <PERSON> quiere editar este archivo:", "wantsToEditOutsideWorkspace": "Kilo <PERSON> quiere editar este archivo fuera del espacio de trabajo:", "wantsToEditProtected": "Kilo Code quiere editar un archivo de configuración protegido:", "wantsToCreate": "<PERSON><PERSON> Code quiere crear un nuevo archivo:", "wantsToSearchReplace": "Kilo Code quiere realizar búsqueda y reemplazo en este archivo:", "didSearchReplace": "Kilo Code realizó búsqueda y reemplazo en este archivo:", "wantsToInsert": "<PERSON>lo <PERSON> quiere insertar contenido en este archivo:", "wantsToInsertWithLineNumber": "<PERSON>lo Code quiere insertar contenido en este archivo en la línea {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo <PERSON> quiere añadir contenido al final de este archivo:", "wantsToReadAndXMore": "<PERSON><PERSON> Code quiere leer este archivo y {{count}} más:", "wantsToReadMultiple": "<PERSON>lo <PERSON> quiere leer varios archivos:", "wantsToApplyBatchChanges": "<PERSON>lo Code quiere aplicar cambios a múltiples archivos:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code quiere ver los archivos de nivel superior en este directorio:", "didViewTopLevel": "Kilo Code vio los archivos de nivel superior en este directorio:", "wantsToViewRecursive": "Kilo <PERSON> quiere ver recursivamente todos los archivos en este directorio:", "didViewRecursive": "Kilo Code vio recursivamente todos los archivos en este directorio:", "wantsToViewDefinitions": "Kilo Code quiere ver nombres de definiciones de código fuente utilizados en este directorio:", "didViewDefinitions": "Kilo Code vio nombres de definiciones de código fuente utilizados en este directorio:", "wantsToSearch": "Kilo Code quiere buscar en este directorio <code>{{regex}}</code>:", "didSearch": "Kilo Code buscó en este directorio <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code quiere buscar en este directorio (fuera del espacio de trabajo) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code buscó en este directorio (fuera del espacio de trabajo) <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo <PERSON> quiere ver los archivos de nivel superior en este directorio (fuera del espacio de trabajo):", "didViewTopLevelOutsideWorkspace": "Kilo Code vio los archivos de nivel superior en este directorio (fuera del espacio de trabajo):", "wantsToViewRecursiveOutsideWorkspace": "Kilo <PERSON> quiere ver recursivamente todos los archivos en este directorio (fuera del espacio de trabajo):", "didViewRecursiveOutsideWorkspace": "Kilo Code vio recursivamente todos los archivos en este directorio (fuera del espacio de trabajo):", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code quiere ver nombres de definiciones de código fuente utilizados en este directorio (fuera del espacio de trabajo):", "didViewDefinitionsOutsideWorkspace": "Kilo Code vio nombres de definiciones de código fuente utilizados en este directorio (fuera del espacio de trabajo):"}, "commandOutput": "Salida del comando", "response": "Respuesta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "<PERSON>lo Code quiere usar una herramienta en el servidor MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code quiere acceder a un recurso en el servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code quiere cambiar a modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "Kilo Code quiere cambiar a modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "Kilo Code cambió a modo <code>{{mode}}</code>", "didSwitchWithReason": "Kilo Code cambió a modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code quiere crear una nueva subtarea en modo <code>{{mode}}</code>:", "wantsToFinish": "Kilo <PERSON> quiere finalizar esta subtarea", "newTaskContent": "Instrucciones de la subtarea", "completionContent": "Subtarea completada", "resultContent": "Resultados de la subtarea", "defaultResult": "Por favor, continúa con la siguiente tarea.", "completionInstructions": "¡Subtarea completada! Puedes revisar los resultados y sugerir correcciones o próximos pasos. Si todo se ve bien, confirma para devolver el resultado a la tarea principal."}, "questions": {"hasQuestion": "Kilo Code tiene una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "powershell": {"issues": "Parece que estás teniendo problemas con Windows PowerShell, por favor consulta esta"}, "autoApprove": {"title": "Auto-aprobar:", "none": "<PERSON><PERSON><PERSON>", "description": "Auto-aprobar permite a Kilo Code realizar acciones sin pedir permiso. Habilita solo para acciones en las que confíes plenamente. Configuración más detallada disponible en <settingsLink>Configuración</settingsLink>."}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "errorHeader": "Error al condensar el contexto", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a la entrada (o Shift + clic)", "autoSelectCountdown": "Selección automática en {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Roo Code {{version}} publicado", "description": "Roo Code {{version}} trae poderosas nuevas funcionalidades y mejoras significativas para mejorar tu flujo de trabajo de desarrollo.", "whatsNew": "Novedades", "feature1": "<bold>Indexación de código base graduada de experimental</bold>: La indexación completa del código base ahora es estable y está lista para uso en producción con búsqueda mejorada y comprensión de contexto.", "feature2": "<bold>Nueva función de lista de tareas</bold>: Mantén tus tareas en el buen camino con gestión integrada de tareas que te ayuda a mantenerte organizado y enfocado en tus objetivos de desarrollo.", "feature3": "<bold>Transiciones mejoradas de Arquitecto a Código</bold>: Transferencias fluidas desde la planificación en modo Arquitecto hasta la implementación en modo Código.", "hideButton": "<PERSON><PERSON><PERSON><PERSON> anuncio", "detailsDiscussLinks": "Obtén más detalles y participa en <discordLink>Discord</discordLink> y <redditLink>Reddit</redditLink> 🚀"}, "browser": {"rooWantsToUse": "Kilo Code quiere usar el navegador:", "consoleLogs": "Registros de la consola", "noNewLogs": "(No hay nuevos registros)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Paso {{current}} de {{total}}", "previous": "Anterior", "next": "Siguient<PERSON>"}, "sessionStarted": "Sesión de navegador iniciada", "actions": {"title": "Acción de navegación: ", "launch": "Iniciar <PERSON> en {{url}}", "click": "Clic ({{coordinate}})", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> hacia arriba", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloque de código", "collapse": "Contraer bloque de código", "enable_wrap": "Activar ajuste de línea", "disable_wrap": "Desactivar ajuste de línea", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "systemPromptWarning": "ADVERTENCIA: Anulación de instrucciones del sistema personalizada activa. Esto puede romper gravemente la funcionalidad y causar un comportamiento impredecible.", "profileViolationWarning": "El perfil actual infringe la configuración de tu organización", "shellIntegration": {"title": "Advertencia de ejecución de comandos", "description": "Tu comando se está ejecutando sin la integración de shell de terminal de VSCode. Para suprimir esta advertencia, puedes desactivar la integración de shell en la sección <strong>Terminal</strong> de la <settingsLink>configuración de Kilo Code</settingsLink> o solucionar problemas de integración de terminal de VSCode usando el enlace de abajo.", "troubleshooting": "Haz clic aquí para ver la documentación de integración de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Límite de Solicitudes Auto-aprobadas Alcanzado", "description": "Kilo Code ha alcanzado el límite auto-aprobado de {{count}} solicitud(es) API. ¿Deseas reiniciar el contador y continuar con la tarea?", "button": "Reiniciar y Continuar"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code quiere buscar en la base de código <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code quiere buscar en la base de código <code>{{query}}</code> en <code>{{path}}</code>:", "didSearch": "Se encontraron {{count}} resultado(s) para <code>{{query}}</code>:", "resultTooltip": "Puntuación de similitud: {{score}} (haz clic para abrir el archivo)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> todo"}, "deny": {"title": "<PERSON><PERSON><PERSON> todo"}}, "indexingStatus": {"ready": "Índice listo", "indexing": "Indexando {{percentage}}%", "indexed": "Indexado", "error": "<PERSON><PERSON><PERSON>", "status": "Estado del índice"}, "versionIndicator": {"ariaLabel": "Versión {{version}} - Haz clic para ver las notas de la versión"}}