name: Changeset Release
run-name: Changeset Release ${{ github.actor != 'kilocode-bot' && '- Create PR' || '- Update Changelog' }}

on:
    workflow_dispatch:
    pull_request:
        types: [closed, opened, labeled]

env:
    REPO_PATH: ${{ github.repository }}
    GIT_REF: ${{ github.event_name == 'pull_request' && github.event.pull_request.head.sha || 'main' }}
    NODE_VERSION: 20.19.2
    PNPM_VERSION: 10.8.1

jobs:
    # Job 1: Create version bump PR when changesets are merged to main
    changeset-pr-version-bump:
        if: >
            ( github.event_name == 'pull_request' &&
            github.event.pull_request.merged == true &&
            github.event.pull_request.base.ref == 'main' &&
            !contains(github.event.pull_request.title, 'Changeset version bump') &&
            github.actor != 'kilocode-bot' ) ||
            github.event_name == 'workflow_dispatch'
        runs-on: ubuntu-latest
        permissions:
            contents: write
            pull-requests: write
        steps:
            - name: Git Checkout
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
                  ref: ${{ env.GIT_REF }}

            - name: Install pnpm
              uses: pnpm/action-setup@v4
              with:
                  version: ${{ env.PNPM_VERSION }}

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: "pnpm"

            - name: Install Dependencies
              run: pnpm install

            # Check if there are any new changesets to process
            - name: Check for changesets
              id: check-changesets
              run: |
                  NEW_CHANGESETS=$(find .changeset -name "*.md" ! -name "README.md" | wc -l | tr -d ' ')
                  echo "Changesets diff with previous version: $NEW_CHANGESETS"
                  echo "new_changesets=$NEW_CHANGESETS" >> $GITHUB_OUTPUT

            # Create version bump PR using changesets/action if there are new changesets
            - name: Changeset Pull Request
              if: steps.check-changesets.outputs.new_changesets != '0'
              id: changesets
              uses: changesets/action@v1
              with:
                  commit: "changeset version bump"
                  title: "Changeset version bump"
                  version: pnpm changeset:version # This performs the changeset version bump
              env:
                  GITHUB_TOKEN: ${{ secrets.CROSS_REPO_ACCESS_TOKEN }}

            # Remove changelog-ready label from changeset PR if present
            - name: Remove changelog-ready label from changeset PR
              if: steps.changesets.outputs.pullRequestNumber
              uses: actions/github-script@v7
              with:
                  github-token: ${{ secrets.CROSS_REPO_ACCESS_TOKEN }}
                  script: |
                      try {
                        await github.rest.issues.removeLabel({
                          owner: context.repo.owner,
                          repo: context.repo.repo,
                          issue_number: ${{ steps.changesets.outputs.pullRequestNumber }},
                          name: 'changelog-ready'
                        });
                        console.log('Removed changelog-ready label from changeset PR');
                      } catch (error) {
                        if (error.status === 404) {
                          console.log('changelog-ready label was not present on changeset PR');
                        } else {
                          throw error;
                        }
                      }

            # Get current and previous versions to edit changelog entry
            - name: Get version
              id: get_version
              run: |
                  VERSION=$(git show HEAD:src/package.json | jq -r '.version')
                  echo "version=$VERSION" >> $GITHUB_OUTPUT
                  PREV_VERSION=$(git show origin/main:src/package.json | jq -r '.version')
                  echo "prev_version=$PREV_VERSION" >> $GITHUB_OUTPUT
                  echo "version=$VERSION"
                  echo "prev_version=$PREV_VERSION"
              # Update CHANGELOG.md with proper format
            - name: Update Changelog Format
              if: ${{ !contains(github.event.pull_request.labels.*.name, 'changelog-ready') }}
              env:
                  VERSION: ${{ steps.get_version.outputs.version }}
                  PREV_VERSION: ${{ steps.get_version.outputs.prev_version }}
              run: |
                  echo "Running changelog formatting script..."
                  echo "VERSION: $VERSION"
                  echo "PREV_VERSION: $PREV_VERSION"
                  echo "Current changelog start:"
                  head -20 CHANGELOG.md
                  echo "===================="
                  python .github/scripts/overwrite_changeset_changelog.py
                  echo "===================="
                  echo "Updated changelog start:"
                  head -20 CHANGELOG.md

            # Commit and push changelog updates
            - name: Push Changelog updates
              if: ${{ !contains(github.event.pull_request.labels.*.name, 'changelog-ready') }}
              run: |
                  git config user.name "kilocode-bot"
                  git config user.email <EMAIL>
                  echo "Running git add and commit..."
                  git add CHANGELOG.md
                  git commit -m "Updating CHANGELOG.md format"
                  git status
                  echo "--------------------------------------------------------------------------------"
                  echo "Pushing to remote..."
                  echo "--------------------------------------------------------------------------------"
                  git push --set-upstream origin HEAD

            # Add label to indicate changelog has been formatted
            - name: Add changelog-ready label
              if: ${{ steps.changesets.outputs.pullRequestNumber && !contains(github.event.pull_request.labels.*.name, 'changelog-ready') }}
              uses: actions/github-script@v7
              with:
                  github-token: ${{ secrets.CROSS_REPO_ACCESS_TOKEN }}
                  script: |
                      await github.rest.issues.addLabels({
                        owner: context.repo.owner,
                        repo: context.repo.repo,
                        issue_number: ${{ steps.changesets.outputs.pullRequestNumber }},
                        labels: ['changelog-ready']
                      });
