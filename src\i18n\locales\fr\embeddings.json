{"unknownError": "<PERSON><PERSON><PERSON> inconnue", "authenticationFailed": "Échec de la création des embeddings : Échec de l'authentification. Veuillez vérifier votre clé API.", "failedWithStatus": "Échec de la création des embeddings après {{attempts}} tentatives : HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Échec de la création des embeddings après {{attempts}} tentatives : {{errorMessage}}", "failedMaxAttempts": "Échec de la création des embeddings après {{attempts}} tentatives", "textExceedsTokenLimit": "Le texte à l'index {{index}} dépasse la limite maximale de tokens ({{itemTokens}} > {{maxTokens}}). Ignoré.", "rateLimitRetry": "<PERSON>ite de débit atteinte, nouvelle tentative dans {{delayMs}}ms (tentative {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "Impossible de lire le corps de l'erreur", "requestFailed": "Échec de la requête API Ollama avec le statut {{status}} {{statusText}} : {{errorBody}}", "invalidResponseStructure": "Structure de réponse invalide de l'API Ollama : tableau \"embeddings\" non trouvé ou n'est pas un tableau.", "embeddingFailed": "Échec de l'embedding Ollama : {{message}}", "serviceNotRunning": "Le service Ollama n'est pas en cours d'exécution sur {{baseUrl}}", "serviceUnavailable": "Le service Ollama est indisponible (statut : {{status}})", "modelNotFound": "<PERSON><PERSON><PERSON><PERSON> introuvable : {{modelId}}", "modelNotEmbeddingCapable": "Le modèle O<PERSON> n'est pas capable d'intégrer : {{modelId}}", "hostNotFound": "<PERSON>ôte <PERSON> introuvable : {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Erreur inconnue lors du traitement du fichier {{filePath}}", "unknownErrorDeletingPoints": "Erreur inconnue lors de la suppression des points pour {{filePath}}", "failedToProcessBatchWithError": "Échec du traitement du lot après {{maxRetries}} tentatives : {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Échec de la connexion à la base de données vectorielle Qdrant. Veuillez vous assurer que Qdrant fonctionne et est accessible à {{qdrantUrl}}. Erreur : {{errorMessage}}"}, "validation": {"authenticationFailed": "Échec de l'authentification. Veuillez vérifier votre clé API dans les paramètres.", "connectionFailed": "Échec de la connexion au service d'embedding. Veuillez vérifier vos paramètres de connexion et vous assurer que le service est en cours d'exécution.", "modelNotAvailable": "Le modèle spécifié n'est pas disponible. Veuillez vérifier la configuration de votre modèle.", "configurationError": "Configuration de l'embedder invalide. Veuillez vérifier vos paramètres.", "serviceUnavailable": "Le service d'embedding n'est pas disponible. Veuillez vous assurer qu'il est en cours d'exécution et accessible.", "invalidEndpoint": "Point de terminaison d'API invalide. Veuillez vérifier votre configuration d'URL.", "invalidEmbedderConfig": "Configuration de l'embedder invalide. Veuillez vérifier vos paramètres.", "invalidApiKey": "Clé API invalide. Veuillez vérifier votre configuration de clé API.", "invalidBaseUrl": "URL de base invalide. Veuillez vérifier votre configuration d'URL.", "invalidModel": "Modèle invalide. Veuillez vérifier votre configuration de modèle.", "invalidResponse": "Réponse invalide du service d'embedder. Veuillez vérifier votre configuration."}, "serviceFactory": {"openAiConfigMissing": "Configuration OpenAI manquante pour la création de l'embedder", "ollamaConfigMissing": "Configuration Ollama manquante pour la création de l'embedder", "openAiCompatibleConfigMissing": "Configuration compatible OpenAI manquante pour la création de l'embedder", "geminiConfigMissing": "Configuration Gemini manquante pour la création de l'embedder", "invalidEmbedderType": "Type d'embedder configuré invalide : {{embedder<PERSON><PERSON>ider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "Impossible de déterminer la dimension du vecteur pour le modèle '{{modelId}}' avec le fournisseur '{{provider}}'. Assure-toi que la 'Dimension d'embedding' est correctement définie dans les paramètres du fournisseur compatible OpenAI.", "vectorDimensionNotDetermined": "Impossible de déterminer la dimension du vecteur pour le modèle '{{modelId}}' avec le fournisseur '{{provider}}'. Vérifie les profils du modèle ou la configuration.", "qdrantUrlMissing": "URL Qdrant manquante pour la création du stockage de vecteurs", "codeIndexingNotConfigured": "Impossible de créer les services : L'indexation du code n'est pas correctement configurée"}}