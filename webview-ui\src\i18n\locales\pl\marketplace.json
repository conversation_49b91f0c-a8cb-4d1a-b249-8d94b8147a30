{"title": "Kilo Code Marketplace", "tabs": {"installed": "Zainstalowane", "settings": "Ustawienia", "browse": "Przeglądaj"}, "done": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filters": {"search": {"placeholder": "Szukaj elementów marketplace...", "placeholderMcp": "Szukaj MCPs...", "placeholderMode": "S<PERSON>j trybów..."}, "type": {"label": "Filtruj według typu:", "all": "Wszystkie typy", "mode": "<PERSON><PERSON>", "mcpServer": "<PERSON>wer MC<PERSON>"}, "sort": {"label": "<PERSON><PERSON><PERSON><PERSON>:", "name": "Nazwa", "author": "Autor", "lastUpdated": "Ostatnia aktualizacja"}, "tags": {"label": "Filtruj według tag<PERSON>:", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tagi", "placeholder": "<PERSON><PERSON><PERSON>, aby wys<PERSON> i wybrać tagi...", "noResults": "Nie znaleziono pasujących tagów", "selected": "Pokazywanie elementów z dowolnym z wybranych tagów", "clickToFilter": "<PERSON><PERSON><PERSON><PERSON> tag<PERSON>, aby filtrow<PERSON>ć elementy"}, "none": "Brak"}, "type-group": {"modes": "Tryby", "mcps": "Serwery MCP"}, "items": {"empty": {"noItems": "Nie znaleziono elementów marketplace", "withFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> swoje filtry", "noSources": "Spróbuj dodać źródło w zakładce Źródła", "adjustFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> swoje filtry lub terminy wyszukiwania", "clearAllFilters": "<PERSON><PERSON><PERSON><PERSON>ść wszystkie filtry"}, "count": "Znaleziono {{count}} elementów", "components": "{{count}} komponentów", "matched": "{{count}} dopasowanych", "refresh": {"button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshing": "Odświeżanie...", "mayTakeMoment": "To może ch<PERSON><PERSON><PERSON>."}, "card": {"by": "przez {{author}}", "from": "z {{source}}", "install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "installProject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "installGlobal": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Globalnie)", "remove": "Usuń", "removeProject": "Usuń", "removeGlobal": "<PERSON><PERSON>ń (Globalnie)", "viewSource": "<PERSON><PERSON><PERSON><PERSON>", "viewOnSource": "Zobacz na {{source}}", "noWorkspaceTooltip": "Otwórz obs<PERSON> rob<PERSON>, aby zainstalować elementy marketplace", "installed": "Zainstalowane", "removeProjectTooltip": "Usuń z bieżącego projektu", "removeGlobalTooltip": "Usuń z konfiguracji globalnej", "actionsMenuLabel": "Wię<PERSON>j a<PERSON>"}}, "install": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{name}}", "titleMode": "<PERSON><PERSON><PERSON><PERSON><PERSON> tryb {{name}}", "titleMcp": "Zainstaluj MCP {{name}}", "scope": "<PERSON><PERSON><PERSON>", "project": "Projekt (bieżący obszar roboczy)", "global": "Globalnie (wszystkie obszary robocze)", "method": "Metoda instalacji", "configuration": "Konfiguracja", "configurationDescription": "Skonfiguruj parametry wymagane dla tego serwera MCP", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "successTitle": "{{name}} zain<PERSON><PERSON>ne", "successDescription": "Instalacja zakończona pomyślnie", "installed": "Zainstalowano pomyślnie!", "whatNextMcp": "Możesz teraz skonfigurować i używać tego serwera MCP. Kliknij ikonę MCP na pasku bocznym, aby prz<PERSON>ł<PERSON><PERSON><PERSON>ć zakładki.", "whatNextMode": "Możesz teraz używać tego trybu. Kliknij ikonę Tryby na pasku bocznym, aby przeł<PERSON>cz<PERSON>ć zakładki.", "done": "<PERSON><PERSON><PERSON>", "goToMcp": "Przejdź do zakładki MCP", "goToModes": "Przejdź do ustawień Trybów", "moreInfoMcp": "Zobacz dokumentację MCP {{name}}", "validationRequired": "<PERSON><PERSON><PERSON> dla {{paramName}}", "prerequisites": "Wymagania wstępne"}, "sources": {"title": "Konfiguruj źródła marketplace", "description": "Dodaj repozytoria Git zawierające elementy marketplace. Te repozytoria będą pobierane podczas przeglądania marketplace.", "add": {"title": "Dodaj nowe źródło", "urlPlaceholder": "URL repozytorium Git (np. https://github.com/username/repo)", "urlFormats": "Obsługiwane formaty: HTTPS (https://github.com/username/repo), SSH (**************:username/repo.git) lub protokół Git (git://github.com/username/repo.git)", "namePlaceholder": "Nazwa wyświetlana (maks. 20 znaków)", "button": "<PERSON><PERSON><PERSON>"}, "current": {"title": "Bieżące źródła", "empty": "Brak skonfigurowanych źródeł. <PERSON><PERSON><PERSON>, a<PERSON> r<PERSON>.", "refresh": "Odśwież to źródło", "remove": "<PERSON><PERSON><PERSON> ź<PERSON>ódło"}, "errors": {"emptyUrl": "URL nie może być pusty", "invalidUrl": "Nieprawidłowy format URL", "nonVisibleChars": "URL zawiera niewidoczne znaki inne niż spacje", "invalidGitUrl": "URL musi być prawidłowym URL repozytorium Git (np. https://github.com/username/repo)", "duplicateUrl": "Ten URL jest już na liście (dopasowanie bez uwzględniania wielkości liter i spacji)", "nameTooLong": "Nazwa musi mieć 20 znaków lub mniej", "nonVisibleCharsName": "Nazwa zawiera niewidoczne znaki inne niż spacje", "duplicateName": "Ta nazwa jest już używana (dopasowanie bez uwzględniania wielkości liter i spacji)", "emojiName": "Znaki emoji mogą powodować problemy z wyświetlaniem", "maxSources": "<PERSON><PERSON><PERSON><PERSON>nie {{max}} źródeł dozwolonych"}}, "footer": {"issueText": "Znalazłeś problem z elementem marketplace lub masz sugestie dotyczące nowych elementów? <0>Otwórz issue na GitHub</0>, aby nam o tym powied<PERSON>!"}}