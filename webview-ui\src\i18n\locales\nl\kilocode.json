{"welcome": {"greeting": "Welkom bij Kilo Code!", "introText1": "Kilo Code is een gratis, open source AI coding agent.", "introText2": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met de nieuwste AI modellen zoals Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1, en meer dan 450 andere.", "introText3": "Maak een gratis account aan en krijg $20 aan tokens om te gebruiken met elk AI model.", "ctaButton": "Maak gratis account aan", "manualModeButton": "Gebruik je eigen API-sleutel", "alreadySignedUp": "Al aangemeld?", "loginText": "Log hier in"}, "lowCreditWarning": {"addCredit": "Krediet toe<PERSON>n", "lowBalance": "<PERSON><PERSON>-saldo is laag"}, "notifications": {"toolRequest": "Toolverzoek wacht op goedkeuring", "browserAction": "Browseractie wacht op goedkeuring", "command": "Commando wacht op goedkeuring"}, "settings": {"sections": {"mcp": "MCP-servers"}, "provider": {"account": "<PERSON><PERSON> Code Account", "apiKey": "Kilo Code API-sleutel", "login": "Inloggen bij Kilo Code", "logout": "Uitloggen bij Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "<PERSON><PERSON> zeer grote bestandslezingen toe", "description": "<PERSON><PERSON>, zal Kilo Code zeer grote bestands- of MCP-outputlezingen uitvoeren, zelfs als er een grote kans is dat het contextvenster overloopt (inhoudsgrootte >80% van het contextvenster)."}}, "systemNotifications": {"label": "Systeemmeldingen inschakelen", "description": "<PERSON><PERSON>, zal Kilo Code systeemmeldingen sturen voor belangrijke gebeurtenissen zoals taakvoltooing of fouten.", "testButton": "Testmelding", "testTitle": "Kilo Code", "testMessage": "<PERSON><PERSON> is een testmelding van Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "<PERSON><PERSON> wil je gesp<PERSON> same<PERSON>ten", "condenseConversation": "<PERSON><PERSON><PERSON><PERSON>"}}, "newTaskPreview": {"task": "<PERSON><PERSON>"}, "profile": {"title": "<PERSON><PERSON>", "dashboard": "Dashboard", "logOut": "Uitloggen", "currentBalance": "HUIDIG SALDO", "loading": "Laden..."}, "docs": "Documentatie", "rules": {"tooltip": "Beheer Kilo Code Regels & Workflows", "ariaLabel": "Kilo <PERSON> Regels", "tabs": {"rules": "Regels", "workflows": "Workflows"}, "description": {"rules": "Regels stellen je in staat om Kilo Code instructies te geven die het moet volgen in alle modi en voor alle prompts. Ze zijn een persistente manier om context en voorkeuren op te nemen voor alle gesprekken in je workspace of globaal.", "workflows": "Workflows zijn een voorbereid sjabloon voor een gesprek. Workflows stellen je in staat om prompts te definiëren die je vaak gebruikt, en kunnen een reeks stappen bevatten om Kilo Code te begeleiden door repetitieve taken, zoals het implementeren van een service of het indienen van een PR. Om een workflow aan te roepen, typ", "workflowsInChat": "in de chat."}, "sections": {"globalRules": "Globale Regels", "workspaceRules": "Werkruimte Regels", "globalWorkflows": "Globale Workflows", "workspaceWorkflows": "Werkruimte Workflows"}, "validation": {"invalidFileExtension": "Alleen .md, .txt of geen bestandsextensie toegestaan"}, "placeholders": {"workflowName": "workflow-naam (.md, .txt of geen extensie)", "ruleName": "regel-naam (.md, .txt of geen extensie)"}, "newFile": {"newWorkflowFile": "Nieuw workflow bestand...", "newRuleFile": "<PERSON><PERSON><PERSON> regel bestand..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Bekijk {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_result": "<PERSON><PERSON><PERSON>", "browser_action_launch": "browser opstarten", "checkpoint_saved": "controlepunt opgeslagen", "command": "opdrachtuitvoering", "browser_action": "browseractie", "command_output": "opdrachtuitvoer", "completion_result": "voltooiingsresultaat", "error": "foutmelding", "followup": "vervolgvraag", "mcp_server_response": "MCP-serverrespons", "reasoning": "AI-redenering", "text": "AI-antwoord", "unknown": "onbekend berich<PERSON>pe", "condense_context": "context same<PERSON><PERSON><PERSON>", "use_mcp_server": "MCP-servergebruik", "tool": "gereedschapsgebruik", "user": "gebruikersfeedback"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "Verzenden", "restoreAndSend": "Herstellen & Verzenden"}, "ideaSuggestionsBox": {"newHere": "Nieuw hier?", "suggestionText": "<suggestionButton><PERSON><PERSON> hier</suggestionButton> voor een cool idee, tik dan op <sendIcon /> en kijk hoe ik magie doe!", "ideas": {"idea1": "Maak een d<PERSON>e, gloeiende 3D-bol die reageert op muisbewegingen. Laat de app in de browser werken.", "idea2": "Maak een portfoliowebsite voor een Python-softwareontwikkelaar", "idea3": "Maak een financiële app-mockup in de browser. Test dan of het werkt", "idea4": "Maak een directorywebs<PERSON> met de momenteel beste AI-videogeneratiemodellen", "idea5": "Maak een CSS-gradiëntgenerator die aangepaste stylesheets exporteert en een live-preview toont", "idea6": "<PERSON>reer kaarten die dynamische content prachtig onthullen bij hovering", "idea7": "Maak een rustgevend interactief sterrenveld dat beweegt met muisgebaren"}}}