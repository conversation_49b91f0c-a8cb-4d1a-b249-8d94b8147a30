{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "कोड की योजना बनाने, निर्माण करने और उसे ठीक करने के लिए ओपन सोर्स एआई कोडिंग सहायक।", "command.newTask.title": "नया कार्य", "command.explainCode.title": "कोड समझाएं", "command.fixCode.title": "कोड ठीक करें", "command.improveCode.title": "कोड सुधारें", "command.addToContext.title": "संदर्भ में जोड़ें", "command.openInNewTab.title": "नए टैब में खोलें", "command.focusInput.title": "इनपुट फ़ील्ड पर फोकस करें", "command.setCustomStoragePath.title": "कस्टम स्टोरेज पाथ सेट करें", "command.importSettings.title": "सेटिंग्स इम्पोर्ट करें", "command.terminal.addToContext.title": "टर्मिनल सामग्री को संदर्भ में जोड़ें", "command.terminal.fixCommand.title": "यह कमांड ठीक करें", "command.terminal.explainCommand.title": "यह कमांड समझाएं", "command.acceptInput.title": "इनपुट/सुझाव स्वीकारें", "command.generateCommitMessage.title": "<PERSON><PERSON> के साथ कमिट संदेश जेनरेट करें", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "एमसीपी सर्वर", "command.prompts.title": "मोड्स", "command.history.title": "इतिहास", "command.marketplace.title": "मार्केटप्लेस", "command.openInEditor.title": "एडिटर में खोलें", "command.settings.title": "सेटिंग्स", "command.documentation.title": "दस्तावेज़ीकरण", "command.profile.title": "प्रोफ़ाइल", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "वे कमांड जो स्वचालित रूप से निष्पादित की जा सकती हैं जब 'हमेशा निष्पादन संचालन को स्वीकृत करें' सक्रिय हो", "settings.vsCodeLmModelSelector.description": "VSCode भाषा मॉडल API के लिए सेटिंग्स", "settings.vsCodeLmModelSelector.vendor.description": "भाषा मॉडल का विक्रेता (उदा. copilot)", "settings.vsCodeLmModelSelector.family.description": "भाषा मॉडल का परिवार (उदा. gpt-4)", "settings.customStoragePath.description": "कस्टम स्टोरेज पाथ। डिफ़ॉल्ट स्थान का उपयोग करने के लिए खाली छोड़ें। पूर्ण पथ का समर्थन करता है (उदा. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "<PERSON><PERSON> Code त्वरित सुधार सक्षम करें", "settings.autoImportSettingsPath.description": "Kilo Code कॉन्फ़िगरेशन फ़ाइल का पथ जिसे एक्सटेंशन स्टार्टअप पर स्वचालित रूप से आयात किया जाएगा। होम डायरेक्टरी के सापेक्ष पूर्ण पथ और पथों का समर्थन करता है (उदाहरण के लिए '~/Documents/kilo-code-settings.json')। ऑटो-इंपोर्ट को अक्षम करने के लिए खाली छोड़ दें।", "ghost.input.title": "पुष्टि के लिए 'Enter' दबाएं या रद्द करने के लिए 'Escape' दबाएं", "ghost.input.placeholder": "वर्णन करें कि आप क्या करना चाहते हैं...", "ghost.commands.generateSuggestions": "Kilo Code: सुझाए गए संपादन जेनरेट करें", "ghost.commands.displaySuggestions": "सुझाए गए संपादन दिखाएं", "ghost.commands.cancelSuggestions": "सुझाए गए संपादन रद्द करें", "ghost.commands.applyCurrentSuggestion": "वर्तमान सुझाया गया संपादन लागू करें", "ghost.commands.applyAllSuggestions": "सभी सुझाए गए संपादन लागू करें", "ghost.commands.promptCodeSuggestion": "त्वरित कार्य", "ghost.commands.goToNextSuggestion": "अगले सुझाव पर जाएं", "ghost.commands.goToPreviousSuggestion": "पिछले सुझाव पर जाएं"}