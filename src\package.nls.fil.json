{"extension.displayName": "Kilo Code AI Agent (Pinagsama ang mga feature ng Cline / Roo)", "extension.description": "Open Source AI coding assistant para sa pagpaplano, pagbuo, at pag-aayos ng code.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "Bagong Gawain", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Mga Mode", "command.history.title": "<PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "<PERSON><PERSON><PERSON>", "command.settings.title": "Mga Setting", "command.documentation.title": "Dokumentasyon", "command.openInNewTab.title": "<PERSON><PERSON><PERSON> Tab", "command.explainCode.title": "Ipaliwanag ang Code", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>", "command.addToContext.title": "Idagdag Sa Context", "command.focusInput.title": "I-focus ang Input Field", "command.setCustomStoragePath.title": "Itakda ang Custom Storage Path", "command.terminal.addToContext.title": "Idagdag ang Terminal Content sa Context", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON> ang <PERSON> na Ito", "command.terminal.explainCommand.title": "Ipaliwanag ang Command na Ito", "command.acceptInput.title": "Tanggapin ang Input/Suhes<PERSON>yon", "command.generateCommitMessage.title": "Gumawa ng Commit Message gamit ang Kilo", "command.profile.title": "Profile", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Mga command na maaaring awtomatikong i-execute kapag naka-enable ang 'Always approve execute operations'", "settings.vsCodeLmModelSelector.description": "Mga setting para sa VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Ang vendor ng language model (hal. copilot)", "settings.vsCodeLmModelSelector.family.description": "Ang family ng language model (hal. gpt-4)", "settings.customStoragePath.description": "Custom storage path. Iwanang blangko para gamitin ang default na lokasyon. Sumusuporta ng absolute paths (hal. 'D:\\KiloCodeStorage')", "command.importSettings.title": "I-import ang mga Setting", "settings.enableCodeActions.description": "I-enable ang Kilo Code quick fixes", "settings.autoImportSettingsPath.description": "Path sa Kilo Code configuration file na awtomatikong i-import sa extension startup. Sumusuporta ng absolute paths at paths na relative sa home directory (hal. '~/Documents/kilo-code-settings.json'). Iwanang blangko para i-disable ang auto-import.", "ghost.input.title": "<PERSON><PERSON><PERSON><PERSON> ang '<PERSON>ter' para kumpirmahin o 'Escape' para kanselahin", "ghost.input.placeholder": "Ilarawan kung ano ang gusto mong gawin...", "ghost.commands.generateSuggestions": "Kilo Code: Gumawa ng Mga Iminungkahing Pagbabago", "ghost.commands.displaySuggestions": "Ipakita ang <PERSON>hing Pagbabago", "ghost.commands.cancelSuggestions": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON> Pagbabago", "ghost.commands.applyCurrentSuggestion": "<PERSON><PERSON><PERSON> ang <PERSON> Pagbabago", "ghost.commands.applyAllSuggestions": "Gamitin ang Lahat ng Iminungkahing Pagbabago", "ghost.commands.promptCodeSuggestion": "Ma<PERSON>is na Gawain", "ghost.commands.goToNextSuggestion": "Pumunta sa Susunod na Suhestiyon", "ghost.commands.goToPreviousSuggestion": "Pumunta sa Nakaraang Suhestiyon"}