{"number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Opinie", "description": "Chcielibyśmy usłyszeć Twoje opinie lub pomóc Ci z problemami, kt<PERSON><PERSON>ch doświadczasz.", "githubIssues": "Zgłoś problem na GitHubie", "githubDiscussions": "Dołącz do dyskusji na GitHubie", "discord": "Dołącz do naszej społeczności Discord", "customerSupport": "Obsługa Klienta"}, "answers": {"yes": "Tak", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Usuń", "keep": "<PERSON><PERSON><PERSON>"}, "ui": {"search_placeholder": "Szukaj..."}, "mermaid": {"loading": "Generowanie diagramu mermaid...", "render_error": "<PERSON><PERSON> m<PERSON><PERSON>u", "fixing_syntax": "Naprawianie składni Mermaid...", "fix_syntax_button": "Napraw składnię za pomocą AI", "original_code": "Oryginalny kod:", "errors": {"unknown_syntax": "Nieznany błąd składni", "fix_timeout": "Upłynął limit czasu żądania naprawy LLM", "fix_failed": "Naprawa LLM nie powiodła się", "fix_attempts": "<PERSON><PERSON> u<PERSON>ło się nap<PERSON> składni po {{attempts}} próbach. Ostatni błąd: {{error}}", "no_fix_provided": "LLM nie dostarczył <PERSON>rawy", "fix_request_failed": "Żądanie naprawy nie powiodło się"}, "buttons": {"zoom": "Powiększenie", "zoomIn": "Powię<PERSON><PERSON>", "zoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "save": "Zapisz obraz", "viewCode": "Zobacz kod", "viewDiagram": "<PERSON><PERSON><PERSON><PERSON> diagram", "close": "Zamknij"}, "modal": {"codeTitle": "Kod Mermaid"}, "tabs": {"diagram": "Diagram", "code": "Kod"}, "feedback": {"imageCopied": "Obraz skopiowany do schowka", "copyError": "Błąd kopiowania obrazu"}}, "file": {"errors": {"invalidDataUri": "Nieprawidłowy format URI danych", "copyingImage": "Błąd kopiowania obrazu: {{error}}", "openingImage": "Błąd otwierania obrazu: {{error}}", "pathNotExists": "Ścieżka nie istnieje: {{path}}", "couldNotOpen": "<PERSON>e można otworzyć pliku: {{error}}", "couldNotOpenGeneric": "Nie można otworzyć pliku!"}, "success": {"imageDataUriCopied": "URI danych obrazu skopiowane do schowka"}}}