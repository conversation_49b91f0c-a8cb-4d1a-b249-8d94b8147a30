{"welcome": {"greeting": "<PERSON><PERSON> Code'a hoş geldiniz!", "introText1": "Kilo Code ücretsiz, açık kaynaklı bir AI kodlama ajanıdır.", "introText2": "Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 ve 450+ diğer en yeni AI modelleri ile çalışır.", "introText3": "Ücretsiz hesap oluşturun ve herhangi bir AI modeli ile kullanabilceğiniz $20 değerinde token alın.", "ctaButton": "Ücretsiz hesap o<PERSON>", "manualModeButton": "Kendi API anahtarınızı kullanın", "alreadySignedUp": "Zaten kaydoldunuz mu?", "loginText": "<PERSON><PERSON><PERSON> giri<PERSON> ya<PERSON>ın"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON>", "lowBalance": "Kilo Code bakiyeniz düşük"}, "notifications": {"toolRequest": "<PERSON><PERSON> is<PERSON> onay bekliyor", "browserAction": "Tarayıcı eylemi onay bekliyor", "command": "<PERSON><PERSON><PERSON> onay bekliyor"}, "settings": {"sections": {"mcp": "MCP Sunucuları"}, "contextManagement": {"allowVeryLargeReads": {"label": "Çok büyük dosya okumalarına izin ver", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Kilo Code bağlam penceresini taşma olasılığı yüksek olsa bile çok büyük dosya veya MCP çıktı okumalarını gerçekleştirir (içerik boyutu bağlam penceresinin %80'inden fazla)."}}, "provider": {"account": "Kilo Code Hesabı", "apiKey": "Kilo Code API Anahtarı", "login": "<PERSON>lo <PERSON>'a giriş yap", "logout": "Kilo Code'dan <PERSON> yap"}, "systemNotifications": {"label": "Sistem bildirimlerini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Kilo <PERSON> görev tamamlama veya hatalar gibi önemli olaylar için sistem bildirimleri gönderecektir.", "testButton": "Bildirimi test et", "testTitle": "Kilo Code", "testMessage": "<PERSON><PERSON> Kilo Code'dan bir test bildirimidir."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code konuşmanı özetlemek istiyor", "condenseConversation": "Konuşmayı Özetle"}}, "newTaskPreview": {"task": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "dashboard": "Ko<PERSON>rol <PERSON>i", "logOut": "Çıkış Yap", "currentBalance": "MEVCUT BAKİYE", "loading": "Yükleniyor..."}, "docs": "<PERSON><PERSON><PERSON>", "rules": {"tooltip": "Kilo Code Kuralları ve İş Akışlarını Yönet", "ariaLabel": "Kilo Code Kuralları", "tabs": {"rules": "<PERSON><PERSON><PERSON>", "workflows": "İş Akışları"}, "description": {"rules": "<PERSON><PERSON><PERSON>, Kilo Code'a tüm modlarda ve tüm promptlarda uyması gereken talimatlar sağlamanıza olanak tanır. <PERSON><PERSON><PERSON>, çalışma alanınızda veya global olarak tüm konuşmalar için bağlam ve tercihleri dahil etmenin kalıcı bir yoludur.", "workflows": "İş akışları, bir konuşma için hazırlanmış bir şablondur. İş akışları, sık kullandığınız promptları tanımlamanıza olanak tanır ve bir hizmeti dağıtmak veya PR göndermek gibi tekrarlayan görevler boyunca Kilo Code'u yönlendirmek için bir dizi adım içerebilir. Bir iş akışını çağırmak için şunu yazın", "workflowsInChat": "<PERSON><PERSON><PERSON><PERSON>."}, "sections": {"globalRules": "Global Kurallar", "workspaceRules": "Çalışma Alanı Kuralları", "globalWorkflows": "Global İş Akışları", "workspaceWorkflows": "Çalışma Alanı İş Akışları"}, "validation": {"invalidFileExtension": "Yalnızca .md, .txt veya uzantısız dosyalara izin verilir"}, "placeholders": {"workflowName": "iş-akışı-adı (.md, .txt veya uzantısız)", "ruleName": "kural-adı (.md, .txt veya uzantısız)"}, "newFile": {"newWorkflowFile": "Yeni iş akışı dosyası...", "newRuleFile": "<PERSON>ni kural dosyası..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "{{messageType}} (#{{messageNumber}}) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "messageTypes": {"browser_action_launch": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>latma", "browser_action_result": "tarayıcı işlemi sonucu", "browser_action": "tarayı<PERSON>ı e<PERSON>mi", "command_output": "komut çıktısı", "completion_result": "tama<PERSON><PERSON>a son<PERSON>u", "checkpoint_saved": "kontrol noktası kaydedildi\n(sometimes can be \"ilerleme kaydedildi\" depending on context)", "condense_context": "bağlamı özetle", "error": "hata mesajı", "command": "komut y<PERSON><PERSON><PERSON><PERSON>", "followup": "devam sorusu", "mcp_server_response": "MCP sunucu yanıtı", "text": "YZ yanıtı", "reasoning": "<PERSON><PERSON><PERSON> zeka muhake<PERSON>i", "tool": "a<PERSON>ç k<PERSON>anımı", "unknown": "bilinmeyen mesaj türü", "use_mcp_server": "MCP sunucu k<PERSON>anımı", "user": "kullanıcı geri bildirimi"}}}, "userFeedback": {"editCancel": "İptal", "send": "<PERSON><PERSON><PERSON>", "restoreAndSend": "<PERSON><PERSON>"}, "ideaSuggestionsBox": {"newHere": "<PERSON><PERSON>da yeni misin?", "suggestionText": "<PERSON><PERSON> bir fi<PERSON><PERSON> i<PERSON> <suggestionButton>buraya tıkla</suggestionButton>, sonra <sendIcon /> öğesine dokun ve beni büyü yaparken izle!", "ideas": {"idea1": "Fare hareketlerine yanıt veren dönen, parlayan 3D küre yap. Uygulamanın tarayıcıda çalışmasını sağla.", "idea2": "Bir Python yazılım geliştirici için portföy web sitesi oluştur", "idea3": "Tarayıcıda finansal uygulama maket oluştur. Sonra çalışıp çalışmadığını test et", "idea4": "<PERSON>u anda en iyi AI video üretim modellerini içeren bir dizin web sitesi oluştur", "idea5": "Özel stil sayfaları dışa aktaran ve canlı önizleme gösteren CSS gradient üretici oluştur", "idea6": "Fare üzerine geldiğinde dinamik içeriği güzelce ortaya çıkaran kartlar üret", "idea7": "Fare hareketleriyle birlikte hareket eden sakinleştirici etkileşimli yıldız alanı oluştur"}}}