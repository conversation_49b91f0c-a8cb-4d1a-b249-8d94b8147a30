{"greeting": "مرحبًا، أنا Kilo Code!", "introduction": "<strong>Kilo Code هو وكيل البرمجة الذاتي الأفضل على الإطلاق.</strong> استعد للتصميم والبرمجة وتصحيح الأخطاء وزيادة إنتاجيتك بشكل غير مسبوق. للمتابعة، يحتاج Kilo Code إلى مفتاح API.", "notice": "عشان تبدأ، هذي الإضافة تحتاج مزوّد API.", "start": "يلا نبدأ!", "routers": {"requesty": {"description": "موجّه LLM الأمثل لك", "incentive": "$1 رصيد مجاني"}, "openrouter": {"description": "واجهة موحّدة لنماذج LLM"}}, "chooseProvider": "عشان يشتغل سحر <PERSON>lo <PERSON>، يلزم مفتاح API.", "startRouter": "نوصي باستخدام موجّه LLM:", "startCustom": "أو تقدر تستخدم مفتاح مزوّدك الخاص:", "telemetry": {"title": "ساهم في تحسين Kilo Code", "anonymousTelemetry": "أرسل بيانات الأخطاء والاستخدام لمساعدتنا في إصلاح الأخطاء وتحسين الإضافة. لا يتم إرسال أي كود أو prompts أو معلومات شخصية أبداً.", "changeSettings": "تقدر تغيّر هالخيارات دايمًا من أسفل <settingsLink>الإعدادات</settingsLink>", "settings": "الإعدادات", "allow": "سماح", "deny": "<PERSON><PERSON><PERSON>"}, "importSettings": "استيراد الإعدادات"}