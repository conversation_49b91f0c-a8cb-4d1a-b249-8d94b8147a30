{"title": "خوادم MCP", "done": "تم", "description": "<0>بروتوكول سياق النماذج</0> يسمح بالتواصل مع خوادم MCP تشتغل محليًا وتضيف أدوات وموارد توسّع قدرات Kilo Code. تقدر تستخدم <1>خوادم من المجتمع</1> أو تطلب من Kilo Code يبني لك أدوات مخصصة حسب شغلك (مثال: \"أضف أداة تجيب آخر توثيق npm\").", "instructions": "الإرشادات", "enableToggle": {"title": "تفعيل خوادم MCP", "description": "شغّل الخيار هذا عشان Kilo Code يستخدم أدوات الخوادم المتصلة. هذا يعطيه قدرات أكثر. إذا ما تستخدم الأدوات الإضافية، طفّه لتقليل استهلاك التوكنات."}, "enableServerCreation": {"title": "تفعيل إنشاء خوادم MCP", "description": "فعل هذا الخيار إذا تبي Kilo Code يساعدك تبني خوادم MCP <1>جديدة</1> مخصصة. <0>تعرف على طريقة بناء الخوادم</0>", "hint": "تنبيه: إذا ما تحتاج تبني خادم جديد، يفضّل تطفّي هالخيار لتقليل تكلفة التوكنات."}, "editGlobalMCP": "تعديل MCP العام", "editProjectMCP": "تعديل MCP للمشروع", "refreshMCP": "تحديث خوادم MCP", "learnMoreEditingSettings": "تعرف أكثر على طريقة تعديل إعدادات MCP", "tool": {"alwaysAllow": "السماح دائمًا", "parameters": "المعلمات", "noDescription": "ما فيه وصف", "togglePromptInclusion": "تبديل تضمينها في الموجه"}, "tabs": {"tools": "الأدوات", "resources": "الموارد", "errors": "الأخطاء"}, "emptyState": {"noTools": "ما فيه أدوات", "noResources": "ما فيه موارد", "noErrors": "ما فيه أخطاء"}, "networkTimeout": {"label": "مهلة الشبكة", "description": "الحد الأقصى للانتظار على رد السيرفر", "options": {"15seconds": "15 ثانية", "30seconds": "30 ثانية", "1minute": "دقيقة وحدة", "5minutes": "5 دقايق", "10minutes": "10 دقايق", "15minutes": "15 دقيقة", "30minutes": "30 دقيقة", "60minutes": "ساعة كاملة"}}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON> MCP", "description": "متأكد تبي تحذف خادم MCP \"{{serverName}}\"؟ هذا الإجراء ما تقدر ترجعه.", "cancel": "إلغاء", "delete": "<PERSON><PERSON><PERSON>"}, "serverStatus": {"retrying": "جاري المحاولة من جديد...", "retryConnection": "إعادة محاولة الاتصال"}, "execution": {"running": "قيد التشغيل", "completed": "اكتمل", "error": "خطأ"}}