function calculateDiscount(price, discountRate) {
	return price * discountRate
}

function applyDiscount(item) {
	const discount = calculateDiscount(item.price, 0.1)
	return {
		...item,
		originalPrice: item.price,
		discountedPrice: item.price - discount,
	}
}

function processItems(items) {
	// Filter out invalid items
	const validItems = items.filter((item) => item && item.price > 0)
	return validItems.map(applyDiscount)
}
