{"extension.displayName": "Kilo Code AI Agent (Cline / Roo features combined)", "extension.description": "Assistente di programmazione AI open source per la pianificazione, la creazione e la correzione del codice.", "command.newTask.title": "Nuovo Task", "command.explainCode.title": "Spiega Codice", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "Aggiungi al Contesto", "command.openInNewTab.title": "Apri in Nuova Scheda", "command.focusInput.title": "Focalizza Campo di Input", "command.setCustomStoragePath.title": "Imposta Percorso di Archiviazione Personalizzato", "command.importSettings.title": "Importa Impostazioni", "command.terminal.addToContext.title": "Aggiungi Contenuto del Terminale al Contesto", "command.terminal.fixCommand.title": "Corregg<PERSON>", "command.terminal.explainCommand.title": "Spiega Questo Comando", "command.acceptInput.title": "Accetta Input/Suggerimento", "command.generateCommitMessage.title": "Genera messaggio di commit con Kilo", "views.activitybar.title": "Kilo Code", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.sidebar.name": "Kilo Code", "command.mcpServers.title": "Server MCP", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "Cronologia", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "<PERSON><PERSON> nell'Editor", "command.settings.title": "Impostazioni", "command.documentation.title": "Documentazione", "command.profile.title": "<PERSON>ilo", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Comandi che possono essere eseguiti automaticamente quando 'Approva sempre le operazioni di esecuzione' è attivato", "settings.vsCodeLmModelSelector.description": "Impostazioni per l'API del modello linguistico VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Il fornitore del modello linguistico (es. copilot)", "settings.vsCodeLmModelSelector.family.description": "La famiglia del modello linguistico (es. gpt-4)", "settings.customStoragePath.description": "Percorso di archiviazione personalizzato. Lasciare vuoto per utilizzare la posizione predefinita. Supporta percorsi assoluti (es. 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "Abilita correzioni rapide di Kilo Code.", "settings.autoImportSettingsPath.description": "Percorso di un file di configurazione di Kilo Code da importare automaticamente all'avvio dell'estensione. Supporta percorsi assoluti e percorsi relativi alla directory home (ad es. '~/Documents/kilo-code-settings.json'). Lasciare vuoto per disabilitare l'importazione automatica.", "ghost.input.title": "Scrittore Fantasma Kilo Code", "ghost.input.placeholder": "Descrivi cosa vuoi programmare...", "ghost.commands.generateSuggestions": "Kilo Code: <PERSON>ra Modifiche <PERSON>gger<PERSON>", "ghost.commands.displaySuggestions": "Visualizza Modifiche Suggerite", "ghost.commands.cancelSuggestions": "<PERSON><PERSON><PERSON>", "ghost.commands.applyCurrentSuggestion": "Applica Modifica Suggerita Corrente", "ghost.commands.applyAllSuggestions": "Applica Tutte le Modifiche Suggerite", "ghost.commands.promptCodeSuggestion": "Attività Rapida", "ghost.commands.goToNextSuggestion": "Vai alla Prossima <PERSON>gger<PERSON>o", "ghost.commands.goToPreviousSuggestion": "Vai al Suggerimento Precedente"}