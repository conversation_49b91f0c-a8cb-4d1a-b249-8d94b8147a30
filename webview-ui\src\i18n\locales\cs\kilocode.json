{"welcome": {"greeting": "Vítej v Kilo Code!", "introText1": "Kilo Code je bezplatný open source AI coding agent.", "introText2": "Funguje s nejnovějšími AI modely jako Claude 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 a více než 450 dalšími.", "introText3": "Vytvoř si bezplatný účet a získej $20 v tokenech pro použití s jakýmkoli AI modelem.", "ctaButton": "Vytvořit bezplatný účet", "manualModeButton": "Použij vlastní API key", "alreadySignedUp": "Už jsi registrovaný?", "loginText": "Přihlas se zde"}, "lowCreditWarning": {"addCredit": "Přidat kredit", "lowBalance": "Tvůj Kilo Code zůstatek je nízký"}, "notifications": {"toolRequest": "Požadavek na nástroj čeká na schválení", "browserAction": "<PERSON><PERSON>ce prohlížeče čeká na schválení", "command": "Příkaz čeká na schválení"}, "settings": {"sections": {"mcp": "MCP servery"}, "provider": {"account": "Účet Kilo Code", "apiKey": "Kilo Code API Key", "login": "Přihlásit se do Kilo Code", "logout": "Odhlásit se z Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Povolit čtení velmi velk<PERSON>ch souborů", "description": "<PERSON><PERSON><PERSON> je povoleno, <PERSON>lo <PERSON> provede čtení velmi velkých souborů nebo výstupů MCP i když existuje vysoká pravděpodobnost přetečení kontextového okna (velikost obsahu >80% kontextového okna)."}}, "systemNotifications": {"label": "Povolit systémová oznámení", "description": "<PERSON><PERSON><PERSON> je povoleno, <PERSON><PERSON> bude odesílat systémová oznámení pro důležité události jako dokončení úkolu nebo chyby.", "testButton": "Otestovat ozná<PERSON>í", "testTitle": "Kilo Code", "testMessage": "Toto je testovací oznámení z Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code chce zkrátit tvou konverzaci", "condenseConversation": "Zkrátit konverzaci"}}, "newTaskPreview": {"task": "Úkol"}, "profile": {"title": "Profil", "dashboard": "<PERSON><PERSON><PERSON><PERSON>", "logOut": "Odhlásit se", "currentBalance": "AKTUÁLNÍ ZŮSTATEK", "loading": "Načítání..."}, "docs": "Dokumentace", "rules": {"tooltip": "Spravovat Kilo Code Rules a Workflows", "ariaLabel": "Kilo Code Rules", "tabs": {"rules": "Pravid<PERSON>", "workflows": "Pracovní postupy"}, "description": {"rules": "Pravidla ti umožňují poskytnout Kilo Code pokyny, kter<PERSON> by mě<PERSON> do<PERSON>t ve všech režimech a pro všechny prompty. Je to trva<PERSON><PERSON> z<PERSON>ů<PERSON>b, jak zahrno<PERSON> kontext a preference pro všechny konverzace ve tvém pracovním prostoru nebo globálně.", "workflows": "Pracovní postupy jsou připravená šablona pro konverzaci. Pracovní postupy ti mohou umožnit definovat prompty, které často p<PERSON>, a mohou zahrnovat řadu kroků k vedení Kilo Code přes opakující se ú<PERSON>ly, jako je nasazení služby nebo odeslání PR. Pro vyvolání pracovního postupu napiš", "workflowsInChat": "v chatu."}, "sections": {"globalRules": "Globální pravidla", "workspaceRules": "Pravidla pracovního prostoru", "globalWorkflows": "Globální pracovní postupy", "workspaceWorkflows": "Pracovní postupy pracovního prostoru"}, "validation": {"invalidFileExtension": "Povoleno pouze .md, .txt nebo bez přípony souboru"}, "placeholders": {"workflowName": "workflow-name (.md, .txt nebo bez přípony)", "ruleName": "rule-name (.md, .txt nebo bez přípony)"}, "newFile": {"newWorkflowFile": "Nový soubor pracovního postupu...", "newRuleFile": "Nový soubor pravidla..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Zobrazit {{messageType}} (#{{messageNumber}})", "messageTypes": {"browser_action_launch": "spuštění <PERSON>", "browser_action_result": "výsledek akce <PERSON>e", "browser_action": "a<PERSON><PERSON>", "checkpoint_saved": "uložený kontrolní bod", "command": "provedení příkazu", "command_output": "výstup příkazu", "completion_result": "výsledek dokončení", "condense_context": "zkrátit kontext", "error": "chybová zpráva", "followup": "následná otázka", "mcp_server_response": "odpověď MCP serveru", "reasoning": "uvažování AI", "text": "odpověď AI", "tool": "použití n<PERSON>", "unknown": "neznámý typ zprávy", "use_mcp_server": "použití MCP serveru", "user": "zpětná vazba uživatele"}}}, "userFeedback": {"editCancel": "Zrušit", "send": "<PERSON><PERSON><PERSON>", "restoreAndSend": "Obnovit a odeslat"}, "ideaSuggestionsBox": {"newHere": "Nový tady?", "suggestionText": "<suggestionButton><PERSON><PERSON>ni sem</suggestionButton> pro skvě<PERSON><PERSON> ná<PERSON>, pak klikni na <sendIcon /> a sleduj mě, jak dě<PERSON><PERSON><PERSON>rak<PERSON>!", "ideas": {"idea1": "Vytvoř <PERSON>, zářící <PERSON> kouli, která reaguje na pohyby myši. Udělej aplikaci funkční v prohlížeči.", "idea2": "Vytvoř portfoliovou webovou stránku pro Python vývojáře", "idea3": "Vytvoř mockup finanční aplikace v prohlížeči. Pak otestuj, jestli funguje", "idea4": "<PERSON>yt<PERSON>ř ad<PERSON>řovou webovou stránku obsahující aktuálně nejlepší AI modely pro generování videí", "idea5": "<PERSON>yt<PERSON><PERSON> generátor <PERSON>S <PERSON>ů, který exportuje vlastní styly a zobrazuje živý náhled", "idea6": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> o<PERSON><PERSON> obsah při najetí <PERSON>", "idea7": "Vytvoř uklidňující interaktivní hvězdné pole, které se pohybuje s gesty myši"}}}