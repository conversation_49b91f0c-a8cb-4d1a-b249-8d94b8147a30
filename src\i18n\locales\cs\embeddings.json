{"unknownError": "Neznámá chyba", "authenticationFailed": "Vytvoření embeddings selhalo: Autentizace selhala. Zkontroluj prosím svůj API klíč.", "failedWithStatus": "Vytvoření embeddings selhalo po {{attempts}} pokusech: HTTP {{statusCode}} - {{errorMessage}}", "failedWithError": "Vyt<PERSON>ření embeddings selhalo po {{attempts}} pokusech: {{errorMessage}}", "failedMaxAttempts": "Vytvoření embeddings selhalo po {{attempts}} pokusech", "textExceedsTokenLimit": "Text na indexu {{index}} překračuje maximální limit tokenů ({{itemTokens}} > {{maxTokens}}). Přeskakuji.", "rateLimitRetry": "<PERSON><PERSON><PERSON><PERSON> limit <PERSON><PERSON><PERSON><PERSON>, opak<PERSON><PERSON><PERSON> za {{delayMs}}ms (pokus {{attempt}}/{{maxRetries}})", "ollama": {"couldNotReadErrorBody": "<PERSON><PERSON><PERSON> přeč<PERSON>t tělo ch<PERSON>by", "requestFailed": "Požadavek Ollama API selhal se stavem {{status}} {{statusText}}: {{errorBody}}", "invalidResponseStructure": "Neplatná struktura odpovědi z Ollama API: pole \"embeddings\" nebylo nalezeno nebo není polem.", "embeddingFailed": "Ollama embedding sel<PERSON>o: {{message}}", "serviceNotRunning": "Ollama služba neběží na {{baseUrl}}", "serviceUnavailable": "Ollama služba nen<PERSON> (stav: {{status}})", "modelNotFound": "Ollama model nebyl nalezen: {{modelId}}", "modelNotEmbeddingCapable": "Ollama model nepodporuje embeddings: {{modelId}}", "hostNotFound": "Ollama host nebyl nalezen: {{baseUrl}}"}, "scanner": {"unknownErrorProcessingFile": "Neznámá chyba při zpracování souboru {{filePath}}", "unknownErrorDeletingPoints": "Neznámá chyba při ma<PERSON>í bodů pro {{filePath}}", "failedToProcessBatchWithError": "Zpracován<PERSON> dávky selhalo po {{maxRetries}} pokusech: {{errorMessage}}"}, "vectorStore": {"qdrantConnectionFailed": "Připojení k vektorové databázi <PERSON>nt selhalo. Ujisti se prosím, že Qdrant běž<PERSON> a je přístupný na {{qdrantUrl}}. Chyba: {{errorMessage}}"}, "validation": {"authenticationFailed": "Autentizace selhala. Zkontroluj prosím svůj API klíč v nastavení.", "connectionFailed": "Připojení k embedder službě selhalo. Zkontroluj prosím nastavení připojení a ujisti se, že služba běží.", "modelNotAvailable": "Zadaný model <PERSON><PERSON><PERSON>. Zkontroluj prosím konfiguraci modelu.", "configurationError": "Neplatná konfigurace embedderu. Zkontroluj prosím svá nastavení.", "serviceUnavailable": "Embedder služba není <PERSON>. Ujisti se prosím, že běží a je přístupná.", "invalidEndpoint": "Neplatný API endpoint. Zkontroluj prosím konfiguraci URL.", "invalidEmbedderConfig": "Neplatná konfigurace embedderu. Zkontroluj prosím svá nastavení.", "invalidApiKey": "Neplatný API klíč. Zkontroluj prosím konfiguraci API klíče.", "invalidBaseUrl": "Neplatná základní URL. Zkontroluj prosím konfiguraci URL.", "invalidModel": "Neplatný model. Zkontroluj prosím konfiguraci modelu.", "invalidResponse": "Neplatná odpověď od embedder služby. Zkontroluj prosím svou konfiguraci."}, "serviceFactory": {"openAiConfigMissing": "Chybí konfigurace OpenAI pro vytvoření embedderu", "ollamaConfigMissing": "Chybí konfigurace Ollama pro vytvoření embedderu", "openAiCompatibleConfigMissing": "Chybí konfigurace OpenAI Compatible pro vytvoření embedderu", "geminiConfigMissing": "Chybí konfigurace Gemini pro vytvoření embedderu", "invalidEmbedderType": "Nakonfigurov<PERSON> neplatný typ embedderu: {{embedder<PERSON><PERSON>ider}}", "vectorDimensionNotDeterminedOpenAiCompatible": "<PERSON><PERSON>ze určit rozměr vektoru pro model '{{modelId}}' s poskytovatelem '{{provider}}'. Ujisti se prosím, že 'Rozměr Embeddingu' je správně nastaven v nastavení OpenAI-Compatible poskytovatele.", "vectorDimensionNotDetermined": "<PERSON><PERSON><PERSON> určit rozměr vektoru pro model '{{modelId}}' s poskytovatelem '{{provider}}'. Zkontroluj profily modelů nebo konfiguraci.", "qdrantUrlMissing": "Chybí Qdrant URL pro vytvoření vektorového <PERSON>ště", "codeIndexingNotConfigured": "Nelze vytvořit služby: Indexování kódu není správn<PERSON>o"}}