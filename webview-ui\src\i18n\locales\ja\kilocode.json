{"welcome": {"greeting": "Kilo Codeへようこそ！", "introText1": "Kilo Codeは無料のオープンソースAIコーディングエージェントです。", "introText2": "Claude 4 Sonnet、Gemini 2.5 Pro、GPT-4.1、その他450以上の最新のAIモデルと連携します。", "introText3": "無料アカウントを作成して、任意のAIモデルで使用できる$20のトークンを取得しましょう。", "ctaButton": "無料アカウントを作成", "manualModeButton": "自分のAPIキーを使う", "alreadySignedUp": "すでに登録済みですか？", "loginText": "こちらからログイン"}, "lowCreditWarning": {"addCredit": "クレジットを追加", "lowBalance": "Kilo Codeの残高が少なくなっています"}, "notifications": {"toolRequest": "ツールのリクエストが承認待ちです", "browserAction": "ブラウザのアクションが承認待ちです", "command": "コマンドが承認待ちです"}, "settings": {"sections": {"mcp": "MCPサーバー"}, "provider": {"account": "Kilo Code アカウント", "apiKey": "Kilo Code APIキー", "login": "Kilo Codeにログイン", "logout": "<PERSON><PERSON>からログアウト"}, "contextManagement": {"allowVeryLargeReads": {"label": "非常に大きなファイルの読み取りを許可", "description": "有効にすると、Kilo Codeはコンテキストウィンドウをオーバーフローする可能性が高い場合でも、非常に大きなファイルやMCP出力の読み取りを実行します（コンテンツサイズがコンテキストウィンドウの80%以上）。"}}, "systemNotifications": {"label": "システム通知を有効にする", "description": "有効にすると、Kilo Codeはタスクの完了やエラーなどの重要なイベントについてシステム通知を送信します。", "testButton": "通知をテスト", "testTitle": "Kilo Code", "testMessage": "これはKilo Codeからのテスト通知です。"}}, "chat": {"condense": {"wantsToCondense": "Kilo Codeは会話を要約したいと思っています", "condenseConversation": "会話を要約する"}}, "newTaskPreview": {"task": "タスク"}, "profile": {"title": "プロフィール", "dashboard": "ダッシュボード", "logOut": "ログアウト", "currentBalance": "現在の残高", "loading": "読み込み中..."}, "docs": "ドキュメント", "rules": {"tooltip": "Kilo Codeルールとワークフローを管理", "ariaLabel": "<PERSON><PERSON>", "tabs": {"rules": "ルール", "workflows": "ワークフロー"}, "description": {"rules": "ルールを使用すると、Kilo Codeがすべてのモードとすべてのプロンプトで従うべき指示を提供できます。これらは、ワークスペースまたはグローバルですべての会話にコンテキストと設定を含める永続的な方法です。", "workflows": "ワークフローは会話用の準備されたテンプレートです。ワークフローを使用すると、頻繁に使用するプロンプトを定義でき、サービスのデプロイやPRの送信など、反復的なタスクを通じてKilo Codeをガイドする一連のステップを含めることができます。ワークフローを呼び出すには、次のように入力します", "workflowsInChat": "チャットで。"}, "sections": {"globalRules": "グローバルルール", "workspaceRules": "ワークスペースルール", "globalWorkflows": "グローバルワークフロー", "workspaceWorkflows": "ワークスペースワークフロー"}, "validation": {"invalidFileExtension": ".md、.txt、または拡張子なしのみ許可されています"}, "placeholders": {"workflowName": "ワークフロー名（.md、.txt、または拡張子なし）", "ruleName": "ルール名（.md、.txt、または拡張子なし）"}, "newFile": {"newWorkflowFile": "新しいワークフローファイル...", "newRuleFile": "新しいルールファイル..."}}, "taskTimeline": {"tooltip": {"messageTypes": {"browser_action_launch": "ブラウザ起動", "browser_action_result": "ブラウザ操作の結果", "browser_action": "ブラウザアクション", "command": "コマンド実行", "checkpoint_saved": "チェックポイントを保存しました", "command_output": "コマンド出力", "completion_result": "完了結果", "followup": "フォローアップの質問", "reasoning": "AI推論", "condense_context": "文脈を要約", "error": "エラーメッセージ", "mcp_server_response": "MCP サーバーの応答", "text": "AI応答", "use_mcp_server": "MCPサーバーの使用状況", "tool": "ツール使用", "unknown": "不明なメッセージタイプ", "user": "ユーザーフィードバック"}, "clickToScroll": "{{messageType}}を表示 (#{{messageNumber}})"}}, "userFeedback": {"editCancel": "キャンセル", "send": "送信", "restoreAndSend": "復元して送信"}, "ideaSuggestionsBox": {"newHere": "初めてですか？", "suggestionText": "クールなアイデアのために<suggestionButton>ここをクリック</suggestionButton>してから、<sendIcon />をタップして私がマジックを行うのを見てください！", "ideas": {"idea1": "マウスの動きに反応する回転する光る3D球体を作成してください。アプリをブラウザで動作させてください。", "idea2": "Pythonソフトウェア開発者のためのポートフォリオウェブサイトを作成", "idea3": "ブラウザで金融アプリのモックアップを作成。そして動作するかテストする", "idea4": "現在最高のAIビデオ生成モデルを含むディレクトリウェブサイトを作成", "idea5": "カスタムスタイルシートをエクスポートし、ライブプレビューを表示するCSSグラデーションジェネレーターを作成", "idea6": "ホバー時に動的なコンテンツを美しく表示するカードを生成", "idea7": "マウスジェスチャーに合わせて動く落ち着いたインタラクティブな星空を作成"}}}