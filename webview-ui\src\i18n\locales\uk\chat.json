{"greeting": "Що Kilo Code може зробити для тебе?", "task": {"title": "Завдання", "seeMore": "Дивитися більше", "seeLess": "Дивитися менше", "tokens": "Токени:", "cache": "Кеш:", "apiCost": "Вартість API:", "condenseContext": "Інтелектуальне стиснення контексту", "contextWindow": "Д<PERSON><PERSON><PERSON>на контексту:", "closeAndStart": "Закрити завдання і почати нове", "export": "Експортувати історію завдання", "share": "Поділитися завданням", "delete": "Видалити завдання (Shift + Клік для пропуску підтвердження)", "shareWithOrganization": "Поділитися з організацією", "shareWithOrganizationDescription": "Лише члени твоєї організації мають доступ", "sharePublicly": "Поділитися публічно", "sharePubliclyDescription": "Будь-хто з посиланням має доступ", "connectToCloud": "Підключитися до Cloud", "connectToCloudDescription": "Увійдіть до Kilo Code Cloud для обміну завданнями", "sharingDisabledByOrganization": "Обмін вимкнено організацією", "shareSuccessOrganization": "Посилання організації скопійовано до буфера обміну", "shareSuccessPublic": "Публічне посилання скопійовано до буфера обміну"}, "history": {"title": "Історія"}, "unpin": "Відкріпити", "pin": "Закріпити", "retry": {"title": "Повторити", "tooltip": "Спробувати операцію знову"}, "startNewTask": {"title": "Почати нове завдання", "tooltip": "Почати нове завдання"}, "reportBug": {"title": "Повідомити про помилку"}, "proceedAnyways": {"title": "Продовжити все одно", "tooltip": "Продовжити поки виконується команда"}, "save": {"title": "Зберегти", "tooltip": "Зберегти зміни файлу"}, "tokenProgress": {"availableSpace": "Доступний простір: {{amount}} токенів", "tokensUsed": "Використано токенів: {{used}} з {{total}}", "reservedForResponse": "Зарезервовано для відповіді моделі: {{amount}} токенів"}, "reject": {"title": "Від<PERSON><PERSON><PERSON>ити", "tooltip": "Відхилити цю дію"}, "completeSubtaskAndReturn": "Завершити підзавдання і повернутися", "approve": {"title": "Схвалити", "tooltip": "Схвалити цю дію"}, "read-batch": {"approve": {"title": "Схвалити все"}, "deny": {"title": "Відхилити все"}}, "runCommand": {"title": "Виконати команду", "tooltip": "Виконати цю команду"}, "proceedWhileRunning": {"title": "Продовжити під час виконання", "tooltip": "Продовжити незважаючи на попередження"}, "killCommand": {"title": "Зупинити команду", "tooltip": "Зупинити поточну команду"}, "resumeTask": {"title": "Відновити завдання", "tooltip": "Продовжити поточне завдання"}, "terminate": {"title": "Завершити", "tooltip": "Завершити поточне завдання"}, "cancel": {"title": "Скасувати", "tooltip": "Скасувати поточну операцію"}, "scrollToBottom": "Прокрутити до кінця чату", "about": "Створюй, рефактори та налагоджуй код за допомогою AI. Переглянь нашу <DocsLink>документацію</DocsLink>, щоб дізнатися більше.", "onboarding": "Твій список завдань у цьому робочому просторі порожній.", "rooTips": {"boomerangTasks": {"title": "Оркестрація завдань", "description": "Розділяй завдання на менші, керовані частини"}, "stickyModels": {"title": "Липкі моделі", "description": "Кожен режим запам'ятовує твою останню використану модель"}, "tools": {"title": "Інструменти", "description": "Дозволь AI вирішувати проблеми, переглядаючи веб, виконуючи команди та інше"}, "customizableModes": {"title": "Налаштовувані режими", "description": "Спеціалізовані персони зі своєю поведінкою та призначеними моделями"}}, "selectMode": "Вибери режим для взаємодії", "selectApiConfig": "Вибери конфігурацію API", "selectModelConfig": "Вибрати модель", "enhancePrompt": "Покращити запит додатковим контекстом", "enhancePromptDescription": "Кнопка 'Покращити запит' допомагає покращити твій запит, надаючи додатковий контекст, роз'яснення або переформулювання. Спробуй ввести запит тут і натиснути кнопку знову, щоб побачити, як це працює.", "modeSelector": {"title": "Режими", "marketplace": "Ринок режимів", "settings": "Налаштування режимів", "description": "Спеціалізовані персони, які налаштовують поведінку Kilo Code."}, "addImages": "Додати зображення до повідомлення", "sendMessage": "Надіслати повідомлення", "stopTts": "Зупинити озвучування тексту", "typeMessage": "Введи повідомлення...", "typeTask": "Створи, знайди, запитай щось", "addContext": "@ для додавання контексту, / для зміни режимів", "dragFiles": "утримуй shift для перетягування файлів", "dragFilesImages": "утримуй shift для перетягування файлів/зображень", "errorReadingFile": "Помилка читання файлу:", "noValidImages": "Не оброблено жодного дійсного зображення", "separator": "Роздільник", "edit": "Редагувати...", "forNextMode": "для наступного режиму", "apiRequest": {"title": "API запит", "failed": "API запит не вдався", "streaming": "API запит...", "cancelled": "API запит скасовано", "streamingFailed": "Потокова передача API не вдалася"}, "checkpoint": {"initial": "Початкова контрольна точка", "regular": "Контрольна точка", "initializingWarning": "Все ще ініціалізується контрольна точка... Якщо це займає надто багато часу, ти можеш вимкнути контрольні точки в <settingsLink>налаштуваннях</settingsLink> і перезапустити своє завдання.", "menu": {"viewDiff": "Переглянути різницю", "restore": "Відновити контрольну точку", "restoreFiles": "Відновити файли", "restoreFilesDescription": "Відновлює файли твого проекту до знімка, зробленого в цій точці.", "restoreFilesAndTask": "Відновити файли та завдання", "confirm": "Підтвердити", "cancel": "Скасувати", "cannotUndo": "Цю дію не можна скасувати.", "restoreFilesAndTaskDescription": "Відновлює файли твого проекту до знімка, зробленого в цій точці, і видаляє всі повідомлення після цієї точки."}, "current": "Поточна"}, "contextCondense": {"title": "Контекст стиснуто", "condensing": "Стискання контексту...", "errorHeader": "Не вдалося стиснути контекст", "tokens": "токени"}, "instructions": {"wantsToFetch": "Kilo Code хоче отримати детальні інструкції для допомоги з поточним завданням"}, "fileOperations": {"wantsToRead": "Kilo Code хоче прочитати цей файл:", "wantsToReadMultiple": "<PERSON>lo Code хоче прочитати кілька файлів:", "wantsToReadAndXMore": "Kilo Code хоче прочитати цей файл і ще {{count}}:", "wantsToReadOutsideWorkspace": "Kilo Code хоче прочитати цей файл за межами робочого простору:", "didRead": "Kilo Code прочитав цей файл:", "wantsToEdit": "Kilo Code хоче редагувати цей файл:", "wantsToEditOutsideWorkspace": "Kilo Code хоче редагувати цей файл за межами робочого простору:", "wantsToEditProtected": "Kilo Code хоче редагувати захищений файл конфігурації:", "wantsToApplyBatchChanges": "Kilo Code хоче застосувати зміни до кількох файлів:", "wantsToCreate": "Kilo Code хоче створити новий файл:", "wantsToSearchReplace": "Kilo Code хоче шукати та замінити в цьому файлі:", "didSearchReplace": "Kilo Code виконав пошук і заміну в цьому файлі:", "wantsToInsert": "Kilo Code хоче вставити вміст у цей файл:", "wantsToInsertWithLineNumber": "Kilo Code хоче вставити вміст у цей файл на рядку {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code хоче додати вміст в кінець цього файлу:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code хоче переглянути файли верхнього рівня в цій директорії:", "didViewTopLevel": "Kilo Code переглянув файли верхнього рівня в цій директорії:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code хоче переглянути файли верхнього рівня в цій директорії (за межами робочого простору):", "didViewTopLevelOutsideWorkspace": "Kilo Code переглянув файли верхнього рівня в цій директорії (за межами робочого простору):", "wantsToViewRecursive": "Kilo Code хоче рекурсивно переглянути всі файли в цій директорії:", "didViewRecursive": "Kilo Code рекурсивно переглянув всі файли в цій директорії:", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code хоче рекурсивно переглянути всі файли в цій директорії (за межами робочого простору):", "didViewRecursiveOutsideWorkspace": "Kilo Code рекурсивно переглянув всі файли в цій директорії (за межами робочого простору):", "wantsToViewDefinitions": "Kilo Code хоче переглянути назви визначень вихідного коду, що використовуються в цій директорії:", "didViewDefinitions": "Kilo Code переглянув назви визначень вихідного коду, що використовуються в цій директорії:", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code хоче переглянути назви визначень вихідного коду, що використовуються в цій директорії (за межами робочого простору):", "didViewDefinitionsOutsideWorkspace": "Kilo Code переглянув назви визначень вихідного коду, що використовуються в цій директорії (за межами робочого простору):", "wantsToSearch": "Kilo Code хоче шукати в цій директорії <code>{{regex}}</code>:", "didSearch": "Kilo Code шукав у цій директорії <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code хоче шукати в цій директорії (за межами робочого простору) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code шукав у цій директорії (за межами робочого простору) <code>{{regex}}</code>:"}, "codebaseSearch": {"wantsToSearch": "Kilo Code хоче шукати в кодовій базі <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code хоче шукати в кодовій базі <code>{{query}}</code> в <code>{{path}}</code>:", "didSearch": "Знайдено {{count}} результат(ів) для <code>{{query}}</code>:", "resultTooltip": "Оцінка схожості: {{score}} (клікни для відкриття файлу)"}, "commandOutput": "Вивід команди", "response": "Відповідь", "arguments": "Аргументи", "mcp": {"wantsToUseTool": "Kilo Code хоче використати інструмент на сервері MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code хоче отримати доступ до ресурсу на сервері MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code хоче перейти в режим {{mode}}", "wantsToSwitchWithReason": "<PERSON>lo Code хоче перейти в режим {{mode}}, тому що: {{reason}}", "didSwitch": "<PERSON><PERSON> Code перейшов у режим {{mode}}", "didSwitchWithReason": "<PERSON><PERSON> <PERSON> перейшов у режим {{mode}}, тому що: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code хоче створити нове підзавдання в режимі {{mode}}:", "wantsToFinish": "Kilo Code хоче завершити це підзавдання", "newTaskContent": "Інструкції підзавдання", "completionContent": "Підзавдання завершено", "resultContent": "Результати підзавдання", "defaultResult": "Будь ласка, переходь до наступного завдання.", "completionInstructions": "Підзавдання завершено! Ти можеш переглянути результати та запропонувати будь-які виправлення або наступні кроки. Якщо все виглядає добре, підтверди, щоб повернути результат до батьківського завдання."}, "questions": {"hasQuestion": "Kilo Code має питання:"}, "taskCompleted": "Завдання виконано", "error": "Помилка", "diffError": {"title": "Редагування не вдалося"}, "troubleMessage": "Kilo Code має проблеми...", "powershell": {"issues": "Схоже, у тебе проблеми з Windows PowerShell, будь ласка, подивись це"}, "autoApprove": {"title": "Автосхвалення:", "none": "Немає", "description": "Автосхвалення дозволяє Kilo Code виконувати дії без запиту дозволу. Увімкни лише для дій, яким ти повністю довіряєш. Більш детальна конфігурація доступна в <settingsLink>Налаштуваннях</settingsLink>."}, "announcement": {"title": "🎉 Випущено Roo Code {{version}}", "description": "Roo Code {{version}} приносить основні нові функції та покращення на основі твоїх відгуків.", "whatsNew": "Що нового", "feature1": "<bold>Запуск Roo Marketplace</bold>: Мар<PERSON><PERSON>тплейс тепер працює! Відкривай та встановлюй режими та MCP легше, ніж будь-коли.", "feature2": "<bold>Моделі Gemini 2.5</bold>: Додано підтримку нових моделей Gemini 2.5 Pro, Flash та Flash Lite.", "feature3": "<bold>Підтримка файлів Excel та інше</bold>: Додано підтримку файлів Excel (.xlsx) та численні виправлення помилок і покращення!", "hideButton": "Приховати оголошення", "detailsDiscussLinks": "Отримай більше деталей та обговори в <discordLink>Discord</discordLink> та <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Ду<PERSON><PERSON><PERSON>", "seconds": "{{count}}с"}, "followUpSuggest": {"copyToInput": "Копіювати до вводу (так само як shift + клік)", "autoSelectCountdown": "Автовибір через {{count}}с", "countdownDisplay": "{{count}}с"}, "browser": {"rooWantsToUse": "Kilo Code хоче використати браузер:", "consoleLogs": "Логи консолі", "noNewLogs": "(Немає нових логів)", "screenshot": "Знімок екрана браузера", "cursor": "курсор", "navigation": {"step": "Крок {{current}} з {{total}}", "previous": "Попередній", "next": "Наступний"}, "sessionStarted": "Сесія браузера розпочата", "actions": {"title": "Дія перегляду: ", "launch": "Запустити браузер на {{url}}", "click": "Клік ({{coordinate}})", "type": "Ввести \"{{text}}\"", "scrollDown": "Прокрутити вниз", "scrollUp": "Прокрутити вгору", "close": "Закрити браузер"}}, "codeblock": {"tooltips": {"expand": "Розгорнути блок коду", "collapse": "Згорнути блок коду", "enable_wrap": "Увімкнути перенос слів", "disable_wrap": "Вимкнути перенос слів", "copy_code": "Копіювати код"}}, "systemPromptWarning": "ПОПЕРЕДЖЕННЯ: Активне користувацьке перевизначення системного запиту. Це може серйозно порушити функціональність і спричинити непередбачувану поведінку.", "profileViolationWarning": "Поточний профіль порушує налаштування твоєї організації", "shellIntegration": {"title": "Попередження про виконання команди", "description": "Твоя команда виконується без інтеграції оболонки терміналу VSCode. Щоб прибрати це попередження, ти можеш вимкнути інтеграцію оболонки в розділі <strong>Terminal</strong> <settingsLink>налаштувань Kilo Code</settingsLink> або усунути проблеми з інтеграцією терміналу VSCode, використовуючи посилання нижче.", "troubleshooting": "Натисни тут для документації з інтеграції оболонки."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Досягнуто ліміт автосхвалених запитів", "description": "Kilo Code досяг автосхваленого ліміту {{count}} API запит(ів). Хочеш скинути лічильник і продовжити завдання?", "button": "Скинути і продовжити"}}, "indexingStatus": {"ready": "Індекс готовий", "indexing": "Індексування {{percentage}}%", "indexed": "Проіндексовано", "error": "Помилка індексу", "status": "Статус індексу"}, "versionIndicator": {"ariaLabel": "Версія {{version}} - Клікни для перегляду приміток до випуску"}}