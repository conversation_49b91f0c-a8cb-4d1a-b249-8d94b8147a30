{"answers": {"yes": "O<PERSON>", "no": "Non", "cancel": "Annuler", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "Conserver"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "Retour d'information", "description": "Nous aimerions recevoir vos commentaires ou vous aider avec tout problème que vous rencontrez.", "githubIssues": "Signaler un problème sur GitHub", "githubDiscussions": "Rejoindre les discussions GitHub", "discord": "Rejoindre notre communauté Discord", "customerSupport": "Support Client"}, "ui": {"search_placeholder": "Rechercher..."}, "mermaid": {"loading": "Génération du diagramme mermaid...", "render_error": "Impossible de rendre le diagramme", "fixing_syntax": "Correction de la syntaxe Mermaid...", "fix_syntax_button": "Corriger la syntaxe avec l'IA", "original_code": "Code original :", "errors": {"unknown_syntax": "<PERSON><PERSON><PERSON> de syntaxe inconnue", "fix_timeout": "La demande de correction par l'IA a expiré", "fix_failed": "La correction par l'IA a échoué", "fix_attempts": "Échec de la correction de la syntaxe après {{attempts}} tentatives. Dernière erreur : {{error}}", "no_fix_provided": "L'IA n'a pas pu fournir de correction", "fix_request_failed": "La demande de correction a échoué"}, "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "save": "Enregistrer l'image", "viewCode": "Voir le code", "viewDiagram": "Voir le diagramme", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "Code Mermaid"}, "tabs": {"diagram": "Diagramme", "code": "Code"}, "feedback": {"imageCopied": "Image copiée dans le presse-papiers", "copyError": "Erreur lors de la copie de l'image"}}, "file": {"errors": {"invalidDataUri": "Format d'URI de données invalide", "copyingImage": "Erreur lors de la copie de l'image : {{error}}", "openingImage": "Erreur lors de l'ouverture de l'image : {{error}}", "pathNotExists": "Le chemin n'existe pas : {{path}}", "couldNotOpen": "Impossible d'ouvrir le fichier : {{error}}", "couldNotOpenGeneric": "Impossible d'ouvrir le fichier !"}, "success": {"imageDataUriCopied": "URI de données d'image copiée dans le presse-papiers"}}}