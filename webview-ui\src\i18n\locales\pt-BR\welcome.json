{"greeting": "<PERSON><PERSON><PERSON>, eu sou o Kilo Code!", "introduction": "<strong><PERSON>lo Code é o principal agente de codificação autônomo.</strong> Prepare-se para arquitetar, codificar, depurar e aumentar sua produtividade como nunca antes. Para continuar, o Kilo Code necessita de uma chave API.", "notice": "Para começar, esta extensão precisa de um provedor de API.", "start": "Vamos lá!", "routers": {"requesty": {"description": "Seu roteador LLM otimizado", "incentive": "$1 de crédit<PERSON> g<PERSON>"}, "openrouter": {"description": "Uma interface unificada para LLMs"}}, "chooseProvider": "Para fazer sua mágica, o Kilo Code precisa de uma chave API.", "startRouter": "Recomendamos usar um roteador LLM:", "startCustom": "Ou você pode trazer sua própria chave API:", "telemetry": {"title": "Ajude a melhorar o Kilo Code", "anonymousTelemetry": "Envie dados de erros e uso para nos ajudar a corrigir bugs e melhorar a extensão. Nenhum código, prompts ou informação pessoal é enviado.", "changeSettings": "Você sempre pode mudar isso na parte inferior das <settingsLink>configurações</settingsLink>", "settings": "configuraç<PERSON><PERSON>", "allow": "<PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>"}, "importSettings": "Importar configurações"}