{"greeting": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON>!", "introduction": "<strong>Kilo Code to wiodący autonomiczny agent kodowania.</strong> Przygotuj się na projektowanie architektury, kodowanie, debugowanie i zwiększenie produktywności jak nigdy dotąd. <PERSON><PERSON>, Kilo Code wymaga klucza API.", "notice": "<PERSON><PERSON>, to rozszerzenie potrzebuje dostawcy API.", "start": "Zaczynajmy!", "routers": {"requesty": {"description": "Twój zoptymalizowany router LLM", "incentive": "$1 darmowego kredytu"}, "openrouter": {"description": "Ujednolicony interfejs dla LLMs"}}, "chooseProvider": "<PERSON><PERSON>, Kilo Code potrzebuje klucza API.", "startRouter": "Zalecamy korzystanie z routera LLM:", "startCustom": "Lub możesz użyć własnego klucza API:", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON> Kilo <PERSON>", "anonymousTelemetry": "Wyślij dane o błędach i użyciu, aby pomóc nam w naprawianiu błędów i ulepszaniu rozszerzenia. Nigdy nie są wysyłane żadne kody, prompts ani informacje o<PERSON>.", "changeSettings": "Zawsze możesz to zmienić na dole <settingsLink>ustawień</settingsLink>", "settings": "ustawienia", "allow": "Zezwól", "deny": "Odmów"}, "importSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}