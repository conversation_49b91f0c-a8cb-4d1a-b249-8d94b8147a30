{"name": "@roo-code/playwright-e2e", "private": true, "type": "module", "scripts": {"lint": "eslint tests --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "format": "prettier --write tests", "playwright": "npx dotenvx run -f .env.local -- playwright test", "playwright:docker": "node run-docker-playwright.js", "playwright:verbose": "PLAYWRIGHT_VERBOSE_LOGS=true npm run playwright", "clean": "<PERSON><PERSON>f test-results playwright-report .turbo"}, "devDependencies": {"@playwright/test": "^1.53.1", "@roo-code/types": "workspace:^", "@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "^22.15.29", "@vscode/test-electron": "^2.4.0", "dotenv": "^16.4.5", "rimraf": "^6.0.1", "typescript": "5.8.3"}, "dependencies": {"chalk": "^5.4.1", "fs-extra": "^11.2.0", "signale": "^1.4.0"}}