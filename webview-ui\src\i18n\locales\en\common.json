{"answers": {"yes": "Yes", "no": "No", "cancel": "Cancel", "remove": "Remove", "keep": "Keep"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear your feedback or help with any issues you're experiencing.", "githubIssues": "Report an issue on GitHub", "githubDiscussions": "Join <PERSON> discussions", "discord": "Join our Discord community", "customerSupport": "Customer Support"}, "ui": {"search_placeholder": "Search..."}, "mermaid": {"loading": "Generating mermaid diagram...", "render_error": "Unable to Render Diagram", "fixing_syntax": "Fixing Mermaid syntax...", "fix_syntax_button": "Fix syntax with AI", "original_code": "Original code:", "errors": {"unknown_syntax": "Unknown syntax error", "fix_timeout": "LLM fix request timed out", "fix_failed": "LLM fix failed", "fix_attempts": "Failed to fix syntax after {{attempts}} attempts. Last error: {{error}}", "no_fix_provided": "LLM failed to provide a fix", "fix_request_failed": "Fix request failed"}, "buttons": {"zoom": "Zoom", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "copy": "Copy", "save": "Save Image", "viewCode": "View Code", "viewDiagram": "View Diagram", "close": "Close"}, "modal": {"codeTitle": "Mermaid Code"}, "tabs": {"diagram": "Diagram", "code": "Code"}, "feedback": {"imageCopied": "Image copied to clipboard", "copyError": "Error copying image"}}, "file": {"errors": {"invalidDataUri": "Invalid data URI format", "copyingImage": "Error copying image: {{error}}", "openingImage": "Error opening image: {{error}}", "pathNotExists": "Path does not exist: {{path}}", "couldNotOpen": "Could not open file: {{error}}", "couldNotOpenGeneric": "Could not open file!"}, "success": {"imageDataUriCopied": "Image data URI copied to clipboard"}}}