{"greeting": "وش يقدر Kilo Code يسوّي لك؟", "task": {"title": "المهمة", "seeMore": "عر<PERSON> المزيد", "seeLess": "<PERSON>ر<PERSON> أقل", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API Cost:", "condenseContext": "تلخيص السياق بذكاء", "contextWindow": "طول السياق:", "closeAndStart": "أغلق المهمة وابدأ جديدة", "export": "تصدير سجل المهمة", "share": "شارك المهمة", "delete": "احذف المهمة (Shift + انقر لتجاوز التأكيد)", "shareWithOrganization": "مشاركة مع المنظمة", "shareWithOrganizationDescription": "فقط أعضاء منظمتك يقدرون الوصول", "sharePublicly": "مشاركة للجميع", "sharePubliclyDescription": "أي أحد عنده الرابط يقدر يشوف", "connectToCloud": "اتصل بالسحابة", "connectToCloudDescription": "سجّل دخولك لـ Kilo Code Cloud لمشاركة المهام", "sharingDisabledByOrganization": "المشاركة معطّلة من قِبَل المنظمة", "shareSuccessOrganization": "تم نسخ رابط المنظمة للحافظة", "shareSuccessPublic": "تم نسخ الرابط العام للحافظة"}, "history": {"title": "السجل"}, "unpin": "إلغاء التثبيت", "pin": "تثبيت", "retry": {"title": "<PERSON><PERSON><PERSON> المحاولة", "tooltip": "حاول العملية من جديد"}, "startNewTask": {"title": "ابدأ مهمة جديدة", "tooltip": "إنشاء مهمة جديدة"}, "reportBug": {"title": "أ<PERSON><PERSON><PERSON> عن خطأ"}, "proceedAnyways": {"title": "استمر على أي حال", "tooltip": "تابع أثناء تنفيذ الأمر"}, "save": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "احفظ تغييرات الملف"}, "tokenProgress": {"availableSpace": "Available space: {{amount}} tokens", "tokensUsed": "Tokens used: {{used}} of {{total}}", "reservedForResponse": "Reserved for model response: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "ارفض هذا الإجراء"}, "completeSubtaskAndReturn": "إنهاء المهمة الفرعية والعودة", "approve": {"title": "موافقة", "tooltip": "وافق على هذا الإجراء"}, "read-batch": {"approve": {"title": "الموافقة على الكل"}, "deny": {"title": "<PERSON><PERSON><PERSON> ال<PERSON>ل"}}, "runCommand": {"title": "شغّل الأمر", "tooltip": "نفّذ هذا الأمر"}, "proceedWhileRunning": {"title": "تابع أثناء التشغيل", "tooltip": "استمر رغم التحذيرات"}, "killCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> الأمر", "tooltip": "أو<PERSON><PERSON> الأ<PERSON>ر الجاري"}, "resumeTask": {"title": "استئناف المهمة", "tooltip": "تابع المهمة الحالية"}, "terminate": {"title": "إنهاء", "tooltip": "إنهاء المهمة الحالية"}, "cancel": {"title": "إلغاء", "tooltip": "إلغاء العملية الحالية"}, "scrollToBottom": "انزل لنهاية المحادثة", "about": "أنشئ، أعِد هيكلة، واصلِح الأكواد بمساعدة الذكاء. اطّلع على <DocsLink>التوثيق</DocsLink> لتعرف أكثر.", "onboarding": "قائمة المهام فاضية في هالمساحة.", "rooTips": {"boomerangTasks": {"title": "تنظيم المهام", "description": "قسّم المهمة لأجزاء أصغر يسهل إدارتها"}, "stickyModels": {"title": "نماذج ثابتة", "description": "كل نمط يتذكّر آخر نموذج استخدمته"}, "tools": {"title": "الأدوات", "description": "خلّ الذكاء يحل المشاكل بتصفح الويب، تشغيل الأوامر، وغيرها"}, "customizableModes": {"title": "أنماط قابلة للتخصيص", "description": "شخصيات متخصصة لها سلوكها ونماذجها الخاصة"}}, "selectMode": "اختر نمط التفاعل", "selectApiConfig": "اختر إعدادات API", "selectModelConfig": "اختر النموذج", "enhancePrompt": "حسّن الموجه بإضافة سياق", "modeSelector": {"title": "الأنماط", "marketplace": "سوق الأنماط", "settings": "إعدادات الأنماط", "description": "شخصيات متخصصة تضبط سلوك Roo."}, "enhancePromptDescription": "زر «حسّن الموجه» يطوّر موجهك بإضافة سياق أو توضيح أو إعادة صياغة. جرّب اكتب موجه هنا ثم اضغط الزر مرة ثانية وشوف النتيجة.", "addImages": "أضف صور للرسالة", "sendMessage": "أرسل الرسالة", "stopTts": "أو<PERSON><PERSON> النطق الآلي", "typeMessage": "اكتب رسالة...", "typeTask": "ابنِ، ابحث، اسأل عن شي", "addContext": "@ لإضافة سياق، / للتبديل بين الأنماط", "dragFiles": "اضغط Shift واسحب الملفات", "dragFilesImages": "اضغط Shift واسحب الملفات/الصور", "errorReadingFile": "خطأ في قراءة الملف:", "noValidImages": "ما تمت معالجة أي صور صالحة", "separator": "فاصل", "edit": "تحرير...", "forNextMode": "للنمط التالي", "apiRequest": {"title": "طلب API", "failed": "فشل طلب API", "streaming": "جاري طلب API...", "cancelled": "تم إلغاء طلب API", "streamingFailed": "فشل بث API"}, "checkpoint": {"initial": "نقطة حفظ أولية", "regular": "نقطة حفظ", "initializingWarning": "لا يزال يتم إنشاء نقطة الحفظ... إذا طولت، تقدر تطفّي نقاط الحفظ من <settingsLink>الإعدادات</settingsLink> وتعيد تشغيل المهمة.", "menu": {"viewDiff": "عر<PERSON> الفرق", "restore": "استرجاع نقطة الحفظ", "restoreFiles": "استرجاع الملفات", "restoreFilesDescription": "يعيد ملفات مشروعك لنسخة محفوظة عند هذي النقطة.", "restoreFilesAndTask": "استرجاع الملفات والمهمة", "confirm": "تأكيد", "cancel": "إلغاء", "cannotUndo": "هذا الإجراء ما تقدر تتراجع عنه.", "restoreFilesAndTaskDescription": "يعيد ملفات مشروعك لنسخة محفوظة عند هذي النقطة ويحذف كل الرسائل بعدها."}, "current": "الحالية"}, "contextCondense": {"title": "تم تلخيص السياق", "condensing": "جاري تلخيص السياق...", "errorHeader": "فشل تلخيص السياق", "tokens": "توكينات"}, "instructions": {"wantsToFetch": "Kilo Code يريد جلب تعليمات تفصيلية للمهمة الحالية"}, "fileOperations": {"wantsToRead": "Kilo Code يريد قراءة هذا الملف:", "wantsToReadMultiple": "Kilo Code يريد قراءة عدة ملفات:", "wantsToReadAndXMore": "Kilo Code يريد قراءة هذا الملف و{{count}} غيره:", "wantsToReadOutsideWorkspace": "Kilo Code يريد قراءة ملف خارج مساحة العمل:", "didRead": "Kilo Code قرأ هذا الملف:", "wantsToEdit": "Kilo Code يريد تعديل هذا الملف:", "wantsToEditOutsideWorkspace": "Kilo Code يريد تعديل ملف خارج مساحة العمل:", "wantsToEditProtected": "Kilo Code يريد تعديل ملف إعدادات محمي:", "wantsToApplyBatchChanges": "Kilo Code يريد تطبيق تغييرات على عدة ملفات:", "wantsToCreate": "Kilo Code يريد إنشاء ملف جديد:", "wantsToSearchReplace": "Kilo Code يريد البحث والاستبدال في هذا الملف:", "didSearchReplace": "Kilo Code أجرى بحثًا واستبدالًا في هذا الملف:", "wantsToInsert": "<PERSON>lo Code يريد إدراج محتوى في هذا الملف:", "wantsToInsertWithLineNumber": "Kilo Code يريد إدراج محتوى في هذا الملف عند السطر {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code يريد إضافة محتوى في نهاية هذا الملف:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code يريد عرض الملفات العلوية في هذا المجلد:", "didViewTopLevel": "Kilo Code عرض الملفات العلوية في هذا المجلد:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code يريد عرض الملفات العلوية في هذا المجلد (خارج مساحة العمل):", "didViewTopLevelOutsideWorkspace": "Kilo Code عرض الملفات العلوية في هذا المجلد (خارج مساحة العمل):", "wantsToViewRecursive": "Kilo Code يريد استعراض كل الملفات في هذا المجلد بشكل متعمق:", "didViewRecursive": "Kilo Code استعرض كل الملفات في هذا المجلد:", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code يريد استعراض كل الملفات في هذا المجلد (خارج مساحة العمل):", "didViewRecursiveOutsideWorkspace": "Kilo Code استعرض كل الملفات في هذا المجلد (خارج مساحة العمل):", "wantsToViewDefinitions": "Kilo Code يريد عرض أسماء التعريفات البرمجية المستخدمة في هذا المجلد:", "didViewDefinitions": "Kilo Code عرض أسماء التعريفات البرمجية المستخدمة في هذا المجلد:", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code يريد عرض أسماء التعريفات في هذا المجلد (خارج مساحة العمل):", "didViewDefinitionsOutsideWorkspace": "Kilo Code عرض أسماء التعريفات في هذا المجلد (خارج مساحة العمل):", "wantsToSearch": "Kilo Code يريد البحث في هذا المجلد عن <code>{{regex}}</code>:", "didSearch": "Kilo Code بحث في هذا المجلد عن <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code يريد البحث في هذا المجلد (خارج مساحة العمل) عن <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code بحث في هذا المجلد (خارج مساحة العمل) عن <code>{{regex}}</code>:"}, "codebaseSearch": {"wantsToSearch": "Kilo Code يريد البحث في الكود عن <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code يريد البحث في الكود عن <code>{{query}}</code> في <code>{{path}}</code>:", "didSearch": "تم العثور على {{count}} نتيجة لـ <code>{{query}}</code>:", "resultTooltip": "درجة التشابه: {{score}} (انقر لفتح الملف)"}, "commandOutput": "مخرجات الأمر", "response": "الاستجابة", "arguments": "المعطيات", "mcp": {"wantsToUseTool": "Kilo Code يريد استخدام أداة على خادم MCP {{serverName}}:", "wantsToAccessResource": "Kilo Code يريد الوصول إلى مورد على خادم MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Kilo Code يريد التبديل إلى النمط {{mode}}", "wantsToSwitchWithReason": "Kilo Code يريد التبديل إلى النمط {{mode}} لأن: {{reason}}", "didSwitch": "Kilo Code انتقل إلى النمط {{mode}}", "didSwitchWithReason": "Kilo Code انتقل إلى النمط {{mode}} لأن: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code يريد إنشاء مهمة فرعية جديدة في النمط {{mode}}:", "wantsToFinish": "Kilo Code يريد إنهاء هذه المهمة الفرعية", "newTaskContent": "تعليمات المهمة الفرعية", "completionContent": "المهمة الفرعية انتهت", "resultContent": "نتائج المهمة الفرعية", "defaultResult": "يرجى الانتقال للمهمة التالية.", "completionInstructions": "تمت المهمة الفرعية! راجع النتائج واقترح تصحيحات أو خطوات لاحقة. إذا كل شي تمام، أكّد للرجوع للمهمة الأم."}, "questions": {"hasQuestion": "Kilo Code عنده سؤال:"}, "taskCompleted": "المهمة انتهت", "error": "خطأ", "diffError": {"title": "التعديل لم ينجح"}, "troubleMessage": "Kilo Code يواجه مشكلة...", "powershell": {"issues": "يبدو أنك تواجه مشاكل مع Windows PowerShell، اطلع هنا"}, "autoApprove": {"title": "الموافقة التلقائية:", "none": "لا شيء", "description": "الموافقة التلقائية تسمح لـ Kilo Code بتنفيذ إجراءات دون طلب إذن. فعّلها فقط للإجراءات اللي تثق فيها تماماً. إعدادات تفصيلية أكثر متوفرة في <settingsLink>الإعدادات</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} تم إطلاقه", "description": "Roo Code {{version}} يجلب ميزات وتحسينات كبيرة بناءً على ملاحظاتكم.", "whatsNew": "الجديد", "feature1": "<bold>إطلاق سوق Roo</bold>: السوق صار متاح! اكتشف وثبّت الأنماط و MCPs بسهولة.", "feature2": "<bold>نماذج Gemini 2.5</bold>: دعم نماذج Gemini 2.5 Pro و Flash و Flash Lite.", "feature3": "<bold>دعم ملفات Excel والمزيد</bold>: دعم ملفات .xlsx بالإضافة لكثير من الإصلاحات والتحسينات!", "hideButton": "إخفاء الإعلان", "detailsDiscussLinks": "للتفاصيل والنقاش زرونا في <discordLink>ديسكورد</discordLink> و <redditLink>ريديت</redditLink> 🚀"}, "reasoning": {"thinking": "يفكّر", "seconds": "{{count}}ث"}, "followUpSuggest": {"copyToInput": "انسخ إلى الإدخال (Shift + انقر)", "autoSelectCountdown": "سيتم الاختيار تلقائياً خلال {{count}}ث", "countdownDisplay": "{{count}}ث"}, "browser": {"rooWantsToUse": "Kilo Code يريد استخدام المتصفح:", "consoleLogs": "سجلات الكونسول", "noNewLogs": "(لا توجد سجلات جديدة)", "screenshot": "لقطة شاشة للمتصفح", "cursor": "المؤشر", "navigation": {"step": "خطوة {{current}} من {{total}}", "previous": "السابق", "next": "التالي"}, "sessionStarted": "بدأت جلسة التصفح", "actions": {"title": "إجراء تصفح: ", "launch": "فتح المتصفح على {{url}}", "click": "نقرة ({{coordinate}})", "type": "كتابة \"{{text}}\"", "scrollDown": "تمرير لأسفل", "scrollUp": "تمرير لأعلى", "close": "إغلاق المتصفح"}}, "codeblock": {"tooltips": {"expand": "توسيع كتلة الكود", "collapse": "طي كتلة الكود", "enable_wrap": "تفعيل لف الكلمات", "disable_wrap": "إيقاف لف الكلمات", "copy_code": "نسخ الكود"}}, "systemPromptWarning": "تحذير: يوجد تجاوز مخصص للموجه النظامي. قد يسبب مشاكل غير متوقعة.", "profileViolationWarning": "الملف الحالي يخالف إعدادات منظمتك", "shellIntegration": {"title": "تحذير تنفيذ الأوامر", "description": "يتم تنفيذ الأمر خارج تكامل الشل في VS Code. لإيقاف هذا التحذير عطّل تكامل الشل من قسم <strong>Terminal</strong> في <settingsLink>إعدادات Kilo Code</settingsLink> أو اطّلع على الدليل أدناه.", "troubleshooting": "اضغط هنا لدليل تكامل الشل."}, "ask": {"autoApprovedRequestLimitReached": {"title": "تم بلوغ حد الطلبات الموافق عليها تلقائياً", "description": "Kilo Code وصل لحد {{count}} طلب API موافَق عليه تلقائيًا. تبي تعيد العداد وتكمل المهمة؟", "button": "إعادة الضبط والمتابعة"}}, "indexingStatus": {"ready": "الفهرس جاهز", "indexing": "جاري الفهرسة {{percentage}}%", "indexed": "تمت الفهرسة", "error": "خطأ في الفهرسة", "status": "حالة الفهرسة"}, "versionIndicator": {"ariaLabel": "الإصدار {{version}} - انقر لعرض ملاحظات الإصدار"}}