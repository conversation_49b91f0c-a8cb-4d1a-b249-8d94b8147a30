{"title": "Re<PERSON><PERSON><PERSON>", "done": "Hotovo", "modes": {"title": "Re<PERSON><PERSON><PERSON>", "createNewMode": "Vytvořit nový režim", "importMode": "Import<PERSON><PERSON>", "editModesConfig": "Upravit konfiguraci re<PERSON>", "editGlobalModes": "Upravit Global Modes", "editProjectModes": "Upravit Project Modes (.kilocodemodes)", "createModeHelpText": "Re<PERSON><PERSON><PERSON> jsou specializova<PERSON><PERSON>, kter<PERSON> p<PERSON>sobují chování <PERSON>lo <PERSON>. <0>Zjisti více o Používání režimů</0> nebo <1>Přizpůsobení režim<PERSON>.</1>", "selectMode": "Hledat režimy", "noMatchFound": "<PERSON><PERSON><PERSON><PERSON>"}, "apiConfiguration": {"title": "Konfigurace API", "select": "<PERSON><PERSON><PERSON>, kterou konfiguraci API použít pro tento režim"}, "tools": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtInModesText": "Nástroje pro vestavěné režimy nelze upravovat", "editTools": "Upravit nástroje", "doneEditing": "Dokončit úpravy", "allowedFiles": "Po<PERSON><PERSON><PERSON> soubory:", "toolNames": {"read": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON> soubory", "browser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command": "Spustit příkazy", "mcp": "Použít MCP"}, "noTools": "<PERSON><PERSON><PERSON><PERSON>"}, "roleDefinition": {"title": "Definice role", "resetToDefault": "Obnovit výchozí", "description": "Definuj odbornost a osobnost Kilo Code pro tento režim. Tento popis formuje, jak se Ki<PERSON> Code prezentuje a jak přistupuje k úkolům."}, "description": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (pro lidi)", "resetToDefault": "Obnovit na výchozí popis", "description": "Stručný popis zobrazený v rozbalovacím menu pro výběr režimu."}, "whenToUse": {"title": "<PERSON><PERSON> (volitelné)", "description": "<PERSON><PERSON><PERSON>, kdy by m<PERSON><PERSON> b<PERSON>t tento režim použit. To pomáhá Orchestrátoru vybrat správný režim pro úkol.", "resetToDefault": "Obnovit výchozí popis '<PERSON><PERSON>'"}, "customInstructions": {"title": "Vlastní instrukce pro režim (volitelné)", "resetToDefault": "Obnovit výchozí", "description": "Přidej pokyny pro chování specifické pro režim {{modeName}}.", "loadFromFile": "Vlastní instrukce specifické pro režim {{mode}} lze také načíst ze složky <span>.kilocode/rules/</span> ve tvém pracovním prostoru (.kilocoderules-{{slug}} je zastaralé a brzy přestane fungovat)."}, "exportMode": {"title": "<PERSON>rt<PERSON><PERSON>", "description": "Exportuj tento režim s pravidly ze složky .kilocode/rules-{{slug}}/ zkombinovanými do sdílitelného YAML souboru. Původní soubory zůstanou nezměněny.", "exporting": "Exportování..."}, "importMode": {"selectLevel": "<PERSON><PERSON><PERSON>, kam importovat tento režim:", "import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importing": "Importování...", "global": {"label": "Globální úroveň", "description": "Dostupné napříč všemi projekty. Pokud exportovaný režim obsahoval soubory pravidel, budou znovu vytvořeny v globální složce .kilocode/rules-{slug}/."}, "project": {"label": "Úroveň projektu", "description": "Dostupné pouze v tomto pracovním prostoru. Pokud exportovaný režim obsahoval soubory pravidel, budou znovu vytvořeny ve složce .kilocode/rules-{slug}/."}}, "advanced": {"title": "Pokročilé: Přepsat System Prompt"}, "globalCustomInstructions": {"title": "Vlastní instrukce pro všechny režimy", "description": "Tyto instrukce platí pro všechny režimy. Poskytují základní sadu chován<PERSON>, k<PERSON><PERSON> může být rozšířena instrukcemi specifickými pro režim níže. <0>Zjistit více</0>", "loadFromFile": "Instrukce lze také načíst ze složky <span>.kilocode/rules/</span> ve tvém pracovním prostoru (.kilocoderules je zastaralé a brzy přestane fungovat)."}, "systemPrompt": {"preview": "Náhled System Prompt", "copy": "Zkopírovat system prompt do <PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "System Prompt (re<PERSON><PERSON> {{modeName}})"}, "supportPrompts": {"title": "Podpůrn<PERSON>", "resetPrompt": "Obnovit {{promptType}} prompt na výchozí", "prompt": "Prompt", "enhance": {"apiConfiguration": "Konfigurace API", "apiConfigDescription": "Můžeš vybrat konfiguraci API, která se bude vždy používat pro vylepšování promptů, nebo jen použ<PERSON> to, co je aktuálně vybráno", "useCurrentConfig": "Použít aktuálně vybranou konfiguraci API", "testPromptPlaceholder": "<PERSON><PERSON><PERSON> prompt pro otest<PERSON><PERSON><PERSON> vylepšení", "previewButton": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>š<PERSON>í promptu", "testEnhancement": "Otestovat vylepšení"}, "types": {"ENHANCE": {"label": "Vylepšit Prompt", "description": "Použij vylepšení promptu k získání přizpůsobených návrhů nebo vylepšení pro tvé vstupy. To zaji<PERSON><PERSON>u<PERSON>, že Kilo Code rozumí tvému záměru a poskytuje nejlepší možné odpovědi. Dostupné přes ikonu ✨ v chatu."}, "EXPLAIN": {"label": "Vysvětlit kód", "description": "Získej podrobná vysvětlení <PERSON> kódu, funkcí nebo celých souborů. Užitečné pro pochopení složitého kódu nebo učení se nových vzorů. Dostupné v akcích kódu (ikona žárovky v editoru) a kontextové nabídce editoru (pravé tlačítko na vybraném kódu)."}, "FIX": {"label": "Opravit problé<PERSON>", "description": "Získej pomoc s identifikací a řešením chyb, errorů nebo problémů s kvalitou kódu. Poskytuje krok za krokem návod k opravě problémů. Dostupné v akcích kódu (ikona žárovky v editoru) a kontextové nabídce editoru (pravé tlačítko na vybraném kódu)."}, "IMPROVE": {"label": "<PERSON>ylep<PERSON><PERSON> kód", "description": "Získej návrhy na optimalizaci kódu, lepš<PERSON> postupy a architektonická vylepšení při zachování funkčnosti. Dostupné v akcích kódu (ikona žárovky v editoru) a kontextové nabídce editoru (pravé tlačítko na vybraném kódu)."}, "ADD_TO_CONTEXT": {"label": "Přidat do kontextu", "description": "Přidej kontext k tvému aktuálnímu úkolu nebo konverzaci. Užitečné pro poskytnutí dalších informací nebo vyjasnění. Dostupné v akcích kódu (ikona žárovky v editoru) a kontextové nabídce editoru (pravé tlačítko na vybraném kódu)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Přidat obsah terminálu do kontextu", "description": "Přidej výstup terminálu k tvému aktuálnímu úkolu nebo konverzaci. Užitečné pro poskytnutí výstupů příkazů nebo logů. Dostupné v kontextové nabídce terminálu (pravé tlačítko na vybraném obsahu terminálu)."}, "TERMINAL_FIX": {"label": "Opravit příkaz terminálu", "description": "Získej pomoc s opravou příkazů termin<PERSON>lu, k<PERSON><PERSON> se<PERSON>haly nebo potřebují vylepšení. Dostupné v kontextové nabídce terminálu (pravé tlačítko na vybraném obsahu terminálu)."}, "TERMINAL_EXPLAIN": {"label": "Vysvětlit příkaz terminálu", "description": "Získej podrobná vysvětlení příkazů terminálu a jejich výstupů. Dostupné v kontextové nabídce terminálu (pravé tlačítko na vybraném obsahu terminálu)."}, "NEW_TASK": {"label": "Začít nový úkol", "description": "Začni nový úkol se vstupem uživatele. Dostupné v paletě příkazů."}, "COMMIT_MESSAGE": {"label": "Generování zprávy commitu", "description": "Generuj popisné zprávy commitu na základě tv<PERSON><PERSON> staged git změn. Přizpůsob prompt pro gene<PERSON><PERSON><PERSON> z<PERSON>r<PERSON><PERSON>, k<PERSON><PERSON> do<PERSON><PERSON><PERSON><PERSON> nejlepší postupy tvého repozitáře."}}}, "advancedSystemPrompt": {"title": "Pokročilé: Přepsat System Prompt", "description": "<2>⚠️ Varování:</2> <PERSON>to pokročilá funkce obchází bezpečnostní opatření. <1>PŘEČTI SI TOTO PŘED POUŽITÍM!</1>Přepiš výchozí system prompt vytvořením souboru v <span>.kilocode/system-prompt-{{slug}}</span>."}, "createModeDialog": {"title": "Vytvořit nový režim", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON>adej n<PERSON>zev <PERSON>"}, "slug": {"label": "Slug", "description": "Slug se používá v URL a názvech souborů. Měl by b<PERSON>t malými písmeny a obsahovat pouze písmena, čísla a pomlčky."}, "saveLocation": {"label": "Umístění <PERSON>", "description": "<PERSON><PERSON><PERSON>, kam uložit tento režim. Režimy specifické pro projekt mají přednost před globálními.", "global": {"label": "Globální", "description": "Dostupné ve všech pracovních prostorech"}, "project": {"label": "Specifické pro projekt (.kilocodemodes)", "description": "Dostupné pouze v tomto pracovním prostoru, má přednost před globálním"}}, "roleDefinition": {"label": "Definice role", "description": "Definuj odbornost a osobnost Kilo Code pro tento režim."}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (pro lidi)", "description": "Stručný popis zobrazený v rozbalovacím menu pro výběr režimu."}, "whenToUse": {"label": "<PERSON><PERSON> (volitelné)", "description": "Poskytni jasný popis, kdy je tento režim nej<PERSON>činnější a v jakých typech úkolů vyniká."}, "tools": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, které nástroje může tento režim p<PERSON>."}, "customInstructions": {"label": "Vlastn<PERSON> instrukce (volitelné)", "description": "Přidej pokyny pro chování specifické pro tento režim."}, "buttons": {"cancel": "Zrušit", "create": "Vytvořit rež<PERSON>"}, "deleteMode": "<PERSON><PERSON><PERSON><PERSON>"}, "allFiles": "všechny soubory", "deleteMode": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Oprav<PERSON> <PERSON><PERSON><PERSON> s<PERSON>t re<PERSON> \"{{modeName}}\"?", "rulesFolder": "<PERSON>to režim má složku pravidel v {{folderPath}}, k<PERSON><PERSON> bude také s<PERSON>.", "descriptionNoRules": "Opravdu ch<PERSON>š smazat tento vlastní režim?", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Zrušit"}}