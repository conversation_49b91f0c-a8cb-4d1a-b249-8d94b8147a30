{"extension.displayName": "Kilo Code AI Agent (Συνδυασμένα χαρακτηριστικά Cline / Roo)", "extension.description": "Β<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικοποίησης AI ανοιχτού κώδικα για σχεδιασμό, δημιουργία και διόρθωση κώδικα.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "Νέα Εργασία", "command.mcpServers.title": "Διακ<PERSON><PERSON>ιστές MCP", "command.prompts.title": "Λειτουργ<PERSON>ες", "command.history.title": "Ιστορικό", "command.marketplace.title": "Αγορά", "command.openInEditor.title": "Άνοιγμα στον Επεξεργαστή", "command.settings.title": "Ρυθμίσεις", "command.documentation.title": "Τεκμηρίωση", "command.openInNewTab.title": "Άνοιγμα σε Νέα Καρτέλα", "command.explainCode.title": "Εξήγηση Κώδικα", "command.fixCode.title": "Διόρθωση Κώδικα", "command.improveCode.title": "Βελτίωση Κώδικα", "command.addToContext.title": "Προσθήκη στο Πλαίσιο", "command.focusInput.title": "Εστίαση στο Πεδίο Εισαγωγής", "command.setCustomStoragePath.title": "Ορισμός Προσαρμοσμένης Διαδρομής Αποθήκευσης", "command.terminal.addToContext.title": "Προσθήκη Περιεχομένου Τερματικού στο Πλαίσιο", "command.terminal.fixCommand.title": "Διόρθωση Αυτής της Εντολής", "command.terminal.explainCommand.title": "Εξήγηση Αυτής της Εντολής", "command.acceptInput.title": "Αποδοχή Εισαγωγής/Πρότασης", "command.generateCommitMessage.title": "Δημιουργ<PERSON>α Μηνύματος Commit με το <PERSON>lo", "command.profile.title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "Εντολές που μπορούν να εκτελεστούν αυτόματα όταν είναι ενεργοποιημένο το 'Always approve execute operations'", "settings.vsCodeLmModelSelector.description": "Ρυθμίσεις για το VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Ο προμηθευτής του μοντέλου γλώσσας (π.χ. copilot)", "settings.vsCodeLmModelSelector.family.description": "Η οικογένεια του μοντέλου γλώσσας (π.χ. gpt-4)", "settings.customStoragePath.description": "Προσαρμοσμένη διαδρομή αποθήκευσης. Αφήστε κενό για να χρησιμοποιήσετε την προεπιλεγμένη τοποθεσία. Υποστηρίζει απόλυτες διαδρομές (π.χ. 'D:\\KiloCodeStorage')", "command.importSettings.title": "Εισαγωγή Ρυθμίσεων", "settings.enableCodeActions.description": "Ενεργοποίηση γρήγορων διορθώσεω<PERSON>", "settings.autoImportSettingsPath.description": "Διαδρομή σε αρχείο διαμόρφωσης Kilo Code για αυτόματη εισαγωγή κατά την εκκίνηση της επέκτασης. Υποστηρίζει απόλυτες διαδρομές και διαδρομές σχετικές με τον αρχικό κατάλογο (π.χ. '~/Documents/kilo-code-settings.json'). Αφήστε κενό για απενεργοποίηση της αυτόματης εισαγωγής.", "ghost.input.title": "Πατήστε 'Enter' για επιβεβαίωση ή 'Escape' για ακύρωση", "ghost.input.placeholder": "Περιγράψτε τι θέλετε να κάνετε...", "ghost.commands.generateSuggestions": "Kilo Code: Δημιουργία Προτεινόμενων Επεξεργασιών", "ghost.commands.displaySuggestions": "Εμφάνιση Προτεινόμενων Επεξεργασιών", "ghost.commands.cancelSuggestions": "Ακύρωση Προτεινόμενων Επεξεργασιών", "ghost.commands.applyCurrentSuggestion": "Εφαρμογή Τρέχουσας Προτεινόμενης Επεξεργασίας", "ghost.commands.applyAllSuggestions": "Εφαρμογή Όλων των Προτεινόμενων Επεξεργασιών", "ghost.commands.promptCodeSuggestion": "Γρήγορη Εργασία", "ghost.commands.goToNextSuggestion": "Μετάβαση στην Επόμενη Πρόταση", "ghost.commands.goToPreviousSuggestion": "Μετάβαση στην Προηγούμενη Πρόταση"}