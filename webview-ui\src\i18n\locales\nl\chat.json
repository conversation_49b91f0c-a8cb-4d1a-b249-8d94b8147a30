{"greeting": "Welkom bij Kilo Code", "task": {"title": "<PERSON><PERSON>", "seeMore": "<PERSON><PERSON> weer<PERSON><PERSON>", "seeLess": "<PERSON><PERSON> weer<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-kosten:", "contextWindow": "Contextlengte:", "closeAndStart": "<PERSON><PERSON> sluiten en een nieuwe starten", "export": "Taakgeschiedenis exporteren", "delete": "<PERSON><PERSON> verwijderen (<PERSON><PERSON> + <PERSON><PERSON> om bevestiging over te slaan)", "condenseContext": "Context intelligent <PERSON><PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> delen", "shareWithOrganization": "<PERSON><PERSON> met organisa<PERSON>", "shareWithOrganizationDescription": "<PERSON><PERSON> leden van je organisatie kunnen toegang krijgen", "sharePublicly": "<PERSON><PERSON><PERSON>", "sharePubliclyDescription": "<PERSON><PERSON><PERSON> met de link kan toegang k<PERSON>", "connectToCloud": "<PERSON><PERSON><PERSON><PERSON> met <PERSON>", "connectToCloudDescription": "Meld je aan bij Kilo Code Cloud om taken te delen", "sharingDisabledByOrganization": "Delen uitgeschakeld door organisatie", "shareSuccessOrganization": "Organisatielink gekopieerd naar klembord", "shareSuccessPublic": "Openbare link gekopieerd naar klembord"}, "history": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unpin": "Losmaken", "pin": "Vastmaken", "retry": {"title": "Opnieuw proberen", "tooltip": "<PERSON><PERSON><PERSON> de bewerking opnieuw"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> taak starten", "tooltip": "Begin een nieuwe taak"}, "reportBug": {"title": "<PERSON>ug rapporteren"}, "proceedAnyways": {"title": "<PERSON><PERSON> doorgaan", "tooltip": "Ga door terwijl het commando wordt uitgevoerd"}, "save": {"title": "Opsla<PERSON>", "tooltip": "Bestandswijzigingen opslaan"}, "tokenProgress": {"availableSpace": "Beschikbare ruimte: {{amount}} tokens", "tokensUsed": "Gebruikte tokens: {{used}} van {{total}}", "reservedForResponse": "Gereserveerd voor modelantwoord: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Deze actie weigeren"}, "completeSubtaskAndReturn": "Subtaak voltooien en terugkeren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Deze actie goedkeuren"}, "runCommand": {"title": "Commando uitvoeren", "tooltip": "<PERSON><PERSON>r dit commando uit"}, "proceedWhileRunning": {"title": "Doorgaan tijdens uitvoeren", "tooltip": "Ga door ondanks waarschuwingen"}, "killCommand": {"title": "Commando stoppen", "tooltip": "Huidig commando stoppen"}, "resumeTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON>a door met de huidige taak"}, "terminate": {"title": "Beëindigen", "tooltip": "Beëindig de huidige taak"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> de huidige bewerking"}, "scrollToBottom": "<PERSON><PERSON> naar on<PERSON>aan de chat", "about": "<PERSON><PERSON>, refactor en debug code met AI-assistentie. Bekijk onze <DocsLink>documentatie</DocsLink> voor meer informatie.", "onboarding": "<PERSON> in deze werkruimte is leeg.", "rooTips": {"boomerangTasks": {"title": "<PERSON><PERSON>", "description": "Splits taken op in kleinere, beheers<PERSON>e delen"}, "stickyModels": {"title": "Vastgezette modellen", "description": "Elke modus onthoudt je laatst gebruikte model"}, "tools": {"title": "Tools", "description": "Laat de AI problemen oplossen door te browsen, commando's uit te voeren en meer"}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> modi", "description": "Gespecialiseerde persona's met hun eigen gedrag en toegewezen modellen"}}, "selectMode": "Selecteer modus voor interactie", "selectApiConfig": "Selecteer API-configuratie", "selectModelConfig": "Selecteer model", "enhancePrompt": "Prompt verbeteren met extra context", "enhancePromptDescription": "De knop 'Prompt verbeteren' helpt je prompt te verbeteren door extra context, verduid<PERSON><PERSON>ing of herformulering te bieden. <PERSON>beer hier een prompt te typen en klik opnieuw op de knop om te zien hoe het werkt.", "modeSelector": {"title": "<PERSON><PERSON>", "marketplace": "Modus <PERSON>", "settings": "Modus Instellingen", "description": "Gespecialiseerd<PERSON> persona's die het ged<PERSON> van Kilo Code aanpassen."}, "addImages": "Afbeeldingen toevoegen aan bericht", "sendMessage": "Bericht verzenden", "stopTts": "Stop tekst-naar-spraak", "typeMessage": "Typ een bericht...", "typeTask": "<PERSON>p hier je taak...", "addContext": "@ om context toe te voegen, / om van modus te wisselen", "dragFiles": "houd shift ingedrukt om bestanden te slepen", "dragFilesImages": "houd shift ingedrukt om bestanden/afbeeldingen te slepen", "errorReadingFile": "Fout bij het lezen van bestand:", "noValidImages": "Er zijn geen geldige afbeeldingen verwerkt", "separator": "Scheidingsteken", "edit": "Bewerken...", "forNextMode": "voor volgende modus", "apiRequest": {"title": "API-verzoek", "failed": "API-verz<PERSON>k mis<PERSON>t", "streaming": "API-verzoek...", "cancelled": "API-verz<PERSON>k g<PERSON>", "streamingFailed": "API-streaming mislukt"}, "checkpoint": {"initial": "Initiële checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wordt nog steeds geïnitialiseerd... Als dit te lang duurt, kun je checkpoints uitschakelen in de <settingsLink>instellingen</settingsLink> en je taak opnieuw starten.", "menu": {"viewDiff": "Bekijk verschil", "restore": "Herstel checkpoint", "restoreFiles": "<PERSON><PERSON><PERSON>", "restoreFilesDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt.", "restoreFilesAndTask": "Bestanden & taak herstellen", "confirm": "Bevestigen", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Deze actie kan niet ongedaan worden gemaakt.", "restoreFilesAndTaskDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt en verwijdert alle berichten na dit punt."}, "current": "<PERSON><PERSON><PERSON>"}, "instructions": {"wantsToFetch": "Kilo Code wil gedetail<PERSON>rde instructies op<PERSON>n om te helpen met de huidige taak"}, "fileOperations": {"wantsToRead": "Kilo Code wil dit bestand lezen:", "wantsToReadOutsideWorkspace": "Kilo Code wil dit bestand buiten de werkruimte lezen:", "didRead": "Kilo Code heeft dit bestand gelezen:", "wantsToEdit": "Kilo Code wil dit bestand bewerken:", "wantsToEditOutsideWorkspace": "Kilo Code wil dit bestand buiten de werkruimte bewerken:", "wantsToEditProtected": "Kilo Code wil een beveiligd configuratiebestand bewerken:", "wantsToCreate": "Kilo Code wil een nieuw bestand aanmaken:", "wantsToSearchReplace": "Kilo Code wil zoeken en vervangen in dit bestand:", "didSearchReplace": "Kilo Code heeft zoeken en vervangen uitgevoerd op dit bestand:", "wantsToInsert": "Kilo Code wil inhoud invoegen in dit bestand:", "wantsToInsertWithLineNumber": "Kilo Code wil inhoud invoegen in dit bestand op regel {{lineNumber}}:", "wantsToInsertAtEnd": "Kilo Code wil inhoud toevoegen aan het einde van dit bestand:", "wantsToReadAndXMore": "Kilo Code wil dit bestand en nog {{count}} andere lezen:", "wantsToReadMultiple": "Kilo Code wil meerdere bestanden lezen:", "wantsToApplyBatchChanges": "Kilo Code wil wijzigingen toepassen op meerdere bestanden:"}, "directoryOperations": {"wantsToViewTopLevel": "Kilo Code wil de bovenliggende bestanden in deze map bekijken:", "didViewTopLevel": "Kilo Code heeft de bovenliggende bestanden in deze map bekeken:", "wantsToViewRecursive": "Kilo Code wil alle bestanden in deze map recursief bekijken:", "didViewRecursive": "Kilo Code heeft alle bestanden in deze map recursief bekeken:", "wantsToViewDefinitions": "Kilo Code wil broncode-definitienamen bekijken die in deze map worden gebruikt:", "didViewDefinitions": "Kilo Code heeft broncode-definitienamen bekeken die in deze map worden gebruikt:", "wantsToSearch": "Kilo Code wil deze map doorzoeken op <code>{{regex}}</code>:", "didSearch": "Kilo Code heeft deze map doorzocht op <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Kilo Code wil deze map (buiten werkruimte) doorzoeken op <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Kilo Code heeft deze map (buiten werkruimte) doorzocht op <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Kilo Code wil de bovenliggende bestanden in deze map (buiten werkruimte) bekijken:", "didViewTopLevelOutsideWorkspace": "Kilo Code heeft de bovenliggende bestanden in deze map (buiten werkruimte) bekeken:", "wantsToViewRecursiveOutsideWorkspace": "Kilo Code wil alle bestanden in deze map (buiten werkruimte) recursief bekijken:", "didViewRecursiveOutsideWorkspace": "Kilo Code heeft alle bestanden in deze map (buiten werkruimte) recursief bekeken:", "wantsToViewDefinitionsOutsideWorkspace": "Kilo Code wil broncode-definitienamen bekijken die in deze map (buiten werkruimte) worden gebruikt:", "didViewDefinitionsOutsideWorkspace": "Kilo Code heeft broncode-definitienamen bekeken die in deze map (buiten werkruimte) worden gebruikt:"}, "commandOutput": "Commando-uitvoer", "response": "Antwoord", "arguments": "Argumenten", "mcp": {"wantsToUseTool": "Kilo Code wil een tool gebruiken op de {{serverName}} MCP-server:", "wantsToAccessResource": "Kilo Code wil een bron benaderen op de {{serverName}} MCP-server:"}, "modes": {"wantsToSwitch": "Kilo Code wil overschakelen naar {{mode}} modus", "wantsToSwitchWithReason": "Kilo Code wil overschakelen naar {{mode}} modus omdat: {{reason}}", "didSwitch": "Kilo Code is overgeschakeld naar {{mode}} modus", "didSwitchWithReason": "Kilo Code is overgeschakeld naar {{mode}} modus omdat: {{reason}}"}, "subtasks": {"wantsToCreate": "Kilo Code wil een nieuwe subtaak aanmaken in {{mode}} modus:", "wantsToFinish": "Kilo Code wil deze subtaak voltooien", "newTaskContent": "Subtaak-instructies", "completionContent": "Subtaak voltooid", "resultContent": "Subtaakresultaten", "defaultResult": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met de volgende taak.", "completionInstructions": "Subtaak voltooid! Je kunt de resultaten bekijken en eventuele correcties of volgende stappen voorstellen. Als alles goed is, bevestig dan om het resultaat terug te sturen naar de hoofdtaak."}, "questions": {"hasQuestion": "Kilo Code heeft een vraag:"}, "taskCompleted": "Taak voltooid", "error": "Fout", "diffError": {"title": "Bewerking mislukt"}, "troubleMessage": "Kilo Code ondervindt problemen...", "powershell": {"issues": "Het lijkt erop dat je problemen hebt met Windows PowerShell, zie deze"}, "autoApprove": {"title": "Automatisch goedkeuren:", "none": "<PERSON><PERSON>", "description": "Met automatisch goedkeuren kan Kilo Code acties uitvoeren zonder om toestemming te vragen. <PERSON><PERSON><PERSON> dit alleen in voor acties die je volledig vertrouwt. Meer gedetailleerde configuratie besch<PERSON> in de <settingsLink>Instellingen</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} uitgebracht", "description": "Roo Code {{version}} brengt krachtige nieuwe functies en significante verbeteringen om je ontwikkelingsworkflow te verbeteren.", "whatsNew": "Wat is er nieuw", "feature1": "<bold>Codebase Indexering Afgestudeerd van Experimenteel</bold>: Volledige codebase indexering is nu stabiel en klaar voor productiegebruik met verbeterde zoekfunctionaliteit en contextbegrip.", "feature2": "<bold>Nieuwe Todo Lijst Functie</bold>: <PERSON><PERSON> je taken op koers met ge<PERSON><PERSON>greerd todo-beheer dat je helpt georganiseerd en gefocust te blijven op je ontwikkelingsdoelen.", "feature3": "<bold>Verbeterde Architect naar Code Overgangen</bold>: <PERSON><PERSON><PERSON> van planning in Architect modus naar implementatie in Code modus.", "hideButton": "Aankondiging verbergen", "detailsDiscussLinks": "<PERSON><PERSON><PERSON><PERSON> meer details en doe mee aan discussies op <discordLink>Discord</discordLink> en <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Denkt na", "seconds": "{{count}}s"}, "contextCondense": {"title": "Context same<PERSON>", "condensing": "Context aan het samenvatten...", "errorHeader": "Context same<PERSON><PERSON>ten mislukt", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON><PERSON> naar invoer (zelfde als shift + klik)", "autoSelectCountdown": "Automatische selectie in {{count}}s", "countdownDisplay": "{{count}}s"}, "browser": {"rooWantsToUse": "Kilo Code wil de browser gebruiken:", "consoleLogs": "Console-logboeken", "noNewLogs": "(Geen nieuwe logboeken)", "screenshot": "Browserschermopname", "cursor": "cursor", "navigation": {"step": "Stap {{current}} van {{total}}", "previous": "Vorige", "next": "Volgende"}, "sessionStarted": "<PERSON>rows<PERSON>ssie gestart", "actions": {"title": "Browse-actie: ", "launch": "Browser starten op {{url}}", "click": "<PERSON>lik ({{coordinate}})", "type": "Typ \"{{text}}\"", "scrollDown": "<PERSON><PERSON> naar beneden", "scrollUp": "<PERSON><PERSON> naar boven", "close": "Browser sluiten"}}, "codeblock": {"tooltips": {"expand": "Codeblok uitvouwen", "collapse": "Codeblok <PERSON>wen", "enable_wrap": "Regelafbreking inschakelen", "disable_wrap": "Regelafbreking uitschakelen", "copy_code": "Code kopiëren"}}, "systemPromptWarning": "WAARSCHUWING: Aangepaste systeemprompt actief. Dit kan de functionaliteit ernstig verstoren en onvoorspelbaar gedrag veroorzaken.", "profileViolationWarning": "Het huidige profiel schendt de instellingen van uw organisatie", "shellIntegration": {"title": "Waarschuwing commando-uitvoering", "description": "Je commando wordt uitgevoerd zonder VSCode-terminal shell-integratie. Om deze waarschuwing te onderdrukken kun je shell-integratie uitschakelen in het gedeelte <strong>Terminal</strong> van de <settingsLink>Kilo Code-instellingen</settingsLink> of de VSCode-terminalintegratie oplossen via de onderstaande link.", "troubleshooting": "Klik hier voor shell-integratie documentatie."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limiet voor automatisch goedgekeurde verzoeken bereikt", "description": "Kilo Code heeft de automatisch goedgekeurde limiet van {{count}} API-verzoek(en) bereikt. Wil je de teller resetten en doorgaan met de taak?", "button": "Resetten en doorgaan"}}, "codebaseSearch": {"wantsToSearch": "Kilo Code wil de codebase doorzoeken op <code>{{query}}</code>:", "wantsToSearchWithPath": "Kilo Code wil de codebase doorzoeken op <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch": "{{count}} resultaat/resultaten gevonden voor <code>{{query}}</code>:", "resultTooltip": "Gelijkenisscore: {{score}} (klik om bestand te openen)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON> go<PERSON>"}, "deny": {"title": "Alles weigeren"}}, "indexingStatus": {"ready": "Index gereed", "indexing": "Indexeren {{percentage}}%", "indexed": "Geïndexeerd", "error": "Index fout", "status": "Index status"}, "versionIndicator": {"ariaLabel": "Versie {{version}} - Klik om release notes te bekijken"}}