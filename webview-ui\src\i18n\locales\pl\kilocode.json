{"welcome": {"greeting": "Witaj w Kilo Code!", "introText1": "<PERSON><PERSON> Code to darmowy, otwartoźródłowy agent kodowania AI.", "introText2": "Współpracuje z najnowszymi modelami AI, ta<PERSON><PERSON> jak <PERSON> 4 Sonnet, Gemini 2.5 Pro, GPT-4.1 i ponad 450 innymi.", "introText3": "Stwórz darmowe konto i otrzymaj tokeny o wartości $20 do wykorzystania z dowolnym modelem AI.", "ctaButton": "Stwórz darmowe konto", "manualModeButton": "Użyj własnego klucza API", "alreadySignedUp": "<PERSON><PERSON>strowany?", "loginText": "Zalog<PERSON>j się tutaj"}, "lowCreditWarning": {"addCredit": "<PERSON><PERSON><PERSON>", "lowBalance": "Twój stan konta Kilo <PERSON> jest niski"}, "notifications": {"toolRequest": "Żądanie narzędzia oczekuje na zatwierdzenie", "browserAction": "Akcja przeglądarki oczekuje na zatwierdzenie", "command": "Polecenie oczekuje na zatwierdzenie"}, "settings": {"sections": {"mcp": "Serwery MCP"}, "provider": {"account": "<PERSON><PERSON>", "apiKey": "Klucz API Kilo Code", "login": "Zaloguj się do Kilo Code", "logout": "Wyloguj się z Kilo Code"}, "contextManagement": {"allowVeryLargeReads": {"label": "Zezwól na odczyt bardzo dużych plików", "description": "<PERSON><PERSON>, Kilo Code będzie wykonywać odczyty bardzo dużych plików lub wyjść MCP, nawet jeśli istnieje duże prawdopodobieństwo przepełnienia okna kontekstu (rozmiar zawartości >80% okna kontekstu)."}}, "systemNotifications": {"label": "Włącz powiadomienia systemowe", "description": "<PERSON><PERSON>, Kilo Code będzie wysyłać powiadomienia systemowe o ważnych wydarzeniach, takich jak zakończenie zadania lub błędy.", "testButton": "<PERSON><PERSON><PERSON>ow<PERSON>dom<PERSON>", "testTitle": "Kilo Code", "testMessage": "To jest testowe powiadomienie z Kilo Code."}}, "chat": {"condense": {"wantsToCondense": "Kilo Code chce skondensować Twoją rozmowę", "condenseConversation": "Skondensuj <PERSON>ę"}}, "newTaskPreview": {"task": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Profil", "dashboard": "Panel kontrolny", "logOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentBalance": "AKTUALNY STAN KONTA", "loading": "Ładowanie..."}, "docs": "Dokumentacja", "rules": {"tooltip": "Zarządzaj Regułami i Przepływami Pracy Kilo Code", "ariaLabel": "Reguły Kilo Code", "tabs": {"rules": "Reg<PERSON>ł<PERSON>", "workflows": "Przepływy Pracy"}, "description": {"rules": "Reguły pozwalają dostarczyć Kilo Code instrukcje, których powinien przestrzegać we wszystkich trybach i dla wszystkich promptów. To trwały sposób dołączania kontekstu i preferencji dla wszystkich rozmów w twoim obszarze roboczym lub globalnie.", "workflows": "Przepływy pracy to przygotowany szablon dla rozmowy. Przepływy pracy pozwalają zdefiniować prompty, kt<PERSON><PERSON><PERSON> często używasz, i mogą zawierać serię kroków, aby pop<PERSON><PERSON><PERSON><PERSON> Kilo Code przez powtarzalne zadania, takie jak wdrażanie usługi lub przesyłanie PR. Aby wywołać przepływ pracy, wpisz", "workflowsInChat": "w c<PERSON>ie."}, "sections": {"globalRules": "Reguły Globalne", "workspaceRules": "Reguły Obszaru Roboczego", "globalWorkflows": "Przepływy Pracy Globalne", "workspaceWorkflows": "Przepływy Pracy Obszaru Roboczego"}, "validation": {"invalidFileExtension": "Dozwolone są tylko rozszerzenia .md, .txt lub brak rozszerzenia"}, "placeholders": {"workflowName": "nazwa-przepływu-pracy (.md, .txt lub bez rozszerzenia)", "ruleName": "nazwa-reguły (.md, .txt lub bez rozszerzenia)"}, "newFile": {"newWorkflowFile": "Nowy plik przepływu pracy...", "newRuleFile": "Nowy plik reguły..."}}, "taskTimeline": {"tooltip": {"clickToScroll": "Wyświ<PERSON>l {{messageType}} (nr {{messageNumber}})", "messageTypes": {"browser_action_result": "wynik działania przeglądarki", "browser_action": "akcja przeglądarki", "checkpoint_saved": "punkt kontrolny zapisany", "browser_action_launch": "uruchomienie przeglądarki", "command": "wykonanie pole<PERSON>nia", "command_output": "wynik polecenia", "completion_result": "wynik wykonania", "condense_context": "skondensuj kontekst", "followup": "pytanie uzupełniające", "mcp_server_response": "Odpowiedź serwera MCP", "reasoning": "Rozumowanie AI", "error": "komunikat o błędzie", "unknown": "nieznany typ wia<PERSON>", "text": "Odpowiedź AI", "tool": "użycie narzędzi", "use_mcp_server": "Użycie serwera MCP", "user": "opinia użytkownika"}}}, "userFeedback": {"editCancel": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "restoreAndSend": "Przywróć i Wyślij"}, "ideaSuggestionsBox": {"newHere": "<PERSON><PERSON> tutaj?", "suggestionText": "<suggestionButton><PERSON><PERSON><PERSON><PERSON> tutaj</suggestionButton> dla fajnego p<PERSON>, nast<PERSON><PERSON><PERSON> dotknij <sendIcon /> i zobacz, jak robię magię!", "ideas": {"idea1": "Stw<PERSON>rz o<PERSON> się, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kulę <PERSON>, która reaguje na ruchy myszy. Spraw, aby aplikacja działała w przeglądarce.", "idea2": "Stwórz stronę portfolio dla programisty Python", "idea3": "Stwórz makietę aplikacji finansowej w przeglądarce. Następnie sprawdź, czy działa", "idea4": "St<PERSON><PERSON><PERSON> stron<PERSON> katalogow<PERSON> zawierającą najlepsze obecnie modele generowania wideo AI", "idea5": "Stwórz generator gradientów CSS, który eksportuje niestandardowe arkusze stylów i pokazuje podgląd na żywo", "idea6": "<PERSON>ygene<PERSON><PERSON> kart<PERSON>, które pięknie ujawniają dynamiczną zawarto<PERSON> po najechaniu my<PERSON>ą", "idea7": "St<PERSON><PERSON>rz <PERSON>po<PERSON>jące interaktywne pole gwiazd, które porusza się wraz z gestami myszy"}}}