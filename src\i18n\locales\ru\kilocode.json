{"info": {"settings_imported": "Настройки успешно импортированы."}, "userFeedback": {"message_update_failed": "Не удалось обновить сообщение", "no_checkpoint_found": "Контрольная точка перед этим сообщением не найдена", "message_updated": "Сообщение успешно обновлено"}, "lowCreditWarning": {"title": "Предупреждение о низком балансе!", "message": "Проверьте, можете ли вы пополнить счет бесплатными кредитами или приобрести дополнительные!"}, "notLoggedInError": "Не удается выполнить запрос, убедитесь, что вы подключены и вошли в систему с выбранным провайдером.\n\n{{error}}", "rules": {"actions": {"delete": "Удалить", "confirmDelete": "Вы уверены, что хотите удалить {{filename}}?", "deleted": "{{filename}} удален"}, "errors": {"noWorkspaceFound": "Папка рабочего пространства не найдена", "fileAlreadyExists": "Файл {{filename}} уже существует", "failedToCreateRuleFile": "Не удалось создать файл правила.", "failedToDeleteRuleFile": "Не удалось удалить файл правила."}, "templates": {"workflow": {"description": "Описание рабочего процесса здесь...", "stepsHeader": "## <PERSON>аги", "step1": "Шаг 1", "step2": "Шаг 2"}, "rule": {"description": "Описание правила здесь...", "guidelinesHeader": "## Руководящие принципы", "guideline1": "Принцип 1", "guideline2": "Принцип 2"}}}, "commitMessage": {"activated": "Генератор сообщений о коммитах Kilo Code активирован", "gitNotFound": "⚠️ Git репозиторий не найден или git недоступен", "gitInitError": "⚠️ Ошибка инициализации Git: {{error}}", "generating": "Kilo: Генерируется сообщение коммита...", "noChanges": "Kilo: Не найдено изменений для анализа", "generated": "Kilo: Сообщение коммита сгенерировано!", "generationFailed": "Kilo: Не удалось создать сообщение коммита: {{errorMessage}}", "generatingFromUnstaged": "Kilo: Генерация сообщения с использованием неподготовленных изменений", "activationFailed": "Kilo: Не удалось активировать генератор сообщений: {{error}}", "providerRegistered": "Kilo: Поставщик сообщения коммита зарегистрирован"}, "autocomplete": {"statusBar": {"enabled": "$(sparkle) Kilo Автодополнение", "warning": "$(warning) Kilo Автодополнение", "disabled": "$(circle-slash) Kilo Автодополнение", "tooltip": {"sessionTotal": "Общая стоимость сессии:", "disabled": "Автодополнение Kilo Code (отключено)", "model": "Модель:", "tokenError": "Для использования автозаполнения должен быть установлен действительный токен", "lastCompletion": "Последнее завершение:", "basic": "Автодополнение Kilo Code"}, "cost": {"lessThanCent": "<$0,01", "zero": "0,00 ₽"}}, "toggleMessage": "<PERSON><PERSON> Автодополнение {{status}}"}, "ghost": {"progress": {"analyzing": "Анализ<PERSON><PERSON><PERSON><PERSON> ваш код...", "generating": "Создание предлагаемых правок...", "processing": "Обработка предлагаемых правок...", "showing": "Отображение предложенных правок...", "title": "Kilo Code"}, "input": {"title": "Kilo Code: Быстрая Задача", "placeholder": "напр., 'переработать эту функцию, чтобы сделать её более эффективной'"}, "commands": {"displaySuggestions": "Показать предлагаемые правки", "generateSuggestions": "Kilo Code: Создать Предлагаемые Изменения", "cancelSuggestions": "Отменить предложенные правки", "applyCurrentSuggestion": "Применить текущее предложенное изменение", "category": "Kilo Code", "applyAllSuggestions": "Применить все предложенные правки", "promptCodeSuggestion": "Быстрое задание"}, "codeAction": {"title": "Kilo Code: Предлагаемые правки"}, "chatParticipant": {"description": "Я могу помочь вам с быстрыми задачами и предложенными изменениями.", "fullName": "<PERSON>lo <PERSON> Агент", "name": "Агент"}, "messages": {"provideCodeSuggestions": "Предлагаю код..."}}}