{"answers": {"yes": "Ya", "no": "Tidak", "cancel": "<PERSON><PERSON>", "remove": "Hapus", "keep": "Simpan"}, "number_format": {"thousand_suffix": "rb", "million_suffix": "jt", "billion_suffix": "m"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>mi ingin mendengar feedback kamu atau membantu dengan masalah yang kamu alami.", "githubIssues": "Laporkan masalah di GitHub", "githubDiscussions": "Bergabung dengan diskusi GitHub", "discord": "Bergabung dengan komunitas Discord kami", "customerSupport": "Dukungan Pelanggan"}, "ui": {"search_placeholder": "Cari..."}, "mermaid": {"loading": "Membuat diagram mermaid...", "render_error": "Tidak Dapat Merender Diagram", "fixing_syntax": "Memperbaiki sintaks Mermaid...", "fix_syntax_button": "Perbaiki sintaks dengan AI", "original_code": "<PERSON><PERSON>:", "errors": {"unknown_syntax": "Error sintaks tidak dikenal", "fix_timeout": "Permintaan per<PERSON>ikan LLM habis waktu", "fix_failed": "Perbaikan LLM gagal", "fix_attempts": "Gagal memperbaiki sintaks setelah {{attempts}} percobaan. Error terakhir: {{error}}", "no_fix_provided": "LLM gagal member<PERSON><PERSON>n", "fix_request_failed": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>ikan gagal"}, "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON>", "save": "Simpan Gambar", "viewCode": "<PERSON><PERSON>", "viewDiagram": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "Kode Mermaid"}, "tabs": {"diagram": "Diagram", "code": "<PERSON><PERSON>"}, "feedback": {"imageCopied": "Gambar disalin ke clipboard", "copyError": "Error menyalin gambar"}}, "file": {"errors": {"invalidDataUri": "Format data URI tidak valid", "copyingImage": "Error menyalin gambar: {{error}}", "openingImage": "Error membuka gambar: {{error}}", "pathNotExists": "Path tidak ada: {{path}}", "couldNotOpen": "Tidak dapat membuka file: {{error}}", "couldNotOpenGeneric": "Tidak dapat membuka file!"}, "success": {"imageDataUriCopied": "Data URI gambar disalin ke clipboard"}}}